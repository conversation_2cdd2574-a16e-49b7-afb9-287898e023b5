# 开发测试环境示例

# 服务配置
HOST=0.0.0.0
PORT=7000

# 数据库配置信息
# 注意：可以使用 python scripts/encrypt_db_url.py 命令加密数据库连接字符串
# 加密后的格式：postgres://postgres:ENC:xxxxx@localhost:5432/hi_ideagen
DATABASE_URL=postgres://postgres:123456@localhost:5432/hi_ideagen

# 安全配置，用于生成JWT令牌和数据库连接字符串加密，请勿泄露
# 强烈建议在生产环境中修改此密钥，并确保其复杂度足够高（至少32个字符）
SECRET_KEY=your_secret_key_here

# 加密用的key
CRYPTO_BASE_KEY=

# 默认超级管理员管理员配置
DEFAULT_ADMIN_USERNAME=super_admin
DEFAULT_ADMIN_PASSWORD=strong_password_here
DEFAULT_SUPER_ADMIN_MOBILE=手机号码
# 默认机构的管理员配置
DEFAULT_ORG_ADMIN_MOBILE=手机号码

# 研究配置
# SerpAPI API Key，用于Google搜索
SERPAPI_API_KEY=your_serpapi_api_key
# Jina API Key，用于Jina搜索
JINA_API_KEY=your_jina_api_key

# PubMed配置
PUBMED_TOOL_NAME=College-Agent
PUBMED_EMAIL=

# 日志配置
# 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
# 日志目录，宿主机挂载目录，用绝对路径表示，跟上面的配置不一样，这个是Docker宿主机挂载目录
LOG_ROOT_DIR=/home/<USER>/co-mpanion-dev/college-files/logs
# 项目的日志文件具体输出的路径目录，本地开发用相对路径./logs，Docker用绝对路径/app/logs
LOG_DIR=./logs
# 日志保留天数，超过此天数的日志文件将被自动删除
LOG_RETENTION_DAYS=30
# 时区设置，Docker容器时区配置，默认东八区
TZ=Asia/Shanghai
# 体验用户允许看到的最大文字（报告和大纲都一样）
TRIAL_USER_MAX_TEXT=10000


# 搜索配置
SEARCH_RESULTS_LIMIT=10
# 研究迭代次数限制（搜索-分析-改进查询的最大循环次数，每次循环系统会根据已获取的信息生成新的搜索查询，直到达到信息充分或达到迭代上限）
RESEARCH_ITERATION_LIMIT=2
# 谷歌搜索相关上下文的时候允许的最大搜索次数
MAX_SEARCH_QUERIES=35
# 不开启文献总结的情况下，允许收集的最大contexts数
MAX_CONTEXTS=5
# 开启文献总结的情况下，允许收集的最大contexts数（contexts + literature的总数）
MAX_LITERATURE_AND_CONTEXTS=35
# 每次生成报告的最大搜索参考文献数
MAX_LITERATURES_COUNT=35

# 幻觉审查的google搜索条目数
MAX_HALLUCINATION_DETECTION_SEARCH=10

# 搜索引擎：google、google_scholar、jina
# 也可以是google,jina（注意前后左右都不可以有空格）
#（这表示先搜索google，如果google报错就会搜索jina）
# 如果搜索引擎里面有google和google_scholar就必须配置SERPAPI_API_KEY
# 如果搜索引擎里面有jina就必须配置JINA_API_KEY
# 涉及流程：1、正文生成过程中搜索有用的参考文献和背景信息；2、生成图片时搜索相关的数据网页
SEARCH_ENGINE=google  
#参考文献谷歌搜索引擎
LITERATURE_LIBRARY_SEARCH_ENGINE=google_scholar
# 生成图片数据时候的搜索引擎
# 这表示先搜索google，如果google报错就会搜索jina。
# 必须配置SERPAPI_API_KEY和JINA_API_KEY
PICTURE_DATA_SEARCH_ENGINE=google,jina
# Jina直接内容开关 ON/OFF ，ON开启后使用jina直接搜索内容，OFF关闭后使用jina搜索内容后，再使用google搜索内容
JINA_DIRECT_CONTENT=ON
######LLM参数
# 温度
LLM_DEFAULT_TEMPERATURE=1
# 上采样
LLM_DEFAULT_TOP_K=80
# 上采样
LLM_DEFAULT_TOP_P=0.6

# JSON Schema支持的模型列表（支持结构化输出的模型）
JSON_SCHEMA_SUPPORTED_MODELS=gpt-4o,gpt-4o-mini,gpt-4-turbo,google/gemini-2.5-flash

# JSON Schema模型校验开关（ON/OFF）
# 开启时使用JSON_SCHEMA_SUPPORTED_MODELS列表进行校验，关闭时所有模型都使用文本解析
ENABLE_JSON_SCHEMA_MODEL_CHECK=ON

# dify应用的api基础地址（不要以"/"结尾）
DIFY_API_URL=""
# dify应用的密钥
DIFY_API_KEY=""
# dify应用-AI对话-的密钥
DIFY_AI_CHAT_API_KEY=""
# dify应用-AI理科作业助手-的密钥
DIFY_AI_HOMEWORK_API_KEY=""

# JWT Token配置
ACCESS_TOKEN_EXPIRE_SECONDS=1209600  # 默认14天有效期 (14 * 24 * 60 * 60 = 1209600秒)


# 阿里云短信服务配置
# 阿里云AccessKey ID，用于短信服务认证
ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id_here
# 阿里云AccessKey Secret，用于短信服务认证
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret_here
# 短信签名名称，在阿里云控制台申请的签名
SMS_SIGN_NAME=你的短信签名
# 短信模板代码，在阿里云控制台申请的模板
SMS_TEMPLATE_CODE=SMS_123456789
# 验证码有效期（秒），默认5分钟
SMS_CODE_EXPIRE_TIME=300
# 同一手机号发送短信间隔（秒），防止频繁发送
SMS_SEND_INTERVAL=60

# 短信测试模式配置
# 是否启用测试模式（ON/OFF），启用后可使用固定验证码绕过阿里云短信服务
SMS_TEST_MODE=ON
# 测试用固定验证码，在测试模式下使用此验证码可直接通过验证
SMS_TEST_CODE=654321

# Redis配置
# Redis连接URL，包含主机、端口、数据库
REDIS_URL=redis://co-mpanion-redis-dev:6379/10
# Redis数据目录-Docker宿主机挂载目录
REDIS_DIR=/home/<USER>/co-mpanion-dev/redis

#论文字数要求
PAPER_WORD_COUNT=15000

# PPT生成配置
# PPT文档内容长度限制（字符数）
PPT_MAX_CONTENT_LENGTH=15000
# 图片生成的搜索关键词数量
FIGURE_SEARCH_KEYWORD_COUNT=1
# 图片生成所需数据的谷歌搜索条目数
FIGURE_GOOGLE_SEARCH_LIMIT=1
# 前端访问的基础路径
WEB_BASE_URL=http://localhost:8000
# 下载token过期时间（小时），默认24小时
DOWNLOAD_TOKEN_EXPIRE_HOURS=24

# OnlyOffice配置
# OnlyOffice回调基础URL，用于生成文档保存回调地址
ONLYOFFICE_CALLBACK_BASE_URL=

# SaaS登录配置
# 默认机构登录
SAAS_LOGIN_AS_DEFAULT_ORG=ON
SAAS_LOGIN_MAX_ALLOWED_COUNT=5
AES_ENCRYPT_KEY=
HMAC_KEY=
AES_SALT=
HMAC_SALT=

# 文件存储类型 local/oss 默认为local
FILE_MODE=

# Aliyun OSS Config
OSS_ACCESS_KEY_ID=
OSS_ACCESS_KEY_SECRET=
OSS_BUCKET=
OSS_ENDPOINT=
OSS_REGION=

# 多实例部署时候的应用的唯一标识符
APP_INSTANCE_ID=INSTANCE_1
# 大模型交互场景数据的有效时长（单位秒）默认12小时
LLM_INTERACTION_DATA_EXPIRE_TIME=43200
# 每次AI去痕的段落数
AI_TRACES_SAME_MOMENT_COUNT=3