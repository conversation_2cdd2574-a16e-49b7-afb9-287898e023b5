from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "saas_wallet_transaction" ALTER COLUMN "transaction_state" SET DEFAULT 'PRE_CHARGING';
        ALTER TABLE "saas_wallet_transaction" ALTER COLUMN "transaction_state" TYPE VARCHAR(25) USING "transaction_state"::VARCHAR(25);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "saas_wallet_transaction" ALTER COLUMN "transaction_state" SET DEFAULT 'PROCESSING';
        ALTER TABLE "saas_wallet_transaction" ALTER COLUMN "transaction_state" TYPE VARCHAR(10) USING "transaction_state"::VARCHAR(10);"""
