from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "co_verify" (
    "id" UUID NOT NULL PRIMARY KEY,
    "ai_report" TEXT,
    "status" VARCHAR(9) NOT NULL DEFAULT 'NO-START',
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "file_id" UUID REFERENCES "upload_files" ("id") ON DELETE CASCADE,
    "user_id" UUID REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "co_verify"."ai_report" IS 'AI报告内容';
COMMENT ON COLUMN "co_verify"."status" IS '验证状态';
COMMENT ON COLUMN "co_verify"."created_at" IS '创建时间';
COMMENT ON COLUMN "co_verify"."updated_at" IS '更新时间';
COMMENT ON COLUMN "co_verify"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "co_verify"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "co_verify"."file_id" IS '关联的上传文件';
COMMENT ON COLUMN "co_verify"."user_id" IS '关联的用户';
COMMENT ON TABLE "co_verify" IS 'co验证模型';
        ALTER TABLE "upload_files" ADD "user_id" UUID;
        ALTER TABLE "upload_files" ADD CONSTRAINT "fk_upload_f_users_43f8ac97" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "upload_files" DROP CONSTRAINT IF EXISTS "fk_upload_f_users_43f8ac97";
        ALTER TABLE "upload_files" DROP COLUMN "user_id";
        DROP TABLE IF EXISTS "co_verify";"""
