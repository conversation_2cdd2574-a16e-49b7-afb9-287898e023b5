from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "literatures" ADD "paper_type" VARCHAR(10) NULL;
        CREATE TABLE IF NOT EXISTS "saas_platforms" (
    "id" UUID NOT NULL PRIMARY KEY,
    "platform" VARCHAR(50) NOT NULL UNIQUE,
    "platform_name" VARCHAR(100) NOT NULL,
    "token_url" VARCHAR(512) NOT NULL,
    "userinfo_url" VARCHAR(512) NOT NULL,
    "client_id" VARCHAR(100) NOT NULL,
    "client_secret" VARCHAR(100) NOT NULL,
    "is_active" BOOL NOT NULL DEFAULT True,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "literatures"."paper_type" IS '文献类型标识';
COMMENT ON COLUMN "saas_platforms"."platform" IS '平台唯一标识，如 \"jiayi\"';
COMMENT ON COLUMN "saas_platforms"."platform_name" IS '平台展示名称';
COMMENT ON COLUMN "saas_platforms"."token_url" IS '获取token的完整URL';
COMMENT ON COLUMN "saas_platforms"."userinfo_url" IS '获取医生信息的URL';
COMMENT ON COLUMN "saas_platforms"."client_id" IS '客户端ID';
COMMENT ON COLUMN "saas_platforms"."client_secret" IS '客户端密钥';
COMMENT ON COLUMN "saas_platforms"."is_active" IS '是否启用';
COMMENT ON TABLE "saas_platforms" IS 'SaaS平台配置表';
        CREATE TABLE IF NOT EXISTS "saas_user_mappings" (
    "id" UUID NOT NULL PRIMARY KEY,
    "user_id" UUID NOT NULL,
    "external_id" VARCHAR(255) NOT NULL,
    "platform" VARCHAR(50) NOT NULL,
    "mobile" VARCHAR(50) NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "uid_saas_user_m_platfor_0736b2" UNIQUE ("platform", "external_id")
);
COMMENT ON COLUMN "saas_user_mappings"."user_id" IS '关联的用户ID';
COMMENT ON COLUMN "saas_user_mappings"."external_id" IS '第三方平台的用户ID';
COMMENT ON COLUMN "saas_user_mappings"."platform" IS '来源平台标识';
COMMENT ON COLUMN "saas_user_mappings"."mobile" IS '医生手机号，作为用户名';
COMMENT ON COLUMN "saas_user_mappings"."name" IS '用户姓名';
COMMENT ON TABLE "saas_user_mappings" IS '第三方用户与本系统用户的映射关系表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "literatures" DROP COLUMN "paper_type";
        DROP TABLE IF EXISTS "saas_user_mappings";
        DROP TABLE IF EXISTS "saas_platforms";"""
