from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "saas_platforms" ADD "charge_confirm_url" VARCHAR(512);
        ALTER TABLE "saas_platforms" ADD "pre_charge_url" VARCHAR(512);
        ALTER TABLE "saas_platforms" ADD "charge_cancel_url" VARCHAR(512);
        ALTER TABLE "saas_platforms" ADD "api_key" VARCHAR(512);
        CREATE TABLE IF NOT EXISTS "saas_wallet_transaction" (
    "id" UUID NOT NULL PRIMARY KEY,
    "user_id" VARCHAR(100) NOT NULL,
    "count" INT NOT NULL,
    "agent_id" VARCHAR(100) NOT NULL,
    "platform" VARCHAR(50) NOT NULL,
    "pre_charge_id" VARCHAR(200),
    "business_id" VARCHAR(200),
    "order_state" VARCHAR(50),
    "transaction_state" VARCHAR(10) NOT NULL DEFAULT 'PROCESSING',
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "saas_platforms"."charge_confirm_url" IS '计费确认接口的URL';
COMMENT ON COLUMN "saas_platforms"."pre_charge_url" IS '预计费接口的URL';
COMMENT ON COLUMN "saas_platforms"."charge_cancel_url" IS '计费取消接口的URL';
COMMENT ON COLUMN "saas_platforms"."api_key" IS '接口调用的APIKEY';
COMMENT ON COLUMN "saas_wallet_transaction"."user_id" IS '用户ID';
COMMENT ON COLUMN "saas_wallet_transaction"."count" IS '使用次数';
COMMENT ON COLUMN "saas_wallet_transaction"."agent_id" IS '智能体';
COMMENT ON COLUMN "saas_wallet_transaction"."platform" IS '第三方平台';
COMMENT ON COLUMN "saas_wallet_transaction"."pre_charge_id" IS '预扣费ID';
COMMENT ON COLUMN "saas_wallet_transaction"."business_id" IS '关联的业务主键';
COMMENT ON COLUMN "saas_wallet_transaction"."order_state" IS '订单状态,成功/失败';
COMMENT ON COLUMN "saas_wallet_transaction"."transaction_state" IS '交易状态';
COMMENT ON COLUMN "saas_wallet_transaction"."created_at" IS '创建时间';
COMMENT ON COLUMN "saas_wallet_transaction"."updated_at" IS '更新时间';
COMMENT ON TABLE "saas_wallet_transaction" IS 'SaaS交易订单表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "saas_platforms" DROP COLUMN "charge_confirm_url";
        ALTER TABLE "saas_platforms" DROP COLUMN "pre_charge_url";
        ALTER TABLE "saas_platforms" DROP COLUMN "charge_cancel_url";
        ALTER TABLE "saas_platforms" DROP COLUMN "api_key";
        DROP TABLE IF EXISTS "saas_wallet_transaction";"""
