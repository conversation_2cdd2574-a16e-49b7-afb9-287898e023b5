from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "system_configs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "key" VARCHAR(255) NOT NULL UNIQUE,
    "category" VARCHAR(100),
    "description" TEXT,
    "value" TEXT,
    "default_value" TEXT,
    "order_no" INT NOT NULL DEFAULT 0,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False
);
COMMENT ON COLUMN "system_configs"."key" IS '配置键';
COMMENT ON COLUMN "system_configs"."category" IS '配置分类';
COMMENT ON COLUMN "system_configs"."description" IS '配置描述';
COMMENT ON COLUMN "system_configs"."value" IS '配置值';
COMMENT ON COLUMN "system_configs"."default_value" IS '默认值';
COMMENT ON COLUMN "system_configs"."order_no" IS '排序号';
COMMENT ON COLUMN "system_configs"."created_at" IS '创建时间';
COMMENT ON COLUMN "system_configs"."updated_at" IS '修改时间';
COMMENT ON COLUMN "system_configs"."is_deleted" IS '是否删除';
COMMENT ON TABLE "system_configs" IS '系统配置表，用于存储系统级别的配置信息';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "system_configs";"""
