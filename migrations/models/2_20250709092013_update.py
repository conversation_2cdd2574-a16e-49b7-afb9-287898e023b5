from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "co_ai_ppts" (
    "id" UUID NOT NULL PRIMARY KEY,
    "original_filename" VARCHAR(255) NOT NULL,
    "original_file_path" VARCHAR(500) NOT NULL,
    "file_type" VARCHAR(50) NOT NULL,
    "ppt_file_path" VARCHAR(500) NOT NULL,
    "ppt_filename" VARCHAR(255) NOT NULL,
    "ppt_file_size" INT,
    "model_type" VARCHAR(50) NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'processing',
    "error_message" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "model_config_id" UUID REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "upload_file_id" UUID NOT NULL REFERENCES "upload_files" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "co_ai_ppts"."original_filename" IS '原始文档文件名';
COMMENT ON COLUMN "co_ai_ppts"."original_file_path" IS '原始文档文件路径';
COMMENT ON COLUMN "co_ai_ppts"."file_type" IS '原始文件类型';
COMMENT ON COLUMN "co_ai_ppts"."ppt_file_path" IS '生成的PPT文件路径';
COMMENT ON COLUMN "co_ai_ppts"."ppt_filename" IS 'PPT文件名';
COMMENT ON COLUMN "co_ai_ppts"."ppt_file_size" IS 'PPT文件大小(字节)';
COMMENT ON COLUMN "co_ai_ppts"."model_type" IS '使用的模型类型';
COMMENT ON COLUMN "co_ai_ppts"."status" IS '生成状态 (processing: 生成中, completed: 完成, failed: 失败)';
COMMENT ON COLUMN "co_ai_ppts"."error_message" IS '错误信息';
COMMENT ON COLUMN "co_ai_ppts"."created_at" IS '创建时间';
COMMENT ON COLUMN "co_ai_ppts"."updated_at" IS '更新时间';
COMMENT ON COLUMN "co_ai_ppts"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "co_ai_ppts"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "co_ai_ppts"."model_config_id" IS '使用的模型配置';
COMMENT ON COLUMN "co_ai_ppts"."upload_file_id" IS '关联的上传文件';
COMMENT ON COLUMN "co_ai_ppts"."user_id" IS '创建用户';
COMMENT ON TABLE "co_ai_ppts" IS 'PPT生成记录模型';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "co_ai_ppts";"""
