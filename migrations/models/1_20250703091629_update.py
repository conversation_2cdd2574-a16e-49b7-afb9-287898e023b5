from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "co_ai_traces" (
    "id" UUID NOT NULL PRIMARY KEY,
    "original_filename" VARCHAR(255) NOT NULL,
    "file_size" INT NOT NULL,
    "file_type" VARCHAR(50) NOT NULL,
    "char_count" INT NOT NULL,
    "processed_sections" INT NOT NULL,
    "original_content" TEXT NOT NULL,
    "processed_content" TEXT,
    "status" VARCHAR(20) NOT NULL DEFAULT 'processing',
    "error_message" TEXT,
    "processing_start_time" TIMESTAMPTZ,
    "processing_end_time" TIMESTAMPTZ,
    "tokens_consumed" INT DEFAULT 0,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "model_config_id" UUID REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "upload_file_id" UUID NOT NULL REFERENCES "upload_files" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "co_ai_traces"."original_filename" IS '原始文件名';
COMMENT ON COLUMN "co_ai_traces"."file_size" IS '文件大小(字节)';
COMMENT ON COLUMN "co_ai_traces"."file_type" IS '文件类型';
COMMENT ON COLUMN "co_ai_traces"."char_count" IS '字符数量';
COMMENT ON COLUMN "co_ai_traces"."processed_sections" IS '处理的段落数量';
COMMENT ON COLUMN "co_ai_traces"."original_content" IS '原始内容';
COMMENT ON COLUMN "co_ai_traces"."processed_content" IS '处理后的内容';
COMMENT ON COLUMN "co_ai_traces"."status" IS '处理状态 (processing: 处理中, completed: 完成, failed: 失败)';
COMMENT ON COLUMN "co_ai_traces"."error_message" IS '错误信息';
COMMENT ON COLUMN "co_ai_traces"."processing_start_time" IS '处理开始时间';
COMMENT ON COLUMN "co_ai_traces"."processing_end_time" IS '处理结束时间';
COMMENT ON COLUMN "co_ai_traces"."tokens_consumed" IS '消耗的tokens数量';
COMMENT ON COLUMN "co_ai_traces"."created_at" IS '创建时间';
COMMENT ON COLUMN "co_ai_traces"."updated_at" IS '更新时间';
COMMENT ON COLUMN "co_ai_traces"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "co_ai_traces"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "co_ai_traces"."model_config_id" IS '使用的模型配置';
COMMENT ON COLUMN "co_ai_traces"."upload_file_id" IS '关联的上传文件';
COMMENT ON COLUMN "co_ai_traces"."user_id" IS '创建用户';
COMMENT ON TABLE "co_ai_traces" IS 'AI去痕记录模型';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "co_ai_traces";"""
