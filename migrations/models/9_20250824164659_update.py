from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "organization_model_uses" ADD "is_thinking" BOOL DEFAULT False;
        ALTER TABLE "user_default_models" ADD "is_thinking" BOOL DEFAULT False;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "user_default_models" DROP COLUMN "is_thinking";
        ALTER TABLE "organization_model_uses" DROP COLUMN "is_thinking";"""
