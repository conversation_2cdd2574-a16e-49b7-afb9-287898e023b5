#!/usr/bin/env python
"""
数据库连接字符串加密解密测试脚本

用法:
    python scripts/test_db_crypto.py [--db-url "postgres://user:password@host:port/db"]
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入加密工具
from app.utils.db_crypto import encrypt_db_url, decrypt_db_url, is_encrypted_db_url
from app.core.logging import get_logger

logger = get_logger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="数据库连接字符串加密解密测试")
    parser.add_argument("--db-url", default="postgres://postgres:123456@localhost:5432/test_db", 
                        help="要测试的数据库连接URL")
    return parser.parse_args()

def mask_db_url(db_url):
    """隐藏数据库URL中的敏感信息"""
    import re
    if not db_url:
        return db_url
    # 使用正则表达式替换密码部分
    return re.sub(r'(://\w+:)([^@]+)(@)', r'\1******\3', db_url)

def extract_password_from_url(db_url):
    """从数据库URL中提取密码"""
    from urllib.parse import urlparse
    parsed_url = urlparse(db_url)
    if '@' in parsed_url.netloc:
        auth, _ = parsed_url.netloc.split('@', 1)
        if ':' in auth:
            _, password = auth.split(':', 1)
            return password
    return None

def main():
    """主函数"""
    args = parse_args()
    db_url = args.db_url
    
    print("="*50)
    print("数据库连接字符串加密解密测试")
    print("="*50)
    
    # 提取原始密码
    original_password = extract_password_from_url(db_url)
    if not original_password:
        print("❌ 无法从连接字符串中提取密码")
        return 1
    
    # 显示原始连接字符串（隐藏密码）
    print(f"原始连接字符串: {mask_db_url(db_url)}")
    print(f"原始密码: {original_password}")
    
    # 检查是否已经加密
    if is_encrypted_db_url(db_url):
        print("连接字符串已经加密")
        decrypted_url = decrypt_db_url(db_url)
        print(f"解密后的连接字符串: {mask_db_url(decrypted_url)}")
        return 0
    
    # 加密连接字符串
    print("\n执行加密...")
    encrypted_url = encrypt_db_url(db_url)
    print(f"加密后的连接字符串: {encrypted_url}")
    
    # 解密连接字符串
    print("\n执行解密...")
    decrypted_url = decrypt_db_url(encrypted_url)
    print(f"解密后的连接字符串: {mask_db_url(decrypted_url)}")
    
    # 提取解密后的密码
    decrypted_password = extract_password_from_url(decrypted_url)
    print(f"解密后的密码: {decrypted_password}")
    
    # 验证结果
    if decrypted_password == original_password:
        print("\n✅ 测试成功: 加密解密一致")
        return 0
    else:
        print("\n❌ 测试失败: 加密解密不一致")
        print(f"原始密码: {original_password}")
        print(f"解密后密码: {decrypted_password}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 