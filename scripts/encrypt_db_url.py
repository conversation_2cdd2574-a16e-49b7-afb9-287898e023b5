#!/usr/bin/env python
"""
数据库连接字符串加密工具

用法:
    python scripts/encrypt_db_url.py [--env-file .env] [--output-file .env]

选项:
    --env-file      指定环境变量文件路径，默认为 .env
    --output-file   指定输出文件路径，默认与输入文件相同
    --help          显示帮助信息
"""

import os
import sys
import re
from pathlib import Path
import argparse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入加密工具
from app.utils.db_crypto import encrypt_db_url, is_encrypted_db_url
from app.core.logging import get_logger

logger = get_logger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="数据库连接字符串加密工具")
    parser.add_argument("--env-file", default=".env", help="指定环境变量文件路径，默认为 .env")
    parser.add_argument("--output-file", help="指定输出文件路径，默认与输入文件相同")
    return parser.parse_args()

def encrypt_db_url_in_env_file(env_file, output_file=None):
    """在环境变量文件中加密数据库连接字符串"""
    if output_file is None:
        output_file = env_file
    
    # 检查文件是否存在
    env_path = Path(env_file)
    if not env_path.exists():
        logger.error(f"环境变量文件不存在: {env_file}")
        return False
    
    try:
        # 尝试不同的编码读取文件
        encodings = ['utf-8', 'utf-16', 'latin1', 'cp1252']
        env_content = None
        used_encoding = None
        
        for encoding in encodings:
            try:
                with open(env_path, 'r', encoding=encoding) as f:
                    env_content = f.read()
                logger.info(f"成功使用 {encoding} 编码读取文件")
                used_encoding = encoding
                break
            except UnicodeDecodeError:
                continue
        
        if env_content is None:
            logger.error("无法以任何支持的编码读取文件")
            return False
        
        # 打印文件内容用于调试
        logger.info(f"文件内容: {env_content}")
        
        # 查找DATABASE_URL行 - 使用更宽松的正则表达式
        db_url_pattern = r'(DATABASE_URL\s*=\s*)(.*?)(\s*$|\n)'
        match = re.search(db_url_pattern, env_content, re.MULTILINE)
        
        if not match:
            logger.error("在环境变量文件中未找到 DATABASE_URL 配置")
            # 尝试直接查找是否包含关键字
            if "DATABASE_URL" in env_content:
                logger.info("文件中包含 DATABASE_URL 关键字，但正则表达式未匹配")
                
                # 尝试直接解析文件内容
                lines = env_content.splitlines()
                for line in lines:
                    line = line.strip()
                    if line.startswith("DATABASE_URL="):
                        db_url = line[len("DATABASE_URL="):].strip()
                        logger.info(f"通过直接解析找到数据库连接字符串: {db_url}")
                        
                        # 检查是否已经加密
                        if is_encrypted_db_url(db_url):
                            logger.info("数据库连接字符串已经加密，无需再次加密")
                            return True
                        
                        # 加密数据库连接字符串
                        encrypted_db_url = encrypt_db_url(db_url)
                        logger.info(f"加密后的连接字符串: {encrypted_db_url}")
                        
                        # 替换原始连接字符串
                        new_line = f"DATABASE_URL={encrypted_db_url}"
                        new_env_content = env_content.replace(line, new_line)
                        
                        # 写入输出文件
                        with open(output_file, 'w', encoding=used_encoding) as f:
                            f.write(new_env_content)
                        
                        logger.info(f"数据库连接字符串已加密并保存到 {output_file}")
                        return True
            
            return False
        
        # 提取数据库连接字符串
        db_url = match.group(2).strip()
        logger.info(f"找到数据库连接字符串: {db_url}")
        
        # 检查是否已经加密
        if is_encrypted_db_url(db_url):
            logger.info("数据库连接字符串已经加密，无需再次加密")
            return True
        
        # 加密数据库连接字符串
        encrypted_db_url = encrypt_db_url(db_url)
        logger.info(f"加密后的连接字符串: {encrypted_db_url}")
        
        # 替换原始连接字符串
        new_env_content = re.sub(
            db_url_pattern,
            f'\\1{encrypted_db_url}\\3',
            env_content,
            flags=re.MULTILINE
        )
        
        # 写入输出文件
        with open(output_file, 'w', encoding=used_encoding) as f:
            f.write(new_env_content)
        
        logger.info(f"数据库连接字符串已加密并保存到 {output_file}")
        return True
    
    except Exception as e:
        logger.error(f"加密数据库连接字符串时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    args = parse_args()
    
    env_file = args.env_file
    output_file = args.output_file or env_file
    
    success = encrypt_db_url_in_env_file(env_file, output_file)
    
    if success:
        print(f"✅ 数据库连接字符串已成功加密并保存到 {output_file}")
        return 0
    else:
        print("❌ 加密数据库连接字符串失败，请查看日志了解详情")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 