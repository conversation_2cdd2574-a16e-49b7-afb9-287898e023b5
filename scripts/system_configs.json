{"version": "1.0.0", "description": "系统配置初始数据", "configs": [{"key": "defaultSystemSettingJsonMd5", "category": "system", "description": "系统配置JSON的MD5值，用于检测配置变化", "value": "", "defaultValue": "", "orderNo": 0}, {"key": "site_name", "category": "basic", "description": "网站名称", "value": "companion", "defaultValue": "companion", "orderNo": 1}, {"key": "umeng_siteId", "category": "umeng", "description": "友盟统计站点ID", "value": null, "defaultValue": "1281429022", "orderNo": 2}, {"key": "umeng_appKey", "category": "umeng", "description": "友盟统计应用Key", "value": null, "defaultValue": "68915bca6c255c77281984fd", "orderNo": 3}, {"key": "college_sub_title_ai_traces", "category": "college_sub_title", "description": "AI痕迹降低工具 - 副标题显示", "value": null, "defaultValue": "优化文本，降低AI书写痕迹。 ", "orderNo": 4}, {"key": "college_sub_title_ai_verify", "category": "college_sub_title", "description": "幻觉审查工具 - 副标题显示", "value": null, "defaultValue": "检测内容幻觉，确保信息准确。 ", "orderNo": 5}, {"key": "college_sub_title_ai_ppt", "category": "college_sub_title", "description": "AI PPT生成器 - 副标题显示", "value": null, "defaultValue": "上传文档，一键生成精美PPT。 ", "orderNo": 6}, {"key": "college_sub_title_ai_writing", "category": "college_sub_title", "description": "高效写作助手 - 副标题显示", "value": null, "defaultValue": "AI辅助写作，快速生成大纲正文。 ", "orderNo": 7}, {"key": "college_sub_title_ai_homework", "category": "college_sub_title", "description": "AI理科作业助手 - 副标题显示", "value": null, "defaultValue": "智能解答理科难题，提供详细步骤。 ", "orderNo": 8}, {"key": "college_sub_title_ai_chat", "category": "college_sub_title", "description": "AI智能对话 - 副标题显示", "value": null, "defaultValue": "你的全能AI伙伴，解答各类问题。 ", "orderNo": 9}, {"key": "JINA_DIRECT_CONTENT", "category": "JINA_SETTING", "description": "Jina直接内容开关 ON/OFF ，ON开启后使用jina直接搜索内容，OFF关闭后使用jina搜索内容后，再使用google搜索内容， SEARCH_ENGINE 中jina 必须存在并在最前面", "value": null, "defaultValue": "ON", "orderNo": 10}, {"key": "SEARCH_ENGINE", "category": "SEARCH_SETTING", "description": "搜索引擎配置，多个搜索引擎用逗号分隔，如jina,google  默认jina", "value": null, "defaultValue": "jina", "orderNo": 11}, {"key": "PICTURE_DATA_SEARCH_ENGINE", "category": "SEARCH_SETTING", "description": "图片数据搜索引擎配置，多个搜索引擎用逗号分隔，如jina,google 默认jina", "value": null, "defaultValue": "jina", "orderNo": 12}, {"key": "chat_session_ttl", "category": "chat_session", "description": "会话数据TTL（Redis存储过期时间，单位：秒）", "value": null, "defaultValue": "3600", "orderNo": 13}, {"key": "chat_session_timeout", "category": "chat_session", "description": "会话超时时间（业务逻辑超时，单位：秒）", "value": null, "defaultValue": "1800", "orderNo": 14}, {"key": "chat_max_messages_per_round", "category": "chat_session", "description": "每轮最大消息数（单轮对话消息限制）", "value": null, "defaultValue": "20", "orderNo": 15}, {"key": "history_record_filter_days", "category": "history", "description": "历史记录过滤天数（各模块历史记录查询的天数限制）", "value": null, "defaultValue": "30", "orderNo": 16}, {"key": "collage_ai_ppt_hint_history", "category": "hint", "description": "AI PPT生成器 - 提示信息（历史记录）", "value": null, "defaultValue": null, "orderNo": 17}, {"key": "aliyun_access_key_id", "category": "aliyun_setting", "description": "阿里云访问密钥ID", "value": null, "defaultValue": null, "orderNo": 18}, {"key": "aliyun_access_key_secret", "category": "aliyun_setting", "description": "阿里云访问密钥Secret", "value": null, "defaultValue": null, "orderNo": 19}, {"key": "aliyun_access_region", "category": "aliyun_setting", "description": "阿里云服务地域，支持的地域包括：cn-hangzhou（杭州）、cn-shanghai（上海）、cn-beijing（北京）、cn-shenzhen（深圳）、cn-cheng<PERSON>（成都）", "value": null, "defaultValue": "cn-shanghai", "orderNo": 20}, {"key": "aliyun_text_moderation_default_service", "category": "aliyun_text_moderation", "description": "默认文本审核服务类型，支持的服务类型包括：comment_detection_pro（公聊评论内容检测_专业版）、chat_detection_pro（私聊互动内容检测_专业版）、nickname_detection_pro（用户昵称检测_专业版）、comment_detection（公聊评论内容检测）、chat_detection（私聊互动内容检测）、nickname_detection（用户昵称检测）、ai_art_detection_pro（AI内容检测专业版）、ad_compliance_detection_pro（广告合规检测专业版）、pgc_detection_pro（PGC内容检测专业版）、video_detection_pro（视频检测专业版）、image_detection_pro（图片检测专业版）、audio_detection_pro（音频检测专业版）、document_detection_pro（文档检测专业版）", "value": null, "defaultValue": "comment_detection_pro", "orderNo": 21}, {"key": "aliyun_text_moderation_enabled", "category": "aliyun_text_moderation", "description": "阿里云文本审核服务开关（ON/OFF）", "value": null, "defaultValue": "OFF", "orderNo": 22}, {"key": "AGENT_EXPIRE_MINUTES_CHAT", "category": "agent_expire_minutes", "description": "智能聊天预计费过期时间（默认10分钟）", "value": null, "defaultValue": "10", "orderNo": 23}, {"key": "AGENT_EXPIRE_MINUTES_HOMEWORK", "category": "agent_expire_minutes", "description": "作业助手预计费过期时间（默认20分钟）", "value": null, "defaultValue": "20", "orderNo": 24}, {"key": "AGENT_EXPIRE_MINUTES_PAPER", "category": "agent_expire_minutes", "description": "高效写作预计费过期时间（默认90分钟）", "value": null, "defaultValue": "90", "orderNo": 25}, {"key": "AGENT_EXPIRE_MINUTES_PPT", "category": "agent_expire_minutes", "description": "PPT生成预计费过期时间（默认30分钟）", "value": null, "defaultValue": "30", "orderNo": 26}, {"key": "AGENT_EXPIRE_MINUTES_TRACES", "category": "agent_expire_minutes", "description": "降低AI预计费过期时间（默认60分钟）", "value": null, "defaultValue": "60", "orderNo": 27}, {"key": "AGENT_EXPIRE_MINUTES_HALLUCINATION", "category": "agent_expire_minutes", "description": "幻觉审查预计费过期时间（默认60分钟）", "value": null, "defaultValue": "60", "orderNo": 28}]}