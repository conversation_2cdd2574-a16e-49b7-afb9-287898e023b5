FROM registry.cn-shanghai.aliyuncs.com/chos/hi-ideagen-python3.11-base:6.0

# 设置pip镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

# 设置时区为东八区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录
WORKDIR /app

# 安装Pandoc
# RUN apt-get update && \
#     apt-get install -y pandoc && \
#     apt-get clean 

# 首先只复制依赖文件
COPY requirements.txt .

# 安装项目依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用程序文件
COPY . .

# 暴露端口
EXPOSE 7000

# 设置启动脚本
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]

# 启动应用
CMD ["python", "wsgi.py"]