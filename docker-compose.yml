version: '3.8'

services:
  api:
    image: co-mpanion-api-${ENV:-dev}:${VERSION:-latest}
    container_name: ${CONTAINER_NAME:-co-mpanion-api-${ENV:-dev}}
    restart: "no"
    working_dir: /app
    ports:
      - ${PORT}:${PORT}
    volumes:
      # 项目的大纲、正文文件
      - ${STATICS_PATH}/project:/app/llm_file
      # 图片文件
      - ${STATICS_PATH}/images:/app/images
      # 附件文件
      - ${STATICS_PATH}/attachments:/app/attachments
      # 生成的数据图图片
      - ${STATICS_PATH}/pictures:/app/pictures
      # 日志目录挂载
      - ${LOG_ROOT_DIR}:/app/logs
    env_file:
      - ${ENV_FILE}
    networks:
      - co-mpanion-api-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - LOG_LEVEL=DEBUG
      # 设置容器时区为东八区
      - TZ=Asia/Shanghai
    user: "${HOST_UID}:${HOST_GID}"
  redis:
    image: redis:6.2-alpine
    container_name: co-mpanion-redis-${ENV}
    ports:
      - "6378:6379"
    volumes:
      - ${REDIS_DIR}:/data
    command: redis-server --appendonly yes --bind 0.0.0.0
    restart: always
    networks:
      - co-mpanion-api-network
networks:
  co-mpanion-api-network:
    driver: bridge
