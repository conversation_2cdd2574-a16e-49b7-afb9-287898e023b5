#!/bin/sh

# sh deploy.sh 1.0.0 test /home/<USER>/co-mpanion-test/.env

# 检查参数数量
if [ $# -lt 3 ]; then
  echo "错误: 请提供版本号、环境、配置文件绝对地址"
  echo "用法: ./deploy.sh [版本号] [环境: dev|test|prod] [配置文件绝对地址: /home/<USER>/co-mpanion-dev/api/.env]"
  exit 1
fi

# 设置版本号、环境、配置文件
VERSION=$1
ENV=$2
ENV_FILE=$3

# 校验环境参数合法性
if [ "$ENV" != "dev" ] && [ "$ENV" != "test" ] && [ "$ENV" != "prod" ]; then
  echo "错误: 环境参数必须为 dev、test 或 prod"
  exit 1
fi

echo "开始启动 co-mpanion-api API 服务, 版本: $VERSION, 环境: $ENV"

# 检查镜像是否存在
if [ "$(docker images -q co-mpanion-api-$ENV:$VERSION 2> /dev/null)" = "" ]; then
  echo "错误: 镜像 co-mpanion-api-$ENV:$VERSION 不存在"
  exit 1
fi

# 检查 .env 文件是否存在
if [ ! -f "$ENV_FILE" ]; then
  echo "错误: 环境变量文件 $ENV_FILE 不存在"
  echo "请手动创建 $ENV_FILE"
  exit 1
fi

# 加载 .env 文件中的变量（包括 STATICS_PATH 等）
set -a
. "$ENV_FILE"
set +a

# 检查关键变量是否存在
if [ -z "$STATICS_PATH" ]; then
  echo "错误: .env 文件中没有定义 STATICS_PATH"
  exit 1
fi

if [ -z "$PORT" ]; then
  echo "错误: .env 文件中没有定义 PORT"
  exit 1
fi

# 获取宿主 UID/GID 并导出
HOST_UID=$(id -u)
HOST_GID=$(id -g)
export HOST_UID HOST_GID

# 检查并创建 STATICS_PATH 目录
if [ ! -d "$STATICS_PATH" ]; then
  echo "目录 $STATICS_PATH 不存在，正在创建..."
  mkdir -p "$STATICS_PATH"
fi

chmod 777 "$STATICS_PATH"
echo "已设置 $STATICS_PATH 的读写权限为 777"

# 导出变量供 docker-compose 使用
export VERSION
export CONTAINER_NAME=co-mpanion-${ENV}
export PORT
export ENV_FILE
export STATICS_PATH
export ENV

# 启动服务（仅重启 API，不动 Redis）
echo "部署服务: co-mpanion-api-$ENV:$VERSION，容器名: $CONTAINER_NAME，端口: $PORT..."

docker compose up -d --build --no-deps api

echo "✅ 服务启动完成!"
echo "📦 版本: $VERSION"
echo "🌐 环境: $ENV"
echo "📁 环境变量文件: $ENV_FILE"
echo "📂 静态资源路径: $STATICS_PATH"
