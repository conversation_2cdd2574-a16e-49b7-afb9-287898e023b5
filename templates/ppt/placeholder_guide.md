
# PPT模板占位符使用指南

## 占位符列表

### 封面页
- {{main_title}} - 主标题
- {{subtitle}} - 副标题
- {{date}} - 日期
- {{author}} - 作者
- {{school}} -学校
- {{guidance}} - 指导老师

### 目录页
- {{agenda_item_1}} ~ {{agenda_item_5}} - 目录项目1-5
- {{agenda_item_en_1}} ~ {{agenda_item_en_5}} - 目录项目英文1-5
- {{agenda_item_sub_1}} ~ {{agenda_item_sub_5}} - 目录项目子标题1-5


### 内容页（项目符号）
- {{slide_title}} - 幻灯片标题
- {{content_point_1}} ~ {{content_point_4}} - 内容要点1-4



### 总结页
- {{conclusion_point_1}} ~ {{conclusion_point_3}} - 总结要点1-3

### 感谢页
- {{contact_info}} - 联系信息

## 布局类型说明

### 项目符号布局
适用于列表形式的内容展示，支持最多4个要点。
- 系统会自动检测包含 `{{slide_title}}` 和 `{{content_point_*}}` 的页面
- 每个要点应控制在1-2行文字内，保证页面美观

### 两栏布局
适用于对比、分析类内容展示。
- 系统会自动检测包含 `{{slide_title_2}}`、`{{left_column_content}}`、`{{right_column_content}}` 的页面
- 左右栏内容可以包含多行文字，支持简单的项目符号格式
- 建议左栏放理论/概念，右栏放实例/应用

## 使用方法

1. 在PPT模板文件中设置对应的占位符
2. 程序会自动识别占位符类型并选择相应的处理逻辑
3. 内容数据结构需要匹配对应的布局类型
4. 系统会保留原有的文字格式（字体、颜色、大小等）

## 示例数据结构

### 基础数据结构
```python
content_data = {
    "main_title": "人工智能发展趋势",
    "subtitle": "技术革新与未来展望", 
    "date": "2025年1月",
    "author": "AI研究团队",
    "agenda": [
        "人工智能概述",
        "技术发展现状", 
        "应用场景分析",
        "未来发展趋势",
        "总结与展望"
    ],
    "conclusion": [
        "AI技术发展迅速，应用前景广阔",
        "需要关注技术伦理和安全问题",
        "持续创新是发展的关键动力"
    ],
    "contact_info": "感谢您的关注 | AI研究团队 | <EMAIL>"
}
```

### 内容页数据结构
```python
# 项目符号页面
bullet_slide = {
    "title": "人工智能核心技术",
    "points": [
        "机器学习：让机器具备学习能力",
        "深度学习：模拟人脑神经网络结构", 
        "自然语言处理：让机器理解人类语言",
        "计算机视觉：让机器具备视觉感知能力"
    ]
}

# 两栏布局页面
two_column_slide = {
    "title": "理论与实践对比",
    "left_column": "理论基础：\n• 深度学习理论体系\n• 神经网络数学模型\n• 优化算法原理\n• 统计学习理论",
    "right_column": "实践应用：\n• 图像识别系统\n• 语音识别助手\n• 智能推荐算法\n• 自动驾驶技术"
}

# 完整的内容页列表
content_data["content_slides"] = [bullet_slide, two_column_slide]
```

## 技术实现说明

### 自动布局识别
系统会根据占位符类型自动选择处理逻辑：
- 检测到 `{{slide_title}}`、`{{content_point_*}}` → 项目符号布局
- 检测到 `{{slide_title_2}}`、`{{left_column_content}}`、`{{right_column_content}}` → 两栏布局

### 格式保留机制
- 系统使用保留格式的文本替换方式
- 保持原有字体、颜色、大小、对齐方式等样式
- 支持富文本格式的占位符替换

### 动态内容处理
- 系统只使用模板中现有的页面，不创建新页面
- 如果内容页数量超过模板容量，多余内容将被丢弃
- 支持混合使用不同布局类型的内容页
