from typing import (
    Generic,
    TypeVar,
    Optional,
    AsyncGenerator,
    List,
    Union,
    Callable,
    Dict,
    Coroutine,
    Any
)
try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    magic = None
import tempfile
import pypandoc
from docx import Document
import docx
from docx.shared import RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
# 给请求增加唯一的requestID
import contextvars
import time
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from fastapi.responses import FileResponse
from starlette.background import BackgroundTask
from uuid import uuid4
from pydantic import BaseModel
from pydantic.generics import GenericModel
# 从独立模块导入上下文变量避免循环依赖
from app.core.context import request_id_var, request_start_var
import os
import json
# import asyncio
from fastapi import Query, UploadFile
from app.api.schemas.literatures import LiteratureResponse
from app.services.search_service import (
    fetch_webpage_text_async,
    perform_serpapi_search
)
from app.services.system_config_service import system_config_service
from enum import Enum
from app.utils.enum import ErrorType
from app.services.llm_service import call_llm
from app.core.config import settings
from app.services.prompts import (
    REFERENCE_VALID_SYSTEM_PROMPT,
    REFERENCE_VALID_USER_PROMPT,
    REFERENCE_RELATED_SYSTEM_PROMPT,
    REFERENCE_RELATED_USER_PROMPT
)
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from uuid import uuid4
import re
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.core.config import settings
import sys
import pdfplumber
from app.api.repository.upload_file import download_file, remove_local_file
import subprocess

logger = get_logger(__name__)

ALLOW_EXTENSION = ['.doc', '.docx', '.md', '.txt', '.pdf']
# 正文所有标题的纯文字最大匹配长度
PURE_TITLE_MAX_LENGTH=200


class Timer:
    def __init__(self):
        # 初始化时记录当前时间（秒级时间戳）
        self.start_time = time.time()
    def elapsed(self) -> float:
        """返回初始化到现在经过的秒数"""
        start = self.start_time
        # 变成当前时间
        self.start_time = time.time()
        return self.start_time - start
class SingleLiterature(BaseModel):
    reference: str
    url: str
class LiteratureTextAndOtherTitle(BaseModel):
    literature_text: str
    other_text: str
class ContentStatus(str, Enum):
    """内容状态"""
    ERROR = "error"  # 错误状态
    NORMAL = "normal"  # 正常文本
    HEART_BEAT = "heart_beat"
class CiteItem(BaseModel):
    origin: str
    nums: List[int]
    text: str
    remark: Optional[List[str]] = None
class SplitReference(BaseModel):
    text_before_citation_title: str
    citation_paragraph_without_title: str
    citation_title: str
    paragraph_after_citation: str
class Reference(BaseModel):
  index: int
  authors: str
  paper_type: Optional[str]
  title: str
  journal: Optional[str]
  year: str
  volume: Optional[str]
  issue: Optional[str]
  pages: Optional[str]
  full_text: str
  url: str
  remark: Optional[List[str]] = None

T = TypeVar("T")
# 正文中引用的正则匹配
CITATION_NUM_RE = r'(.+?)(?<!>)(\[(\d+(?:-\d+)?(?:,\s*\d+(?:-\d+)?)*)\])'
# 一条参考文献的正则匹配
REFERENCE_RE = r'(?=\[\d+\]).+?\[(M|J|D|C|N|R|S|P|EB/OL|OL)\].+?[\d{3,4}].*?'

class FAULT(str, Enum):
    FAKE_REFERENCE = "参考文献可能不存在"
    NOT_REFERENCED = "未在正文引用"
    OPINION_UNFIT="观点表述可能有误"
    TYPE_ERROR="参考文献引用格式不标准"
    NOT_IN_REFERENCE="参考文献缺失"
def fault_normal(index: int, data: FAULT):
    return f"&#91;{index}&#93;{data.value}"
class ResponseModel(GenericModel, Generic[T]):
  success: bool
  code: int
  data: Optional[T] = None
  error: Optional[str] = ""# from fastapi.responses import JSONResponse

class PageInfo(GenericModel, Generic[T]):
    """分页信息"""
    total: int
    page: int
    size: int
    items: List[T] = []

class ResponsePageModel(GenericModel, Generic[T]):
    """分页响应模型"""
    success: bool
    code: int
    data: PageInfo[T]
    error: Optional[str] = ""
class PageQuery(BaseModel):
  page: Optional[int] = Query(1, ge=1, description="页码")
  size: Optional[int] = Query(10, ge=1, description="每页大小")

## 参考文献的标题识别函数
def citation_head_match(text: str, no_prefix: bool = False):
    # 匹配格式：### 1.参考文献 或 ## 1、参考文献
    pattern = re.compile(
        # r'(\s*(?:\d+|[一二三四五六七八九十])?.*?[、\.]\**?参考文献\**?)' if no_prefix else r'(#*\s*(?:\d+\.)?.*?[、\.]\**?参考文献\**?)',
        r'(\s{0,4}(?:\d+|[一二三四五六七八九十])?\s{0,2}[、\.]\s{0,2}\*{0,2}参考文献\*{0,2})' if no_prefix else r'(#*\s{0,4}(?:\d+|[一二三四五六七八九十])?\s{0,2}[、\.]\s{0,2}\*{0,2}参考文献\*{0,2})',
        re.IGNORECASE
    )
    match = pattern.search(text)
    if not match:
        # 匹配格式：## 参考文献
        pattern1 = re.compile(
            r'(\s{0,2}\*{0,2}参考文献\*{0,2})' if no_prefix else r'(#+\s{0,2}\*{0,2}参考文献\*{0,2})',
            re.IGNORECASE
        )
        match = pattern1.search(text)
    if match:
        logger.info(f"参考文献的标题为：{match.group(1)}")
    else:
        logger.info(f"没有参考文献的标题")
    return match

def save_text_to_file(
    content: str,
    file_path: str,
    mode: Optional[str] = "w"
) -> str:
  """
  将字符串内容保存或追加到指定文件中
  
  Args:
      content: 要保存的文本内容
      file_path: 文件的相对路径
      
  Returns:
      保存的文件相对路径
  """
  # 获取绝对路径
  abs_path = os.path.join(os.getcwd(), file_path)
  
  # 确保目录存在
  directory = os.path.dirname(abs_path)
  if directory and not os.path.exists(directory):
    os.makedirs(directory)
    
  # 追加内容到文件（如果文件不存在则创建）
  with open(abs_path, mode, encoding="utf-8") as f:
    f.write(content)
    
  # 返回相对路径
  return file_path

## markdown格式的标题识别函数
def title_head_match(text: str):
    # 匹配格式：### 1.标题 或 ## 1、标题
    pattern = re.compile(
        rf'(#{{1,5}}\s{{0,2}}(?:\d+)?+\s{{0,2}}[、\.]\**.{{1,{PURE_TITLE_MAX_LENGTH}}}\**\s*[^\n\r]{1,3})',
        re.IGNORECASE
    )
    match = pattern.search(text)

    if not match:
        # 匹配格式：## 标题
        pattern1 = re.compile(
            rf'(#{{1,5}}\s{{0,2}}\**.{{1,{PURE_TITLE_MAX_LENGTH}}}\**[^\n\r]{{1,3}})',
            re.IGNORECASE
        )
        match = pattern1.search(text)
    return match
def send_data(is_success: bool, data: Optional[T], error: str = "") -> ResponseModel[T]: 
    result = ResponseModel(
        success=is_success,
        code=0 if is_success else 500,
        data=data if is_success else None,
        error=(data or error) if not is_success else ""
    )
    logger.info(f"请求响应数据：{str(dict(result))[:200]}")
    return result

async def reference_related(
    content: str,
    expression: str,
    api_key: str,
    api_url: str,
    model: str,
    is_thinking: Optional[bool] = False,
    complete_callback: Optional[Callable[[Dict[str, str]], None]] = None
) -> bool:
    messages = [
        {
            "role": "system",
            "content": REFERENCE_RELATED_SYSTEM_PROMPT
        },
        {
            "role": "user",
            "content": REFERENCE_RELATED_USER_PROMPT.format(
                content=content,
                expression=expression
            )
        }
    ]
    response = await call_llm(
        messages=messages,
        flag="网页的提取内容和引用文本进行比对",
        apiKey=api_key,
        apiUrl=api_url,
        model=model,
        is_thinking=is_thinking,
        complete_callback=complete_callback
    )
    return response == 'Yes'

async def reference_valid(
    data: Reference,
    api_key: str,
    api_url: str,
    model: str,
    is_thinking: Optional[bool] = False,
    limit: int = settings.MAX_HALLUCINATION_DETECTION_SEARCH,
    complete_callback: Optional[Callable[[Dict[str, str]], None]] = None,
    success_callback: Optional[Callable[[], Coroutine[Any, Any, None]]] = None
) -> str:
    response = "No"
    
    # 從系統配置中動態獲取搜索引擎配置
    from app.services.system_config_service import system_config_service
    
    # 檢查 JINA_DIRECT_CONTENT 配置
    jina_direct_content_config = await system_config_service.get_config("JINA_DIRECT_CONTENT")
    jina_direct_content_enabled = jina_direct_content_config and jina_direct_content_config.upper() == "ON"
    
    search_engine_config = await system_config_service.get_config("SEARCH_ENGINE")
    if not search_engine_config:
        search_engine_config = settings.SEARCH_ENGINE
    
    search_results = await perform_serpapi_search(
        query=data.full_text,
        limit=limit,
        search_engine=search_engine_config
    )
    search_results = search_results[:limit]
    
    if jina_direct_content_enabled and search_engine_config == "jina":
        # Jina 直接內容模式：直接使用搜索結果作為內容
        for content in search_results:
            try:
                if content:
                    # 调用LLM
                    messages = [
                        {
                            "role": "system",
                            "content": REFERENCE_VALID_SYSTEM_PROMPT
                        },
                        {
                            "role": "user",
                            "content": REFERENCE_VALID_USER_PROMPT.format(
                                text=content,
                                literature=data.full_text
                            )
                        }
                    ]
                    response = await call_llm(
                        messages=messages,
                        flag="提取网页内容和参考文献进行比对",
                        apiKey=api_key,
                        apiUrl=api_url,
                        model=model,
                        is_thinking=is_thinking,
                        complete_callback=complete_callback
                    )
                    if response and response != 'No':
                        break
            except Exception as e:
                logger.error(f"内容比对报错: {str(e)}")
    else:
        # URL 模式：需要抓取網頁內容
        for url in search_results:
            try:
                page_content = await fetch_webpage_text_async(url)
                if not page_content:
                    return response
                else:
                    # 调用LLM
                    messages = [
                        {
                            "role": "system",
                            "content": REFERENCE_VALID_SYSTEM_PROMPT
                        },
                        {
                            "role": "user",
                            "content": REFERENCE_VALID_USER_PROMPT.format(
                                text=page_content,
                                literature=data.full_text
                            )
                        }
                    ]
                    response = await call_llm(
                        messages=messages,
                        flag="提取网页内容和参考文献进行比对",
                        apiKey=api_key,
                        apiUrl=api_url,
                        model=model,
                        is_thinking=is_thinking,
                        complete_callback=complete_callback
                    )
                    if response and response != 'No':
                        break
            except Exception as e:
                logger.error(f"网页：{url}内容比对报错")
    
    return response
def send_page_data(
    is_success: bool,
    data: Optional[PageInfo[T]] = None,
    error: str = ""
) -> ResponsePageModel[T]:
    """
    构建分页响应数据
        
    Returns:
        ResponsePageModel: 分页响应数据
    """
    result = ResponsePageModel(
        success=is_success,
        code=0 if is_success else 500,
        data=data,
        error=error if not is_success else ""
    )
    logger.info(f"分页请求响应数据：{str(dict(result))[:200]}")
    return result

# 是不是 【### 文字标题】这种形式
def is_markdown_title(text: str) -> bool:
    pattern = r'^#+\s[^\n]+$'
    return re.match(pattern, text.strip()) is not None
# 将正文的引用角标改成小的 text形如[1]
# flag为caret 或 sup
def convert_citations_to_sup(text: str, flag:Optional[str] = "sup"):
    # 前端的文本编辑器不支持^所以替换为 <sup>[1]</sup> 形式
    if flag == 'sup':
        return re.sub(CITATION_NUM_RE, r'\1<sup>\2</sup>', text) 
    else:
        return re.sub(CITATION_NUM_RE, r'\1^\2^', text)
  
# 辅助函数：流式读取文件内容（SSE格式）
async def stream_file_content_sse(
    file_path: str
) -> AsyncGenerator[str, None]:
    """
    流式读取文件内容并以SSE格式返回
    
    Args:
        file_path: 文件的相对路径或绝对路径
        
    Yields:
        SSE格式的文件内容
    """
    # 处理文件路径
    if os.path.isabs(file_path):
        abs_file_path = file_path
    else:
        abs_file_path = os.path.join(os.getcwd(), file_path)
    
    # 检查文件是否存在
    if not os.path.exists(abs_file_path):
        yield f"data: {json.dumps({'error': f'{ErrorType.FILE_NOT_EXIST.value}: {file_path}'})}\n\n"
        return
    try:
        with open(abs_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            content = ''.join(lines)
            content = content.strip()
            chunk = stream_handle_before_send(content)
            if chunk:
                yield f"data: {json.dumps({'content': chunk, 'status': ContentStatus.NORMAL.value})}\n\n"

        # 发送完成事件
        yield f"data: {json.dumps({'status': 'completed'})}\n\n"
    
    except Exception as e:
        yield f"data: {json.dumps({'error': f'读取文件时出错: {str(e)}'})}\n\n"
# 将参考文献的形如 ****.http://和****.DOI:的格式改成markdown的链接形式
def format_to_markdown_link(text: str) -> str:
    try:
        result = split_by_citation_section(text)
        if not result:
            return text
        literature_section = result.citation_paragraph_without_title
        # 现将参考文献一条一条拆出来
        segments = re.split(r'\n\s*(?=\[\d+\]|\d+\.)', literature_section.strip())
        format_list: List[str] = []
        for i, item in enumerate(segments):
            pure_text = item.strip()
            doi_match = re.search(r'^(.*?)DOI:\s*([^\s]+)', pure_text)
            url_match = re.search(r'(^\[\d+\]\s.+?)url:\s{0,2}(http[s]?://[^\s]+)$', pure_text)
            if doi_match:
                label = doi_match.group(1).strip()
                doi_part = doi_match.group(2).strip()
                url=f"https://doi.org/{doi_part}"
                full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', label)
                format_list.append(f"[{i + 1}] [{full_text}]({url})")
            elif url_match:
                label = url_match.group(1).strip()
                url = url_match.group(2).strip()
                full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', label)
                format_list.append(f"[{i + 1}] [{full_text}]({url})")
            else:
                full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', pure_text)
                format_list.append(f"[{i + 1}] {full_text}")
        # 这里使用两个换行符的目的是为了前端的md编辑器可以把每一条文献识别为一个段落。
        reference = ("\n\n").join(format_list)
        res = [
            result.text_before_citation_title,
            result.citation_title,
            reference
        ]
        if result.paragraph_after_citation:
            res.append(result.paragraph_after_citation)
        # 这里使用两个换行符的目的是为了每一部份都被识别为一个段落。
        return ("\n\n").join(res)  
    except Exception as e:
        logger.error(f"整理参考文献成markdown链接格式时发生错误：{str(e)}")
        return text
            
# 将markdown的链接形式的文档进行提取和以DOI:结尾的文献进行提取
def normal_single_literature(text: str) -> SingleLiterature:
    pattern = r'(\[\d+\])\s*\[(.*?)\]\((.*?)(?:\s+".*?")?\)'
    match = re.search(pattern, text)
    doi_match = re.search(r'^(.*?)DOI:\s*([^\s]+)', text)
    label = text
    url = ""
    if match:
        num = match.group(1).strip()
        reference = match.group(2).strip()
        label = f"{num} {reference}"
        url = match.group(3).strip()
    elif doi_match:
        label = doi_match.group(1).strip()
        doi_part = doi_match.group(2).strip()
        url=f"https://doi.org/{doi_part}"
    return SingleLiterature(
        reference=label,
        url=url
    )
def extract_reference_item_list(text: str):
    segments = re.split(r'\n\s*(?=\[\d+\]|\d+\.)', text.strip())
    result_list: List[Reference] = []
    for i, item in enumerate(segments):
        temp = normal_single_literature(item)
        seg = temp.reference
        pattern = re.compile(r"""
            ^\s*(?:\[(?P<index_bracket>\d+)\]|\s*(?P<index>\d+)\.)\s+   # [1] 或 1.
            (?P<authors>.+?)\.\s+                                 # 作者部分（第一个完整句号）
            (?P<title>.+?)
            \[(?P<paper_type>M|J|D|C|N|R|S|P|EB|OL|EB/OL)\]\.\s+                                      # 标题部分（第一个完整句号）
            (?:(?P<journal>.+?)[\.,]\s+)?                                    # 期刊名（第一个完整句号）
            (?P<year>\d{4})[,]{0,1}\s*                                            # 年份
            [;]{0,1}\s*(?:(?P<volume>\d+))?  #可选卷号                                    # 卷号
            # (?:\((?P<issue>S{0,1}.{0,5}\s{0,5}.{0,5}\d{0,8})\))?   # 可选期号
            (?:\((?P<issue>[^\)]+)\))?
            :{0,1}\s*
            (?:(?P<pages>[A-Za-z\d\-–—\.e]+))?  #可选页码 
            \.\s*
            $                                          # 结尾句号
        """, re.VERBOSE)
        # seg不能用full_text替换，因为full_text没有前面的序号了
        match = pattern.match(seg)
        full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', seg)
        # 去掉前后的序号、和前后的换行符
        if match:
            groups = match.groupdict()
            index = groups.get("index") or groups.get("index_bracket")
            result = Reference(**{
                "index": int(index),
                "authors": groups["authors"],
                "title": groups["title"],
                "journal": groups["journal"],
                "year": groups["year"],
                "volume": groups["volume"] if groups["volume"] else None,
                "issue": groups["issue"] if groups["issue"] else None,
                "pages": groups["pages"],
                "full_text": full_text,
                "url": temp.url,
                "paper_type": groups.get("paper_type")
            })
            result_list.append(result)
        else:
            logger.info(seg)
            result = Reference(**{
                "index": i + 1,
                "authors": "",
                "title": "",
                "journal": "",
                "year": "",
                "paper_type": None,
                "volume": None,
                "issue": None,
                "pages": "",
                "full_text": full_text,
                "url": temp.url,
                "remark": [FAULT.TYPE_ERROR.value]
            })
            result_list.append(result)
    return result_list

def demote_headings(text: str):
    # 判断是否存在一级标题
    has_h1 = any(re.match(r'^#\s', line) for line in text.split('\n'))
    
    if not has_h1:
        return text  # 如果没有一级标题，直接返回原文
    
    # 将所有标题降一级（增加一个#）
    lines = []
    for line in text.split('\n'):
        if re.match(r'^#+\s', line):
            line = '#' + line  # 增加一个#
        lines.append(line)
    
    return '\n\n'.join(lines)
# 去除正文的标题标题
def remove_markdown_h1_and_text(
    all_text: str,
    title: str,
    eng_title: Optional[str] = None,
    combine_title: Optional[bool] = True
) -> str:
    """
    在文本开头指定长度内，移除包含title的整个段落
    
    Args:
        all_text: 原始文本
        title: 要查找和移除的标题文本
        combine_title: 是否在结果前拼接标题
        
    Returns:
        处理后的文本
    """
    if not title:
        return all_text
    
    # 截取开头指定长度的文本进行处理
    split_index = len(title) * 2 + 20
    front_text = all_text[:split_index]
    logger.info(f"截取的内容为：{front_text}")
    remaining_text = all_text[split_index:]
    
    # 按段落分割（支持多种换行符组合）
    paragraphs = re.split(r'\n+', front_text)
    
    # 过滤掉包含title的段落
    filtered_paragraphs = []
    
    for paragraph in paragraphs:
        if title in paragraph:
            continue
        filtered_paragraphs.append(paragraph)
    
    # 重新组合过滤后的段落
    filtered_front_text = '\n\n'.join(filtered_paragraphs)
    # 拼接处理后的前半部分和原始的后半部分
    # 因为有的时候返回的内容段落标题会是一级标题。所以改一下
    without_title = demote_headings(filtered_front_text + remaining_text)
    

    def get_first_line(text: str) -> str:
        """返回文本中的第一行"""
        return text.strip().splitlines()[0] if text.strip() else ''
    if eng_title:    
        eng_title = get_first_line(eng_title)
    if combine_title:
        return f"# {title}\n\n# {eng_title}\n\n{without_title}" if eng_title else f"# {title}\n\n{without_title}"
    else:
        return without_title

def read_file_content(file_path: str) -> str:
    """
    读取文件内容并返回
    
    Args:
        file_path: 文件的相对路径或者绝对路径
        
    Returns:
        str: 文件的内容
        
    Raises:
        FileNotFoundError: 当文件不存在时
        IOError: 当读取文件出错时
    """
    logger.info(f"文件的内容: {file_path}")
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    # 检查文件是否存在
    if not os.path.exists(abs_path):
      error_msg = f"{ErrorType.FILE_NOT_EXIST.value}: {file_path}"
      logger.error(error_msg)
      raise Exception(error_msg)
    
    try:
        if abs_path.endswith(".txt") or abs_path.endswith(".md"):
            # 读取文件内容
            with open(abs_path, "r", encoding="utf-8") as f:
                content = f.read()
            result = content.strip()
            return result
        elif abs_path.endswith(".docx"):
            return docx_file_to_markdown(abs_path)
        elif abs_path.endswith(".doc"):
            logger.info(f"开始转换 .doc 为 .docx")
            # 先转换 .doc 为 .docx，然后处理
            from app.utils.document_parser import convert_doc_to_docx_with_libreoffice
            converted_docx_path = convert_doc_to_docx_with_libreoffice(abs_path)
            try:
                result = docx_file_to_markdown(converted_docx_path)
                return result
            finally:
                # 清理临时转换的文件
                if os.path.exists(converted_docx_path):
                    os.unlink(converted_docx_path)
        elif abs_path.endswith(".pdf"):
            return extract_text_from_pdf(abs_path)
        raise Exception("使用了不支持的文件格式")
    except IOError as e:
        raise Exception(f"读取文件时出错: {str(e)}")

def extract_text_from_pdf(file_path: str) -> str:
    logger.info("从PDF文件提取文本")
    """从PDF文件提取文本"""
    if pdfplumber is None:
        raise Exception("pdfplumber库未安装，无法处理PDF文件")
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"PDF文件不存在: {file_path}")
    
    text = ""
    try:
        with pdfplumber.open(file_path) as file:
            for page in file.pages:
                page_text = page.extract_text()
                logger.info(f"PDF段落文字: {page_text}")
                if is_possibly_garbled(page_text):
                    raise Exception("此PDF文档无法正确识别")
                if page_text:
                    text += page_text + "\n\n"
    except Exception as e:
        raise Exception(f"PDF文件解析失败: {str(e)}")
    
    return text.strip()
# 将markdown文档按照标题加正文或者标题的的组合切分
def split_markdown_text_by_paragraph(md_text: str) -> List[str]:
    pattern = r'^(#+\s+.*)$'
    lines = md_text.splitlines()
    sections = []
    current_section = []

    for line in lines:
        if re.match(pattern, line):
            # 遇到新标题时，保存当前段落
            if current_section:
                sections.append('\n\n'.join(current_section).strip())
            current_section = [line]  # 新段落从标题开始
        else:
            current_section.append(line)

    # 保存最后一个段落
    if current_section:
        sections.append('\n\n'.join(current_section).strip())

    return sections

def extract_section_after_citation(literature_text: str):
    try:
        match = title_head_match(literature_text)
        if match:
            split_text = match.group(1).strip()
            start, end = match.span()

            pure_literature = literature_text[:start].strip()
            other_text = f"{split_text}\n{literature_text[end:].strip()}"
            return LiteratureTextAndOtherTitle(
                literature_text=pure_literature,
                other_text=other_text
            )
        else:
            return LiteratureTextAndOtherTitle(
                literature_text=literature_text,
                other_text=''
            )
    except Exception as e:
        msg = f"从参考文献后续文本提取段落失败：str{e}"
        logger.error(msg)
        raise ValueError(msg)

def split_by_citation_section(text: str) -> SplitReference|None:
    """
    根据参考文献的标题将文本分割，并保留分隔符。
    返回一个对象。
    """
    logger.info("准备执行split_by_citation_section函数")
    match = citation_head_match(text)
    logger.info("citation_head_match函数执行完毕了")
    if not match:
        return None

    split_text = match.group(1).strip()
    start, end = match.span()
    right = text[end:].strip()
    logger.info("准备执行extract_section_after_citation函数")
    extract_data = extract_section_after_citation(right)
    logger.info("extract_section_after_citation函数执行完毕")
    logger.info(f"参考文献的标题是: {split_text}")
    return SplitReference(**{
        # 参考文献标题前面的文本
        'text_before_citation_title': text[:start].strip(),
        # 参考文献内容段落
        'citation_paragraph_without_title': extract_data.literature_text,
        # 参考文献那几个标题文字
        'citation_title': split_text,
        # 参考文献段落后面的段落
        'paragraph_after_citation': extract_data.other_text
    })
# 将数学公式转换为占位符，因为数学公式可能包含分段的分隔符，导致数学公式被分为两个段落里面。
def extract_math_blocks(text: str):
    # 支持匹配 $$...$$ 或 $$$$...$$$$，非贪婪，保留包裹的符号
    pattern = re.compile(r'(\${1,2})(.+?)\1', re.DOTALL)

    formulas = {}
    index = 0

    def replacer(match):
        nonlocal index
        full_expr = match.group(0)  # 包含 $$ 或 $$$$
        logger.info(f"匹配到的公式{full_expr}")
        key = f"MATH_{index}"
        formulas[key] = full_expr
        placeholder = f"{{{key}}}"
        index += 1
        return placeholder

    replaced_text = pattern.sub(replacer, text)

    logger.info(f"修改后的文章: {replaced_text}")
    return replaced_text, formulas

# text是一段文本，data是包含text里面占位key的字典数据，当然可能也包含不存在text中的key
def safe_replace(text: str, data: dict[str, str]):
    def replacer(match):
        key = match.group(1)
        return str(data.get(key, match.group(0)))
    
    return re.sub(r'\{(\w+)\}', replacer, text)

def split_text_by_sentences(text: str, regexp=r'(。|\n|\?|\||!|？|！)'):
    replaced_text, formulas = extract_math_blocks(text)

    parts = re.split(regexp, replaced_text)
    sentences = []

    # 避免最后落单的部分
    i = 0
    while i < len(parts) - 1:
        left = parts[i].strip()
        right = parts[i + 1].strip()
        if left or right:
            split_text = left + right
            split_text = safe_replace(split_text, formulas)
            sentences.append(split_text)
        i += 2

    # 如果还有剩余的无标点部分（可能是最后落单）
    if i < len(parts):
        last_part = parts[i].strip()
        if last_part:
            split_text = last_part
            split_text = safe_replace(split_text, formulas)
            sentences.append(last_part)
    
    return sentences

def extract_citations_in_paper_by_sentence(text: str) -> List[CiteItem]:
    sentences = split_text_by_sentences(text)
    full_sentences: List[CiteItem] = []
    for sentence in sentences:
        cite_pattern = re.compile(r'(?P<origin>.+?)(?P<cite>\[(\d+(?:-\d+)?(?:,\s*\d+(?:-\d+)?)*)\])')
        match = cite_pattern.match(sentence)
        if match:
            dict_data = match.groupdict()
            num: List[int] = []
            cite = re.sub(r'^\[(.*)\]$', r'\1', dict_data["cite"])
            if ',' in cite:
                num = [int(item) for item in re.split(r',\s*', cite)]
            elif '-' in cite:
                start, end = map(int, cite.split('-'))
                num.extend(range(start, end + 1))
            else:
                num.append(int(cite))
            text = dict_data["origin"]
            sentence = CiteItem(**{
                "origin": sentence,
                "nums": num,
                "text": text
            })
            full_sentences.append(sentence)
    return full_sentences
# 如果markdown的两个正文段落用\n连接则改为\n\n
# def change_markdown_paragraph_join_way(text: str):
#     return re.sub(
#         r'(?<![#>\-\*\d\.|`])([^\n\S]*\S[^\n]*?)\n(?=[^\n\S]*\S[^\n]*?(?![#>\-\*\d\.|`]))',
#         r'\1\n\n',
#         text
#     )
def is_ordered_list_item(line: str) -> bool:
    """
    检测是否为markdown有序列表项
    
    Args:
        line: 要检测的行
        
    Returns:
        bool: 如果是有序列表项返回True，否则返回False
    """
    stripped_line = line.lstrip()
    if not stripped_line:
        return False
    
    # 匹配有序列表格式：数字 + 点号或右括号 + 空格
    # 例如：1. 项目、2) 项目、10. 项目等
    pattern = r'^\d+[.)](\s|$)'
    return bool(re.match(pattern, stripped_line))

def adjust_markdown_heading_levels(
    text: str,
    level: int
) -> str:
    """
    将Markdown文本中的所有标题调整为指定的等级。

    Args:
        text (str): 原始Markdown文本
        level (int): 指定的标题等级（1-6）

    Returns:
        str: 标题等级统一后的Markdown文本
    """
    if not 1 <= level <= 6:
        return text

    target_hashes = '#' * level
    heading_pattern = re.compile(r'^(\s*)(#{1,6})(\s+)(.*)$', re.MULTILINE)

    def replace_heading(match):
        indent, _, _, content = match.groups()
        return f"{indent}{target_hashes} {content.strip()}"

    return heading_pattern.sub(replace_heading, text)

def change_markdown_paragraph_join_way(text: str):
    lines = text.splitlines()
    logger.info(lines)
    result = []
    skip_join = {'#', '-', '*', '>', '|', '`'}
    for i in range(len(lines)):
        line = lines[i]
        result.append(line)
        # 尝试插入额外空行的条件：
        # 这是加粗的文本。不是单个*
        if line.strip().startswith("**"):
            result.append("")
        elif (is_ordered_list_item(line) and i !=(len(lines) -1) and is_ordered_list_item(lines[i+1])):
            continue
        elif (
            i < len(lines) - 1
            and line.strip()
            and lines[i + 1].strip()
            and line.lstrip()[0] in skip_join
        ):
            continue
        else:
            result.append('')  # 插入空行
    return '\n'.join(result)

def insert_before_last_char(s: str, insert_text: str) -> str:
    if len(s) < 1:
        return ''
    return s[:-1] + insert_text + s[-1]

class RequestIDMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 生成 request_id (支持Spring Boot风格的ID生成)
        request_id = request.headers.get("X-Request-ID")
        if not request_id:
            # 类似Spring Boot的算法：时间戳 + 随机数
            timestamp = int((time.time() * 1000 - 1489111610226))
            random_part = int(time.time() * 1000000) % 1024
            request_id = f"{timestamp:x}{random_part:03x}"
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 设置上下文变量（关键！用于日志追踪）
        request_id_var.set(request_id)
        request_start_var.set(start_time)
        
        # 保持现有兼容性
        request.state.request_id = request_id
        
        try:
            # 调用后续逻辑
            response = await call_next(request)
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            return response
        finally:
            # 清理上下文变量
            request_id_var.set('')
            request_start_var.set(0.0)
def handle_text_before_use(
    text: str,
    fn_list: Optional[List[str]] = [
        "remove_dash_lines",
        "bracket_latex_to_dollar",
        "format_to_markdown_link",
        "change_markdown_paragraph_join_way"
    ]
):
    """
    文本预处理函数
    
    Args:
        text: 原始文本
        fn_list: 要执行的处理函数列表
        heading_adjust_config: 标题级别调整配置，如 {"level": 2, "strategy": "promote_to_next"}
    """
    pipeline = []
    if "remove_dash_lines" in fn_list:
        pipeline.append(remove_dash_lines)
    if "bracket_latex_to_dollar" in fn_list:
        pipeline.append(bracket_latex_to_dollar)
    if "format_to_markdown_link" in fn_list:
        pipeline.append(format_to_markdown_link)
    if "change_markdown_paragraph_join_way" in fn_list:
        pipeline.append(change_markdown_paragraph_join_way)
    if "fix_math_formula_format" in fn_list:
        pipeline.append(fix_math_formula_format)
    for fn in pipeline:
        text = fn(text)
    return text
def remove_markdown_code_fence(text: str) -> str:
    """
    去除 Markdown 中语言标记为 markdown 的代码块语法（```markdown ... ```），保留代码内容。
    
    Args:
        text (str): 原始 Markdown 文本
        
    Returns:
        str: 去除 markdown 代码块语法后的文本
    """
    # 匹配形如 ```markdown\n...内容...\n``` 的代码块，只去掉包裹的三引号部分
    pattern = re.compile(r'^```markdown\s*\n([\s\S]*?)^```\s*$', re.MULTILINE)
    return pattern.sub(r'\1', text)

def stream_handle_before_send(text: str):
    pipeline = [convert_citations_to_sup]
    for fn in pipeline:
        text = fn(text)
    return text
# 将文本转换成可以作为文件名称的文本
def sanitize_filename(filename, replace_with="_"):
    """
    替换文件名中的非法字符
    :param filename: 原始文件名（不含路径）
    :param replace_with: 替换非法字符的字符（默认 "_"）
    :return: 安全的文件名
    """
    # 定义非法字符的正则表达式（包括空格、控制字符、Windows/Unix非法字符）
    illegal_chars = r'[<>:"/\\|?*\x00-\x1f]'  # \x00-\x1f 是控制字符
    # 替换非法字符
    safe_name = re.sub(illegal_chars, replace_with, filename)
    # 去除首尾空格和点（避免 ".filename" 或 "filename."）
    safe_name = safe_name.strip().strip('.')
    # 合并连续替换字符（如多个 "_" 变成单个 "_"）
    safe_name = re.sub(r'_{2,}', '_', safe_name)
    # Windows 保留名称检查（可选）
    windows_reserved = [
        'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
        'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
        'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    ]
    if safe_name.upper() in windows_reserved:
        safe_name = f"_{safe_name}"
    return safe_name
# 获取段落的序号属性
def get_num_props(paragraph:any):
    """
    获取段落的编号属性 (numId, ilvl)，如果存在
    """
    pPr = paragraph._p.pPr
    if pPr is not None and pPr.numPr is not None:
        numId = pPr.numPr.numId.val
        ilvl = pPr.numPr.ilvl.val
        return numId, ilvl
    return None, None
# 给段落设置编号属性
def set_num_props(paragraph, numId, ilvl):
    """
    给段落设置编号属性 (numId, ilvl)
    """
    p = paragraph._p

    # 确保 <w:pPr> 存在
    if p.pPr is None:
        pPr = docx.oxml.OxmlElement('w:pPr')
        p.insert(0, pPr)
    else:
        pPr = p.pPr

    # 构建 <w:numPr>
    numPr = docx.oxml.OxmlElement('w:numPr')

    ilvl_el = docx.oxml.OxmlElement('w:ilvl')
    ilvl_el.set(docx.oxml.ns.qn('w:val'), str(ilvl))
    numId_el = docx.oxml.OxmlElement('w:numId')
    numId_el.set(docx.oxml.ns.qn('w:val'), str(numId))

    numPr.append(ilvl_el)
    numPr.append(numId_el)

    pPr.append(numPr)
def draw_underline(doc_run, color="FF0000"):
    """
    给 doc_run 添加红色下划线，不影响已有格式（如加粗）
    """
    rPr = doc_run._element.rPr
    u = docx.oxml.OxmlElement('w:u')
    # u.set(docx.oxml.ns.qn('w:val'), 'single')  # 单实线下划线
    u.set(docx.oxml.ns.qn('w:color'), color)  # 设置下划线颜色为红色
    rPr.append(u)

def set_hyperlink(
    paragraph: any,
    url: str,
    text: str,
    is_error: Optional[bool] = False
):
    try:
        part = paragraph.part
        r_id = part.relate_to(url, reltype="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink", is_external=True)

        # 创建 <w:hyperlink> 元素
        hyperlink = docx.oxml.OxmlElement('w:hyperlink')
        hyperlink.set(docx.oxml.ns.qn('r:id'), r_id)

        # 创建 run
        new_run = docx.oxml.OxmlElement('w:r')
        rPr = docx.oxml.OxmlElement('w:rPr')

        # 设置颜色
        color = docx.oxml.OxmlElement('w:color')
        color.set(docx.oxml.ns.qn('w:val'), '0000FF')  # 蓝色
        rPr.append(color)

        underline = docx.oxml.OxmlElement('w:u')
        underline.set(docx.oxml.ns.qn('w:val'), 'single')
        underline.set(docx.oxml.ns.qn('w:color'), 'FF0000' if is_error else '0000FF')
        rPr.append(underline)

        font = docx.oxml.OxmlElement('w:rFonts')
        font.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
        font.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
        font.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        rPr.append(font)

        sz = docx.oxml.OxmlElement('w:sz')
        sz.set(docx.oxml.ns.qn('w:val'), '24')  # 12pt
        rPr.append(sz)

        new_run.append(rPr)

        # 添加文本
        text_elm = docx.oxml.OxmlElement('w:t')
        text_elm.text = text
        new_run.append(text_elm)

        # 将 run 添加到 hyperlink
        hyperlink.append(new_run)
    except Exception as e:
        logger.error(f"set_hyperlink报错: {str(e)}")
    paragraph._element.append(hyperlink)

# 添加统一的角标处理函数
def apply_citation_superscript(run, citation_text):
    """
    统一的引用标记角标处理函数
    
    Args:
        run: docx的Run对象
        citation_text: 引用标记文本，如 "[1]", "[2,3]" 等
    """
    run.text = citation_text
    run.font.superscript = True
    run.font.name = 'Times New Roman'
    
    # XML级别的角标设置，确保在所有情况下都生效
    try:
        if hasattr(run._element, 'rPr') and run._element.rPr is not None:
            from docx.oxml import OxmlElement
            # 查找现有的vertAlign元素
            vert_align = run._element.rPr.find(docx.oxml.ns.qn('w:vertAlign'))
            if vert_align is not None:
                # 更新已有元素
                vert_align.set(docx.oxml.ns.qn('w:val'), 'superscript')
            else:
                # 创建并添加新元素
                superscript = OxmlElement('w:vertAlign')
                superscript.set(docx.oxml.ns.qn('w:val'), 'superscript')
                run._element.rPr.append(superscript)
    except Exception as e:
        logger.warning(f"设置XML级别角标时出错: {str(e)}")

def format_docx_file(doc):
    """
    统一处理Word文档格式化
    
    Args:
        doc: python-docx的Document对象
        
    Returns:
        doc: 格式化后的Document对象
    """
    is_in_citation_paragraph = False
    # 处理所有段落
    for i, paragraph in enumerate(doc.paragraphs):
        para_text: str = paragraph.text.strip()
        # 判断是否为标题段落
        is_heading = paragraph.style.name.startswith('Heading')
        
        # 如果是标题且是否为参考文献段落的标识为True
        if is_heading and is_in_citation_paragraph:
            is_in_citation_paragraph = False
        # 判断是否是参考文献标题
        if citation_head_match(para_text, True) and is_heading:
            is_in_citation_paragraph = True

        # 标题进行处理
        if is_heading:
            # 标题段落保持原样式，但更改字体为宋体
            paragraph.style.font.italic = False
            for run in paragraph.runs:
                # 安全地设置字体
                try:
                    # 直接设置字体名称（中英文）
                    run.font.name = '宋体'
                    # 尝试使用更安全的方式设置东亚字体
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    # 设置西文字体为Times New Roman
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                    run.font.bold = True  # 确保标题是粗体
                    # 设置字体颜色为黑色
                    run.font.color.rgb = RGBColor(0, 0, 0)
                    
                    run.font.italic = False  # 移除斜体属性
                    # 确保XML级别也移除斜体
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        for i in run._element.rPr.findall(docx.oxml.ns.qn('w:i')):
                            run._element.rPr.remove(i)
                        for i in run._element.rPr.findall(docx.oxml.ns.qn('w:iCs')):
                            run._element.rPr.remove(i)
                except Exception as e:
                    logger.warning(f"设置标题样式出错: {str(e)}")
        # 非标题进行处理
        else:
            paragraph.style = doc.styles['Normal']
            # 检查每个run，保留原始的加粗样式
            for run in paragraph.runs:
                # 保存原始加粗状态
                original_bold = run.bold
                
                # 直接设置字体名称（中英文）
                run.font.name = 'Times New Roman'
                # 同时设置东亚语言字体（确保中文显示为宋体）
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                # 设置西文字体为Times New Roman
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                
                # 恢复原始加粗状态，而不是强制设为False
                run.font.bold = original_bold
        is_citation_item = re.search(REFERENCE_RE, para_text)
        # is_citation_item = len(re.findall(REFERENCE_RE, para_text)) == 1  
        # 设置段落格式：段落间距、行距等
        try:
            if is_heading:
                # 标题段落设置
                paragraph.paragraph_format.space_before = docx.shared.Pt(12)  # 标题前增加间距
                paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 标题后统一间距
                
                # 一级标题(Heading 1)居中显示
                if paragraph.style.name == 'Heading 1':
                    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            elif is_in_citation_paragraph:
                pure_text = ""
                url = ""
                for i, child in enumerate(paragraph._element):
                    if child.tag.endswith('hyperlink'):
                        # 获取关系ID r:id
                        r_id = child.get(docx.oxml.ns.qn('r:id'))
                        # 从关系映射中获取 URL
                        if r_id and r_id in doc.part._rels:
                            url = doc.part._rels[r_id].target_ref
                        else:
                            url = None
                        for node in child.iter():
                            if node.tag.endswith('t'):  # 文本节点
                                pure_text += node.text or ''
                    else:
                        for node in child.iter():
                            if node.tag.endswith('t'):  # 文本节点
                                pure_text += node.text or ''
                citation_error_pattern = r'^(\[\d+\].+\.)(\(.+?\))$'
                match = re.search(citation_error_pattern, pure_text)
                if match:
                    paragraph.clear()
                    text = match.group(1).strip()
                    error_text = match.group(2).strip()
                    if not url:
                        main_run = paragraph.add_run(text)
                        main_run.font.underline = True
                        # 直接设置字体名称（中英文）
                        main_run.font.name = '宋体'
                        # 画下划线
                        rPr = main_run._element.rPr
                        # 同时设置东亚语言字体（确保中文显示为宋体）
                        rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                        # 设置西文字体为Times New Roman
                        rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                        rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                        u = docx.oxml.OxmlElement('w:u')
                        u.set(docx.oxml.ns.qn('w:val'), 'single')  # 单实线下划线
                        u.set(docx.oxml.ns.qn('w:color'), "FF0000")  # 设置下划线颜色为红色
                        rPr.append(u)
                    else:
                        set_hyperlink(paragraph, url, text, True)
                    # 幻觉审查错误的文本标记为红色
                    remark_run = paragraph.add_run(error_text)
                    remark_run.font.color.rgb = RGBColor(255, 0, 0)

                    remark_run.font.name = '宋体'
                    remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    # 设置西文字体为Times New Roman
                    remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                    remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                else:
                    if url:
                        paragraph.clear()
                        set_hyperlink(paragraph, url, pure_text, False)
                # 首行悬挂缩进
                paragraph.paragraph_format.first_line_indent = docx.shared.Pt(-21)  # 悬挂缩进
                paragraph.paragraph_format.left_indent = docx.shared.Pt(21)  # 左缩进
                # 段前段后间距
                paragraph.paragraph_format.space_before = docx.shared.Pt(0)  # 移除段前间距
                paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 统一设置段后间距为12磅
                # 使用数值设置行距为1.5倍
                paragraph.paragraph_format.line_spacing = 1.5
            else:
                # 正文段落设置 - 统一所有正文段落的间距
                paragraph.paragraph_format.space_before = docx.shared.Pt(0)  # 移除段前间距
                paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 统一设置段后间距为12磅
                # 使用数值设置行距为1.5倍
                paragraph.paragraph_format.line_spacing = 1.5
                p = paragraph._p  # 获取底层 XML 对象
                pPr = p.pPr
                # 这是所有不带有doc序号（类似html标签的ul/ol li这种）的段落
                if not (pPr is not None and pPr.numPr is not None):
                    # print("wuhu")
                    paragraph.paragraph_format.first_line_indent = docx.shared.Pt(21)  # 首行缩进两个中文字符
                # 这说明是正文里面写了参考文献的格式
                if is_citation_item:
                    logger.info(f"正文里面写了参考文献的格式: {para_text}")
        except Exception as e:
            logger.warning(f"设置段落格式时出错: {str(e)}")
        # 正文里面引用参考文献的处理
        try:
            literature_pattern = r'\S+\[\d+(?:,\s*\d+)*\].*?[。！!?？\n\.]'
            error_text_pattern = r'\(\s*(?:\[\d+\][^、\[\]]+)(?:、\[\d+\][^、\[\]]+)*\s*\)'
            # cite_num_pattern = r'^\[\s*\d+(?:\s*,\s*\d+)*\s*\]$'
            search_match = re.search(literature_pattern, para_text)
            if search_match:
                # last_run = None
                for i, item in enumerate(paragraph.runs):
                    run_text = item.text.strip()
                    if re.search(error_text_pattern, run_text):
                        item.font.color.rgb = RGBColor(255, 0, 0)
                    if item.font.underline:
                        draw_underline(item)
                    # if re.search(cite_num_pattern, run_text.strip()):
                    #     draw_underline(last_run)
                    # last_run = item
        except Exception as e:
            logger.error(f"处理有引用的段落的报错：{str(e)}")
    return doc

# def format_docx_file(doc):
#     """
#     统一处理Word文档格式化
    
#     Args:
#         doc: python-docx的Document对象
        
#     Returns:
#         doc: 格式化后的Document对象
#     """
#     is_in_citation_paragraph = False
#     # 处理所有段落
#     for i, paragraph in enumerate(doc.paragraphs):
#         para_text: str = paragraph.text.strip()
#         # 判断是否为标题段落
#         is_heading = paragraph.style.name.startswith('Heading')
        
#         # 如果是标题且是否为参考文献段落的标识为True
#         if is_heading and is_in_citation_paragraph:
#             is_in_citation_paragraph = False
#         # 判断是否是参考文献标题
#         if is_heading and citation_head_match(para_text, True):
#             logger.info("有参考文献段落")
#             is_in_citation_paragraph = True

#         # 标题进行处理
#         if is_heading:
#             # 标题段落保持原样式，但更改字体为宋体
#             paragraph.style.font.italic = False
#             for run in paragraph.runs:
#                 # 安全地设置字体
#                 try:
#                     # 直接设置字体名称（中英文）
#                     run.font.name = '宋体'
#                     # 尝试使用更安全的方式设置东亚字体
#                     if hasattr(run._element, 'rPr') and run._element.rPr is not None:
#                         if hasattr(run._element.rPr, 'rFonts'):
#                             run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
#                     # 设置西文字体为Times New Roman
#                     if hasattr(run._element, 'rPr') and run._element.rPr is not None:
#                         if hasattr(run._element.rPr, 'rFonts'):
#                             run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
#                             run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
#                     run.font.bold = True  # 确保标题是粗体
#                     # 设置字体颜色为黑色
#                     run.font.color.rgb = RGBColor(0, 0, 0)
                    
#                     run.font.italic = False  # 移除斜体属性
#                     # 确保XML级别也移除斜体
#                     if hasattr(run._element, 'rPr') and run._element.rPr is not None:
#                         for i in run._element.rPr.findall(docx.oxml.ns.qn('w:i')):
#                             run._element.rPr.remove(i)
#                         for i in run._element.rPr.findall(docx.oxml.ns.qn('w:iCs')):
#                             run._element.rPr.remove(i)
#                 except Exception as e:
#                     logger.warning(f"设置标题样式出错: {str(e)}")
#         # 非标题进行处理
#         else:
#             paragraph.style = doc.styles['Normal']
            
#             # 检查每个run，保留原始的加粗样式
#             for run in paragraph.runs:
#                 # 保存原始加粗状态
#                 original_bold = run.bold
                
#                 # 直接设置字体名称（中英文）
#                 run.font.name = '宋体'
#                 # 同时设置东亚语言字体（确保中文显示为宋体）
#                 run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
#                 # 设置西文字体为Times New Roman
#                 run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
#                 run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                
#                 # 恢复原始加粗状态，而不是强制设为False
#                 run.font.bold = original_bold
#         is_citation_item = re.search(REFERENCE_RE, para_text)
#         # is_citation_item = len(re.findall(REFERENCE_RE, para_text)) == 1  
#             # 设置段落格式：段落间距、行距等
#         try:
#             if is_heading:
#                 # 标题段落设置
#                 paragraph.paragraph_format.space_before = docx.shared.Pt(12)  # 标题前增加间距
#                 paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 标题后统一间距
                
#                 # 一级标题(Heading 1)居中显示
#                 if paragraph.style.name == 'Heading 1':
#                     paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
#             elif is_in_citation_paragraph:
#                 pure_text = ""
#                 url = ""
#                 for i, child in enumerate(paragraph._element):
#                     if child.tag.endswith('hyperlink'):
#                         # 获取关系ID r:id
#                         r_id = child.get(docx.oxml.ns.qn('r:id'))
#                         # 从关系映射中获取 URL
#                         if r_id and r_id in doc.part._rels:
#                             url = doc.part._rels[r_id].target_ref
#                         else:
#                             url = None
#                         for node in child.iter():
#                             if node.tag.endswith('t'):  # 文本节点
#                                 pure_text += node.text or ''
#                     else:
#                         for node in child.iter():
#                             if node.tag.endswith('t'):  # 文本节点
#                                 pure_text += node.text or ''
#                 citation_error_pattern = r'^(\[\d+\].+\.)(\(.+?\))$'
#                 match = re.search(citation_error_pattern, pure_text)
#                 if match:
#                     paragraph.clear()
#                     text = match.group(1).strip()
#                     error_text = match.group(2).strip()
#                     if not url:
#                         main_run = paragraph.add_run(text)
#                         main_run.font.underline = True
#                         # 直接设置字体名称（中英文）
#                         main_run.font.name = '宋体'
#                         # 画下划线
#                         rPr = main_run._element.rPr
#                         # 同时设置东亚语言字体（确保中文显示为宋体）
#                         rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
#                         # 设置西文字体为Times New Roman
#                         rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
#                         rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
#                         u = docx.oxml.OxmlElement('w:u')
#                         u.set(docx.oxml.ns.qn('w:val'), 'single')  # 单实线下划线
#                         u.set(docx.oxml.ns.qn('w:color'), "FF0000")  # 设置下划线颜色为红色
#                         rPr.append(u)
#                     else:
#                         set_hyperlink(paragraph, url, text, True)
#                     # 幻觉审查错误的文本标记为红色
#                     remark_run = paragraph.add_run(error_text)
#                     remark_run.font.color.rgb = RGBColor(255, 0, 0)

#                     remark_run.font.name = '宋体'
#                     remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
#                     # 设置西文字体为Times New Roman
#                     remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
#                     remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
#                 else:
#                     if url:
#                         paragraph.clear()
#                         set_hyperlink(paragraph, url, pure_text, False)
#                         # 首行悬挂缩进
#                         paragraph.paragraph_format.first_line_indent = docx.shared.Pt(-21)  # 悬挂缩进
#                         paragraph.paragraph_format.left_indent = docx.shared.Pt(21)  # 左缩进
#                         # 段前段后间距
#                         paragraph.paragraph_format.space_before = docx.shared.Pt(0)  # 移除段前间距
#                         paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 统一设置段后间距为12磅
#                         # 使用数值设置行距为1.5倍
#                         paragraph.paragraph_format.line_spacing = 1.5
#                     else:
#                         # 正文段落设置 - 统一所有正文段落的间距
#                         paragraph.paragraph_format.space_before = docx.shared.Pt(0)  # 移除段前间距
#                         paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 统一设置段后间距为12磅
#                         # 使用数值设置行距为1.5倍
#                         paragraph.paragraph_format.line_spacing = 1.5
#                     p = paragraph._p  # 获取底层 XML 对象
#                     pPr = p.pPr
#                     # 这是所有不带有doc序号（类似html标签的ul/ol li这种）的段落
#                     if not (pPr is not None and pPr.numPr is not None):
#                         paragraph.paragraph_format.first_line_indent = docx.shared.Pt(21)  # 首行缩进两个中文字符
                
#                     # 这说明是正文里面写了参考文献的格式
#                     if is_citation_item:
#                         logger.info(f"正文里面写了参考文献的格式: {para_text}")
#         except Exception as e:
#             logger.warning(f"设置段落格式时出错: {str(e)}")
        
        
#         # 正文里面引用参考文献的处理
#         try:
#             literature_pattern = r'[^。！!?？\n\.\s]+?\[\d+(?:,\s*\d+)*\].+?[。！!?？\n\.]'
#             search_match = re.search(literature_pattern, para_text)
#             if search_match:
#                 # numId, ilvl = get_num_props(paragraph)
#                 allBoldTextMap = []
#                 for run in paragraph.runs:
#                     key = run.text
#                     if run.font and run.font.bold:
#                         allBoldTextMap.append(key)
#                         paragraph.clear()
#                 parts = split_text_by_sentences(para_text, r'(。|：|\n|\|\?|\!|？|！)')
#                 for part in parts:
#                     # 这是幻觉审查里面要变颜色和加下划线的正文引用部分
#                     pattern_one = r'(?P<main>.+?\[\d+(?:,\s*\d+)*\])(?P<remark>\([^\)]+?\))(?P<dot>.+?)'
#                     # 这是正常的正文引用部分
#                     pattern_two = r'(?P<main>.+?\[\d+(?:,\s*\d+)*\])(?P<dot>.+?)'
#                     one_match = re.match(pattern_one, part)
#                     two_match = re.match(pattern_two, part)
#                     main = ""
#                     remark = ""
#                     dot = ""
#                     if one_match:
#                         try:
#                             main = one_match.group('main')
#                             remark = one_match.group('remark')
#                             dot = one_match.group('dot')
#                             main_match = re.match(CITATION_NUM_RE, main)
#                             if main_match:
#                                 main_content = main_match.group(1).strip()
#                                 main_run = paragraph.add_run(main_content)
#                                 main_run.font.underline = True
#                                 main_run.font.name = '宋体'
#                                 main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
#                                 # 画下划线
#                                 rPr = main_run._element.rPr
#                                 u = docx.oxml.OxmlElement('w:u')
#                                 u.set(docx.oxml.ns.qn('w:val'), 'single')  # 单实线下划线
#                                 u.set(docx.oxml.ns.qn('w:color'), "FF0000")  # 设置下划线颜色为红色
#                                 rPr.append(u)

#                                 # 角标处理
#                                 main_ref = main_match.group(2).strip()
#                                 main_ref_run = paragraph.add_run(main_ref)
#                                 main_ref_run.font.superscript = True
#                                 main_ref_run.font.name = 'Times New Roman'
#                             else:
#                                 logger.info(f"不符合引用的段落：{main}")
#                                 not_fit_run = paragraph.add_run(main)
#                                 not_fit_run.font.name = '宋体'
#                             # 幻觉审查错误的文本标记为红色
#                             remark_run = paragraph.add_run(remark)
#                             remark_run.font.color.rgb = RGBColor(255, 0, 0)

#                             remark_run.font.name = '宋体'
#                             remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
#                             # 设置西文字体为Times New Roman
#                             remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
#                             remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
#                             # 结束的标点符号
#                             dot_run = paragraph.add_run(dot)
#                             dot_run.font.name = '宋体'
#                         except Exception as e:
#                             logger.error(f"pattern_one里面报错:{str(e)}")
#                     elif two_match:
#                         try:
#                             main = two_match.group('main').strip()
#                             dot = two_match.group('dot')
#                             # 角标处理
#                             main_match = re.match(CITATION_NUM_RE, main)
#                             main_content = main_match.group(1).strip()
#                             main_run = paragraph.add_run(main_content)
#                             main_run.font.name = '宋体'
#                             main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
#                             main_ref = main_match.group(2).strip()
#                             main_ref_run = paragraph.add_run(main_ref)
#                             main_ref_run.font.superscript = True
#                             main_ref_run.font.name = 'Times New Roman'
#                             # 结束的标点符号
#                             dot_run = paragraph.add_run(dot)
#                             dot_run.font.name = '宋体'
#                         except Exception as e:
#                             logger.error(f"pattern_two里面报错:{str(e)}")
#                     # 这是正常不存在引用序号的文本
#                     else:
#                         other_part_run = paragraph.add_run(part)
#                         other_part_run.font.name = '宋体'
#                         other_part_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
#                         # 设置西文字体为Times New Roman
#                         other_part_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
#                         other_part_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
#                         try:
#                             if part in allBoldTextMap:
#                                 other_part_run.font.bold = True
#                         except Exception as e:
#                             logger.info(f"这个段落不适合作为key: {str(e)}")
#                 # if numId and ilvl:
#                 #     set_num_props(paragraph, numId, ilvl)
#         except Exception as e:
#             logger.error(f"处理有引用的段落的报错：{str(e)}")
#     return doc

# 将只有一个\n换行符正文中的参考文献格式文本替换为两个换行符
# def split_citation_in_paper_to_paragraph(text: str) -> str:
#     sentences = split_text_by_sentences(text)
#     is_in_citation_section = False
#     # 正文中的参考文献形式内容
#     citation_list: List[List[str]] = []
#     current_item: List[str] = []
#     for item in sentences:
#         is_citation_head = citation_head_match(item)
#         is_head = is_markdown_title(item)
#         # 这是当处于参考文献后面的标题时
#         if is_head and is_in_citation_section:
#             is_in_citation_section = False
#         if is_citation_head:
#             is_in_citation_section = True
#         if not is_in_citation_section:
#             if re.search(REFERENCE_RE, item.strip()):
#                 current_item.append(item)
#             else:
#                 if (len(current_item) > 1):
#                     citation_list.append(current_item)
#                     current_item = []
#     for item in citation_list:
#         sub_text = ("\n").join(item)
#         text = text.replace(sub_text, ("\n\n").join(item))
#     return text
    # return re.sub(r'(?<!\n)\n(?!\n)', '\n\n', text)
# 这是因为YAML的---会导致pandoc转换失败
def remove_dash_lines(text: str) -> str:
    lines = text.splitlines()
    return '\n'.join(line for line in lines if line.strip() != '---')
# 去掉（字数：xxx）这种文字
def remove_word_count_tag(text: str) -> str:
    # 匹配全角括号里的"字数：数字"
    # 按段落分割（以两个换行作为段落分隔）
    paragraphs = text.strip().split('\n\n')

    if not paragraphs:
        return text
    list_data = []
    reg = re.compile(r'^（.*?）$')
    for item in paragraphs:
        if not reg.match(item.strip()):
            list_data.append(item)
    # 重新组合段落
    return '\n\n'.join(list_data)
# 移除分步生成正文的参考文献部分及后面的段落内容
def remove_after_reference(text: str):
    lines = text.splitlines()
    pattern = re.compile(r'^.{0,7}参考文献.{0,7}$')

    for i, line in enumerate(lines):
        if pattern.match(line.strip()):
            # 返回"参考文献"这一行之前的所有内容
            return '\n'.join(lines[:i])
    
    # 如果没有匹配到，返回原文
    return text
# 将分段生成里面的标题移除
def remove_markdown_heading(text: str, heading_text: str) -> str:
    """
    从Markdown文本中移除指定的标题行
    
    Args:
        text: 原始Markdown文本
        heading_text: 要移除的标题文本
        
    Returns:
        str: 移除标题后的文本，如果输入参数无效则返回原文本或空字符串
    """
    # 边界检查 - 安全处理，不抛出异常
    if not text or not heading_text:
        logger.debug("text或heading_text为空，直接返回原文本")
        return text or ""
    
    # 去除heading_text的前后空白
    heading = heading_text.strip()
    if not heading:
        logger.debug("heading_text去除空白后为空，直接返回原文本")
        return text
    
    logger.debug(f"尝试移除标题: {heading}")
    
    # 使用更安全的正则表达式，避免灾难性回溯
    # 修复语法：正确匹配1-6个#号，支持#后有0个或多个空格的情况
    pattern = re.compile(
        rf'^[ ]{{0,3}}#{{1,6}}[ ]*{re.escape(heading)}[ ]*$',
        re.MULTILINE
    )
    
    result = pattern.sub('', text).strip()
    
    logger.debug(f"标题移除操作完成，原文本长度: {len(text)}, 处理后长度: {len(result)}")
    return result

# 将/api/statics/pictures路径的图片下载下来并且将md里面的图片改成相对路径
def transform_image_url(md_text: str):
    # url = settings.WEB_BASE_URL + "/api/statics/pictures"
    """
    提取 Markdown 文本中的所有图片地址
    """
    pattern = r'!\[.*?\]\((.*?)\)'
    image_list: List[str] = re.findall(pattern, md_text)
    logger.info(f"{image_list}")
    result = md_text
    picture_list: List[str] = []
    start_text = settings.WEB_BASE_URL + "/api/statics/pictures"
    for item in image_list:
        logger.info(start_text)
        if item.startswith(start_text):
            relative_path = item.split('/api/statics/')[-1]
            logger.info(relative_path)
            # 下载文档
            download_file(relative_path)
            result = result.replace(item, relative_path)
            picture_list.append(relative_path)
    logger.info(f"替换后的文档：\n{result}")
    return result, picture_list
# 通用的Markdown转Word处理函数
def convert_markdown_to_docx(
    file_path: str
):
    """
    将Markdown文件转换为格式化的Word文档
    
    Args:
        file_path: Markdown文件的绝对路径或者相对路径
        
    Returns:
        tuple: (临时文件路径, 下载文件名)
    """
    abs_path = ""
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    filename = os.path.basename(abs_path)
    
    # 智能处理文件扩展名
    name_without_ext = os.path.splitext(filename)[0]
    output_filename = f"{name_without_ext}.docx"
    
    # temp_docx_file = os.path.dirname(abs_path) + f"/{output_filename}"
    # 创建临时输出文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as tmp:
        temp_docx_file = tmp.name
    try:
        text = read_file_content(abs_path)
    except Exception as e:
        raise e
    text = handle_text_before_use(
        text,
        [
            "change_markdown_paragraph_join_way",
            "remove_dash_lines",
            "bracket_latex_to_dollar"
        ]
    )
    text = convert_citations_to_sup(text, "caret")
    text = convert_underline_text(text)
    text, picture_list = transform_image_url(text)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    lua_filter_path = os.path.join(current_dir, 'remove_underline.lua')
    # 转换文件
    pypandoc.convert_text(
        text,
        'docx',
        outputfile=temp_docx_file,
        format='markdown',
        extra_args=[
            '--standalone',
            '--mathml',
            '--shift-heading-level-by=0',  # 保持原始标题级别: # -> 标题1, ## -> 标题2, ### -> 标题3
            f'--lua-filter={lua_filter_path}'
        ]
    )
    
    # 使用python-docx处理文档格式
    doc = Document(temp_docx_file)
    doc = format_docx_file(doc)
    doc.save(temp_docx_file)
    # return temp_docx_file, output_filename
    # 返回文件下载响应
    def remove_file(picture_list, temp_docx_file):
        # 移除下载的图片
        for relative_path in picture_list:
            remove_local_file(relative_path)
        # 移除临时文件
        os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None
    return FileResponse(
        path=temp_docx_file,
        filename=output_filename,
        # media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        background=BackgroundTask(remove_file, picture_list=picture_list, temp_docx_file=temp_docx_file)
    )

# 参考文献幻觉审查时参考文献的拼接方式
def hallucination_combine_citation(
    # 参考文献除url的文本可能包含序号也可能不包含序号，就看index有没有传
    full_text: str,
    # 参考文献的序号
    index: Optional[int] = None,
    # 参考文献的链接
    url: Optional[str] = None,
    # 错误信息
    error_list: List[str] = []
):
    text = f"{[index]} " if index else ""
    if error_list:
        text += f"<span style='text-decoration: underline;text-decoration-color: red;'>"
        if url:
            text += f"[{full_text}]({url})</span><span style='color: red'>&#40;{'、'.join(error_list)}&#41;</span>"
        else:
            text += f"{full_text}</span><span style='color: red'>&#40;{'、'.join(error_list)}&#41;</span>"
    else:
        if url:
            text += f"[{full_text}]({url})"
        else:
            text += f"{full_text}"
    return text
# 将正文里面html添加下划线的方式改为[]{.underline}形式
def convert_underline_text(text: str, is_remove:Optional[bool] = False):
    pattern = re.compile(
        r"<span\s+[^>]*?style=['\"]text-decoration\s*:\s*underline;?[^'\"]*['\"][^>]*>(.*?)</span>",
        re.IGNORECASE | re.DOTALL
    )
    return pattern.sub(r"[\1]{.underline}", text) if not is_remove else pattern.sub(r"\1", text)
#将word文档转成markdown，这里只是简单的
def docx_file_to_markdown(
    # 文件路径
    file_path
):
    abs_path = ""
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    lua_filter_path = os.path.join(current_dir, 'remove_underline.lua')
    output = pypandoc.convert_file(
        source_file=abs_path,
        to='markdown-bracketed_spans-native_spans-raw_html',
        format="docx",
        extra_args=[
            '--wrap=none',
            f'--lua-filter={lua_filter_path}'
        ]
    )
    # docx的[1]、[2]转成md后会变成\[1\]，因此需要去掉\
    output = re.sub(r'\\\[(\s*\d+(?:\s*,\s*\d+)*\s*)\\\]', r'[\1]', output)
    # 匹配 [* 开头，*] 结尾，中间可以有任意字符（非贪婪）主要是word的外链会出现这种
    output = re.sub(r'\[\*(.+?)\*\]', r'\1', output)
    # 将\[ \]替换为[]
    # output = re.sub(r'\\\[(.+?)\\\]', r'\[\1\]', output)
    output = re.sub(r'\\\[([^\]]+?)\\\]', r'[\1]', output)
    # 将^[]^ 替换为[]
    output = re.sub(r'\^\[([^\]]+?)\]\^', r'[\1]', output)
    return output
# 通用的Markdown转Word处理函数
def convert_markdown_to_pdf(
    file_path: str
):
    timer = Timer()
    abs_path = ""
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    filename = os.path.basename(abs_path)
    
    # 智能处理文件扩展名
    name_without_ext = os.path.splitext(filename)[0]
    output_filename = f"{name_without_ext}.pdf"
    logger.info(f"建文件夹花费：{timer.elapsed():.4f}秒")
    # 创建临时输出文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
        temp_docx_file = tmp.name
    try:
        text = read_file_content(abs_path)
    except Exception as e:
        raise e
    logger.info(f"创建文件夹花费：{timer.elapsed():.4f}秒")
    text = change_markdown_paragraph_join_way(text)
    text = convert_underline_text(text, True)
    text = remove_dash_lines(text)
    text = convert_citations_to_sup(text, "caret")
    text = re.sub(
        r"<span style='color: red'>&#40;([^<>]*)&#41;</span>",
        r"&#40;\\color{red}\1\\color{black}&#41;",
        text
    )
    text = bracket_latex_to_dollar(text)
    text, picture_list = transform_image_url(text)
    logger.info(text)
    CJKmainfont, mainfont, mathfont, monofont = get_font()
    logger.info(f"文本处理花费：{timer.elapsed():.4f}秒")
    # 返回文件下载响应
    def remove_file(picture_list, temp_docx_file):
        # 移除下载的图片
        for relative_path in picture_list:
            remove_local_file(relative_path)
        # 移除临时文件
        os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None
    logger.info(mainfont)
    # 转换文件
    pypandoc.convert_text(
        text,
        'pdf',
        outputfile=temp_docx_file,
        format='markdown+raw_html',
        extra_args=[
            "--pdf-engine=xelatex",
            '-V', f'mainfont={mainfont}',
            '-V', f"mathfont={mathfont}",
            '-V', f"CJKmainfont={CJKmainfont}",
            '-V', f"monofont={monofont}",
            '-V', 'geometry=margin=1cm',
            '-V', 'documentclass=ctexart',
            '-V', 'pagestyle=empty'
        ]
    )
    logger.info(f"pypandoc花费：{timer.elapsed():.4f}秒")
    return FileResponse(
        path=temp_docx_file,
        filename=output_filename,
        media_type="application/pdf",
        background=BackgroundTask(remove_file, picture_list=picture_list, temp_docx_file=temp_docx_file)
    )

def bracket_latex_to_dollar(text: str) -> str:
    # 替换块级公式 \[...\]
    def replace_block(match):
        content = match.group(1).replace('\n', ' ')
        return f'$$ {content.strip()} $$'

    text = re.sub(r'\\\[\n\s*(.*?)\n\s*\\\]', replace_block, text, flags=re.DOTALL)

    # 替换行内公式 \(...\)
    def replace_inline(match):
        content = match.group(1).replace('\n', ' ')
        return f'${content.strip()}$'  # 或者改成 f'${content.strip()}$' 保持行内形式

    text = re.sub(r'\\\((.*?)\\\)', replace_inline, text, flags=re.DOTALL)

    def replacer(match):
        content = match.group(1)
        # 去除多余空格和换行符，拼成一行
        single_line = ' '.join(line.strip() for line in content.strip().splitlines())
        return f"$$ {single_line} $$"
    
    text = re.sub(r'\$\$\s*(.*?)\s*\$\$', replacer, text, flags=re.DOTALL)
    return text

def fix_math_formula_format(text: str) -> str:
    """
    修复数学公式格式，确保符合LaTeX规范:
    1. 单行公式：$formula$ (无空格)
    2. 多行公式：$$ formula $$ (有空格)
    """
    logger.info(f"准备执行fix_math_formula_format函数，处理前的文本长度: {len(text)}")
    # 修复多行公式格式：确保 $$ 前后有空格
    def fix_display_math(match):
        content = match.group(1).strip()
        return f'$$ {content} $$'
    
    # 处理 $$...$$
    text = re.sub(r'\$\$\s*(.*?)\s*\$\$', fix_display_math, text, flags=re.DOTALL)
    
    # 修复单行公式格式：确保 $ 前后无空格
    def fix_inline_math(match):
        content = match.group(1).strip()
        return f'${content}$'
    
    # 处理 $...$ 但避免处理 $$...$$
    # 使用负向前瞻和负向后顾来避免匹配 $$
    text = re.sub(r'(?<!\$)\$(?!\$)\s*(.*?)\s*\$(?!\$)', fix_inline_math, text, flags=re.DOTALL)
    logger.info(f"fix_math_formula_format函数执行完毕，处理后的文本长度: {len(text)}")
    return text
async def validate_file_security(
    file: UploadFile,
    max_size: int = 15 * 1024 * 1024,
    allowed_mimes: List[str] = [
        'text/plain',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',  # 添加 .doc 文件的 MIME 类型
        'application/pdf',
        'application/zip'
    ]
) -> bool:
    # 检查文件大小（例如限制10MB）
    if file.size > max_size:
        raise Exception("文件大小超过限制")
    
    # 检查MIME类型
    file_content = await file.read()  # 读取前1KB用于检测
    file.file.seek(0)  # 重置文件指针
    
    if MAGIC_AVAILABLE and magic:
        mime_type = magic.from_buffer(file_content, mime=True)
        logger.info(f"mime_type:{mime_type}")
        
        # .doc 文件可能被识别为不同的 MIME 类型，需要特殊处理
        if mime_type not in allowed_mimes:
            # 检查是否为 .doc 文件的其他可能 MIME 类型
            doc_mime_types = [
                'application/msword',
                'application/vnd.ms-office',
                'application/x-msword',
                'application/octet-stream' , # 有时 .doc 文件会被识别为此类型
                'application/zip'
            ]
            
            # 如果文件扩展名是 .doc 且 MIME 类型是可能的 .doc 类型之一，则允许
            if (file.filename and file.filename.lower().endswith('.doc') and 
                mime_type in doc_mime_types):
                logger.info(f".doc 文件通过扩展名验证，MIME类型: {mime_type}")
            else:
                raise Exception("文件类型不支持")
    else:
        # 如果magic库不可用，使用文件扩展名进行基本检查
        logger.warning("magic库不可用，使用文件扩展名进行类型检查")
        file_extension = file.filename.lower()
        if not any(file_extension.endswith(ext) for ext in ['.txt', '.docx', '.doc', '.pdf']):
            raise Exception("文件类型不支持")
    return True
async def validate_text_length(
    file: UploadFile,
    max_length: Optional[int] = 15000
):    
    try:
        if file.filename.endswith(".docx") or file.filename.endswith(".doc") or file.filename.endswith(".pdf"):
            # 根据原文件类型设置临时文件后缀
            if file.filename.endswith(".pdf"):
                suffix = ".pdf"
            elif file.filename.endswith(".doc"):
                suffix = ".doc"
            else:  # .docx
                suffix = ".docx"
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
                contents = await file.read()
                tmp.write(contents)
                tmp_path = tmp.name
            logger.info(f"tmp_path: {tmp_path}")
            content = read_file_content(tmp_path)
        else:
            content_bytes = await file.read()
            content = content_bytes.decode('utf-8')
    except UnicodeDecodeError:
        raise Exception("文档内容无法解析")
    
    file.file.seek(0)  # 重置文件指针
    logger.info(f"文字长度: {len(content)}")
    # 校验字数
    if len(content) > max_length:
        raise Exception(f"文字过长，最多{max_length}字")
    return True

def is_possibly_garbled(text: str, threshold: float = 0.4) -> bool:
    # 非中文、英文字母、数字和常见标点之外的字符
    garbled_chars = re.findall(r"[^\u4e00-\u9fa5a-zA-Z0-9\s.,，。！？、：；“”‘’（）()<>《》\"\'\-]", text)
    garbled_ratio = len(garbled_chars) / len(text) if text else 1.0
    print(f"Garbled ratio: {garbled_ratio:.2%}")
    return garbled_ratio > threshold
def get_font():
    mainfont = ""
    mathfont = ""
    CJKmainfont = ""
    monofont = ""
    if sys.platform.startswith('win'):
        mainfont = "Microsoft YaHei"
        mathfont = "Microsoft YaHei"
        CJKmainfont = "Microsoft YaHei"
        monofont = "Microsoft YaHei"
    elif sys.platform.startswith('linux'):
        mainfont = "Latin Modern Roman"
        mathfont = "Latin Modern Math"
        CJKmainfont = "Noto Sans CJK SC"
        monofont = "Latin Modern Mono"
    elif sys.platform == 'darwin':
        mainfont = "Menlo Regular"
        mathfont = "Latin Modern Math"
        CJKmainfont = "STSong"
        monofont = "Menlo"
    return CJKmainfont, mainfont, mathfont, monofont