from typing import Dict, List, Optional
import asyncio
import re
from app.utils.utils import (
    ContentStatus
)
from app.core.logging import get_logger

logger = get_logger(__name__)

class Data:
    """数据结构"""
    def __init__(self, content: str, status: str = ContentStatus.NORMAL.value):
        self.status: str = status  # 已读取的内容块
        self.content: str = content  # 未读取的内容块 # 异步任务实例

class ProjectContent:
    """项目内容数据结构"""
    def __init__(self):
        # self.status: ContentStatus = ContentStatus.GENERATING
        self.read_chunks: List[Data] = []  # 已读取的内容块
        self.unread_chunks: List[Data] = []  # 未读取的内容块
        self.error_message: Optional[str] = None  # 错误信息
        self.asyncioInstance: Optional[asyncio.Task] = None  # 异步任务实例
        # self.config: Optional[Config] = None
#     # self.error_message: Optional[str] = None  # 错误信息

class ContentManager:
    # """内容管理器，用于管理项目内容的读写状态"""
    # _instance = None
    def __init__(self):
        self.data = {}
    
    data: Dict[str, ProjectContent]
    def get_send_length(self, project_id: str):
        data = self.data[str(project_id)]
        read_chunk = data.read_chunks
        total = 0
        for item in read_chunk:
            # 只计算正常文字
            if item.status == ContentStatus.NORMAL.value:
                total += len(item.content)
        return total
    def add_content(
        self,
        project_id: str,
        content: str,
        status: str = ContentStatus.NORMAL.value
    ) -> None:
        """
        添加内容到指定项目的未读取队列
        
        Args:
            project_id: 项目ID
            content: 要添加的内容
        """
        # 确保项目ID存在于数据字典中
        if str(project_id) not in self.data:
            logger.error("创建新的咯")
            self.data[str(project_id)] = ProjectContent()
        logger.info(f"添加内容到未读取队列: {content}")
        # 添加内容到未读取队列
        self.data[str(project_id)].unread_chunks.append(Data(content, status))

    def get_asyncio(self, project_id: str):
        data = self.data.get(project_id)
        if not data:
            return None
        return data.asyncioInstance
    
    def add_asyncio(self, project_id: str, asyncioInstance: asyncio.Task) -> None:
        """
        为指定项目添加asyncio任务实例
        
        Args:
            project_id: 项目ID
            asyncioInstance: asyncio任务实例
        """
        # 确保项目ID存在于数据字典中
        if str(project_id) not in self.data:
            self.data[str(project_id)] = ProjectContent()
        # 存储asyncio任务实例
        self.data[str(project_id)].asyncioInstance = asyncioInstance
        logger.info(f"为项目 {str(project_id)} 添加asyncio任务实例")
    
    def read_next_chunk(self, project_id: str) -> Optional[Data]:
        """
        读取指定项目的下一个未读取内容块
        
        Args:
            project_id: 项目ID
            
        Returns:
            下一个未读取的内容块，如果没有则返回None
        """
        # 检查项目ID是否存在
        if str(project_id) not in self.data:
            return None
        
        project_content = self.data[str(project_id)]
        
        # 检查是否有未读取的内容
        if len(project_content.unread_chunks) == 0:
            logger.info("未读信息里面没有内容了")
            return None
        
        # 从未读取队列中取出第一项
        chunk = project_content.unread_chunks.pop(0)
        logger.info(f"从未读取列表获取的内容: {chunk.content}")
        # 添加到已读取队列
        project_content.read_chunks.append(chunk)
        
        # 如果取出来的是错误状态的信息。那么将本地缓存数据全部清除
        if chunk.status == ContentStatus.ERROR.value:
            # 删除项目ID
            logger.error("读取下一个chunk时读取到错误信息，准备清除本地缓存数据")
            del self.data[str(project_id)]
        return chunk
    
    def get_project_content(self, project_id: str) -> Optional[ProjectContent]:
        """
        获取项目内容对象
        
        Args:
            project_id: 项目ID
            
        Returns:
            项目内容对象，如果不存在则返回None
        """
        return self.data.get(str(project_id))
    
    def clear_project(self, project_id: str) -> None:
        """
        清除项目的所有内容
        
        Args:
            project_id: 项目ID
        """
        if self.get_asyncio(project_id):
            self.get_asyncio(project_id).cancel()
        if str(project_id) in self.data:
            del self.data[str(project_id)]
        logger.error("\n\n\n清空本地数据啦\n\n\n")