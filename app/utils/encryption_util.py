import base64
import os
import json
import time
import hmac
import hashlib
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives.kdf.scrypt import Scrypt
from cryptography.hazmat.backends import default_backend

from app.core.config import settings
from app.core.logging import get_logger

backend = default_backend()

logger = get_logger(__name__)

aes_salt_bytes = settings.AES_SALT.encode('utf-8')
hmac_salt_bytes = settings.HMAC_SALT.encode('utf-8')

def derive_key(password: str, salt: bytes, length: int = 32) -> bytes:
    kdf = Scrypt(
        salt=salt,
        length=length,
        n=16384,
        r=8,
        p=1,
        backend=backend,
    )
    return kdf.derive(password.encode('utf-8'))

def encrypt_data(data: dict, aes_password: str, hmac_password: str):

    aes_key = derive_key(aes_password, aes_salt_bytes, 32)
    hmac_key = derive_key(hmac_password, hmac_salt_bytes, 32)

    iv = os.urandom(12)

    need_encrypt_data = {
        'data': data,
        'timestamp': int(time.time() * 1000)
    }
    logger.info(f"加密工具类，加密前数据：{need_encrypt_data}")
    plaintext = json.dumps(need_encrypt_data, separators=(',', ':')).encode('utf-8')

    aesgcm = AESGCM(aes_key)
    ciphertext_with_tag = aesgcm.encrypt(iv, plaintext, None)

    tag = ciphertext_with_tag[-16:]
    ciphertext = ciphertext_with_tag[:-16]

    encrypted_with_iv_and_tag = base64.b64encode(iv + tag + ciphertext).decode('utf-8')

    signature = hmac.new(hmac_key, encrypted_with_iv_and_tag.encode('utf-8'), hashlib.sha256).hexdigest()

    return {
        'encryptedData': encrypted_with_iv_and_tag,
        'signature': signature
    }
