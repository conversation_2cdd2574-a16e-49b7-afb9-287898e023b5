from datetime import timedelta
from typing import Optional
from app.core.security import create_download_token
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)

def generate_file_download_url(
    file_path: str,
    resource_id: str,
    user_id: str,
    expires_hours: int = None
) -> Optional[str]:
    """
    生成文件下载地址（带token）
    
    Args:
        file_path: 文件相对路径，例如 "attachments/20250712/xxx.pptx"
        resource_id: 资源ID（如PPT ID）
        user_id: 用户ID
        expires_hours: token过期时间（小时），默认从配置读取
        
    Returns:
        str: 完整的下载URL，如果生成失败返回None
    """
    try:
        # 如果没有指定过期时间，使用配置中的默认值
        if expires_hours is None:
            expires_hours = settings.DOWNLOAD_TOKEN_EXPIRE_HOURS
            
        # 创建下载token
        download_token = create_download_token(
            ppt_id=resource_id,
            user_id=str(user_id),
            expires_delta=timedelta(hours=expires_hours)
        )
        
        # 构建指向API接口的下载URL，而不是直接指向静态文件
        download_url = f"{settings.WEB_BASE_URL}/api/statics/download/{resource_id}?path={file_path}&token={download_token}"
        
        logger.info(f"生成下载地址成功: resource_id={resource_id}, user_id={user_id}")
        return download_url
        
    except Exception as e:
        logger.error(f"生成下载地址失败: {str(e)}", exc_info=True)
        return None



 