import base64
import os
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
from urllib.parse import urlparse, parse_qs, urlunparse, quote_plus
import re
from app.core.logging import get_logger

logger = get_logger(__name__)

# 直接从环境变量获取SECRET_KEY，避免循环导入
MASTER_KEY = os.environ.get("SECRET_KEY", "default-secret-key-for-development-only")

def derive_key(password: str, salt: bytes, length: int = 32) -> bytes:
    """从密码派生密钥"""
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=length,
        salt=salt,
        iterations=100000,
        backend=default_backend()
    )
    return kdf.derive(password.encode('utf-8'))

def encrypt_db_password(password: str) -> str:
    """加密数据库密码"""
    if not password:
        return ""
    
    try:
        # 生成随机盐和初始化向量
        salt = os.urandom(16)
        iv = os.urandom(12)
        
        # 从主密钥派生加密密钥
        key = derive_key(MASTER_KEY, salt, 32)
        
        # 使用AESGCM加密
        aesgcm = AESGCM(key)
        ciphertext = aesgcm.encrypt(iv, password.encode('utf-8'), None)
        
        # 将盐、IV和密文组合并进行Base64编码
        encrypted_data = base64.b64encode(salt + iv + ciphertext).decode('utf-8')
        return f"ENC:{encrypted_data}"
    except Exception as e:
        logger.error(f"数据库密码加密失败: {str(e)}")
        return password

def decrypt_db_password(encrypted_password: str) -> str:
    """解密数据库密码"""
    if not encrypted_password or not encrypted_password.startswith("ENC:"):
        return encrypted_password
    
    try:
        # 提取加密数据
        encrypted_data = base64.b64decode(encrypted_password[4:])
        
        # 提取盐、IV和密文
        salt = encrypted_data[:16]
        iv = encrypted_data[16:28]
        ciphertext = encrypted_data[28:]
        
        # 从主密钥派生解密密钥
        key = derive_key(MASTER_KEY, salt, 32)
        
        # 使用AESGCM解密
        aesgcm = AESGCM(key)
        decrypted_password = aesgcm.decrypt(iv, ciphertext, None).decode('utf-8')
        return decrypted_password
    except Exception as e:
        logger.error(f"数据库密码解密失败: {str(e)}")
        return ""

def encrypt_db_url(db_url: str) -> str:
    """加密数据库连接URL中的密码部分"""
    if not db_url:
        return db_url
        
    try:
        # 解析URL
        parsed_url = urlparse(db_url)
        
        # 检查URL是否包含用户名和密码
        if '@' in parsed_url.netloc:
            auth, host = parsed_url.netloc.split('@', 1)
            if ':' in auth:
                username, password = auth.split(':', 1)
                
                # 加密密码
                encrypted_password = encrypt_db_password(password)
                
                # 重建URL，使用加密后的密码
                new_netloc = f"{username}:{encrypted_password}@{host}"
                encrypted_url = urlunparse((
                    parsed_url.scheme,
                    new_netloc,
                    parsed_url.path,
                    parsed_url.params,
                    parsed_url.query,
                    parsed_url.fragment
                ))
                return encrypted_url
        
        return db_url
    except Exception as e:
        logger.error(f"数据库URL加密失败: {str(e)}")
        return db_url

def decrypt_db_url(encrypted_db_url: str) -> str:
    """解密数据库连接URL中的密码部分"""
    if not encrypted_db_url:
        return encrypted_db_url
        
    try:
        # 解析URL
        parsed_url = urlparse(encrypted_db_url)
        
        # 检查URL是否包含用户名和密码
        if '@' in parsed_url.netloc:
            auth, host = parsed_url.netloc.split('@', 1)
            if ':' in auth:
                username, encrypted_password = auth.split(':', 1)
                
                # 解密密码
                decrypted_password = decrypt_db_password(encrypted_password)
                
                # 重建URL，使用解密后的密码
                new_netloc = f"{username}:{decrypted_password}@{host}"
                decrypted_url = urlunparse((
                    parsed_url.scheme,
                    new_netloc,
                    parsed_url.path,
                    parsed_url.params,
                    parsed_url.query,
                    parsed_url.fragment
                ))
                return decrypted_url
        
        return encrypted_db_url
    except Exception as e:
        logger.error(f"数据库URL解密失败: {str(e)}")
        return encrypted_db_url

def is_encrypted_db_url(db_url: str) -> bool:
    """检查数据库URL是否已加密"""
    if not db_url:
        return False
    
    try:
        parsed_url = urlparse(db_url)
        if '@' in parsed_url.netloc:
            auth, _ = parsed_url.netloc.split('@', 1)
            if ':' in auth:
                _, password = auth.split(':', 1)
                return password.startswith("ENC:")
        return False
    except:
        return False 