import re
from functools import wraps
from typing import Callable, Any, TypeVar, cast, List, Tuple
from fastapi import HTTPException, status

from app.core.logging import get_logger
from app.utils.crypto import encrypt_data as _encrypt_data
from app.utils.crypto import decrypt_data as _decrypt_data

# 配置日志
logger = get_logger(__name__)

# 中国大陆手机号正则表达式
MOBILE_PATTERN = re.compile(r'^1[3-9]\d{9}$')

# 类型变量，用于泛型函数返回类型
T = TypeVar('T')

class MobileDecryptionError(Exception):
    """手机号解密异常"""
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)

def validate_mobile(mobile: str) -> bool:
    """
    验证手机号格式是否正确
    
    Args:
        mobile: 手机号码
        
    Returns:
        bool: 手机号格式是否有效
    """
    if not mobile:
        return False
    return bool(MOBILE_PATTERN.match(mobile))

def encrypt_data(mobile: str) -> str:
    """
    加密手机号码，统一的加密入口
    
    Args:
        mobile: 原始手机号
        
    Returns:
        str: 加密后的手机号（Base64编码），失败返回空字符串
    """
    if not mobile:
        logger.debug("尝试加密空手机号")
        return ""
    
    try:
        return _encrypt_data(mobile)
    except Exception as e:
        logger.error(f"手机号加密失败: {str(e)}")
        return ""

def decrypt_data(encrypted_mobile: str, validate: bool = True) -> str:
    """
    解密手机号码，统一的解密入口
    
    Args:
        encrypted_mobile: 加密后的手机号（Base64编码）
        validate: 是否验证解密后的手机号格式
        
    Returns:
        str: 解密后的原始手机号，失败返回空字符串
    """
    if not encrypted_mobile:
        logger.debug("尝试解密空手机号")
        return ""
    
    try:
        decrypted = _decrypt_data(encrypted_mobile)
        if validate and decrypted and not validate_mobile(decrypted):
            logger.warning(f"解密后的手机号格式无效")
            return ""
        return decrypted
    except Exception as e:
        logger.error(f"手机号解密失败: {str(e)}")
        return ""

def with_mobile_decryption(field_name: str, validate: bool = True, strict: bool = False) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    装饰器：自动解密请求中的手机号字段
    
    Args:
        field_name: 请求对象中手机号字段的名称
        validate: 是否验证解密后的手机号格式
        strict: 是否严格模式，严格模式下解密失败会抛出异常中断请求
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            # 查找请求对象
            request = None
            request_index = -1
            
            # 在位置参数中查找请求对象
            for i, arg in enumerate(args):
                if hasattr(arg, field_name):
                    request = arg
                    request_index = i
                    break
            
            # 在关键字参数中查找请求对象
            request_key = None
            if not request:
                for key, arg in kwargs.items():
                    if hasattr(arg, field_name):
                        request = arg
                        request_key = key
                        break
            
            # 如果找到请求对象并且有手机号字段
            if request and hasattr(request, field_name):
                encrypted_mobile = getattr(request, field_name)
                if encrypted_mobile:
                    try:
                        decrypted_mobile = decrypt_data(encrypted_mobile, validate)
                        if decrypted_mobile:
                            # 创建请求对象的副本并替换手机号
                            request_dict = request.model_dump()
                            request_dict[field_name] = decrypted_mobile
                            
                            # 使用新的请求对象替换原始参数
                            if request_index >= 0:
                                # 创建args的可变副本
                                args_list = list(args)
                                args_list[request_index] = request.__class__(**request_dict)
                                # 重新构建args元组
                                args = tuple(args_list)
                            elif request_key:
                                kwargs[request_key] = request.__class__(**request_dict)
                        else:
                            error_msg = f"手机号解密失败或格式无效: {encrypted_mobile}"
                            logger.warning(error_msg)
                            if strict:
                                raise HTTPException(
                                    status_code=status.HTTP_400_BAD_REQUEST,
                                    detail="手机号格式不正确"
                                )
                    except Exception as e:
                        error_msg = f"处理手机号解密时出错: {str(e)}"
                        logger.error(error_msg)
                        if strict:
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail="手机号解密失败"
                            )
            
            return await func(*args, **kwargs)
        return cast(Callable[..., T], wrapper)
    return decorator

def handle_mobile_response(response_data: dict, field_name: str = "mobile") -> dict:
    """
    处理响应数据中的手机号字段，进行加密
    
    Args:
        response_data: 响应数据字典
        field_name: 手机号字段的名称
        
    Returns:
        处理后的响应数据
    """
    if not response_data or field_name not in response_data:
        return response_data
    
    if response_data[field_name]:
        response_data[field_name] = encrypt_data(response_data[field_name])
    
    return response_data 