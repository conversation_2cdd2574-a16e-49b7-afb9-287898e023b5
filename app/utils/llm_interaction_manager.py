from typing import List, Any, Optional
from app.core.config import settings
from app.utils.enum import ManagerScene
import time
from datetime import datetime
from app.core.logging import get_logger
import json

logger = get_logger(__name__)

class Data:
    def __init__(self, status: str, timestamp: int):
        self.status = status
        self.timestamp = timestamp

    def to_dict(self):
        return {"status": self.status, "timestamp": self.timestamp}

    @staticmethod
    def from_dict(data: dict):
        return Data(timestamp=data["timestamp"], status=data["status"])


class InteractionStatusManager:
    def __init__(self, redis: Any, ttl_seconds: int = settings.LLM_INTERACTION_DATA_EXPIRE_TIME, app_instance_id: str = settings.APP_INSTANCE_ID):
        self.redis = redis
        self.ttl_seconds = ttl_seconds
        self.key_prefix = "interaction_status"
        self.app_instance_id = app_instance_id
        logger.info("InteractionStatusManager初始化完成")

    def _make_key(self, scene: ManagerScene, id: str, app_instance_id: Optional[str] = None) -> str:
        app_id = app_instance_id or self.app_instance_id
        return f"{self.key_prefix}:{str(id)}:{scene.value}:{app_id}"

    async def start_interaction(self, scene: ManagerScene, id: str):
        """
        标记交互开始，设置状态和过期时间
        """
        key = self._make_key(scene, id)
        try:
            logger.info(f"key: {key}开始于第三方大模型交互")
            data = Data("active", int(time.time()))
            await self.redis.set(key, json.dumps(data.to_dict()), ex=self.ttl_seconds)
            logger.info(f"key:{key}已经redis存储成功")
        except Exception as e:
            logger.error(f"key: {key}存储到redis失败：{str(e)}")
    async def keep_alive(self, scene: ManagerScene, id: str, app_instance_id: Optional[str] = None):
        """
        刷新交互状态 TTL，续期
        """
        try:
            key = None
            if app_instance_id:
                key = self._make_key(scene, id, app_instance_id)
                await self.redis.expire(key, self.ttl_seconds)
                logger.info(f"key:{key}刷新交互状态 TTL续期成功")
            else:
                cursor = 0
                key = f"{self.key_prefix}:{str(id)}:{scene.value}:*"
                while True:
                    cursor, keys = await self.redis.scan(cursor=cursor, match=key, count=100)
                    for item in keys:
                        await self.redis.expire(item, self.ttl_seconds)
                        logger.info(f"key:{item}刷新交互状态 TTL续期成功")
                    if cursor == 0:
                        break
        except Exception as e:
            logger.error(f"key:{key}刷新交互状态 TTL续期失败:{str(e)}")


    async def end_interaction(self, scene: ManagerScene, id: str, app_instance_id: Optional[str] = None):
        """
        结束交互，删除状态key
        """
        try:
            key = None
            if app_instance_id:
                key = self._make_key(scene=scene, id=id, app_instance_id=app_instance_id)
                await self.redis.delete(key)
                logger.info(f"key:{key}删除成功")
                return True
            else:
                key = f"{self.key_prefix}:{str(id)}:{scene.value}:*"
                
                cursor = 0
                while True:
                    cursor, keys = await self.redis.scan(cursor=cursor, match=key, count=100)
                    if keys:
                        await self.redis.delete(*keys)  # 或者用 await redis.unlink(*keys)
                        logger.info(f"key:{key}删除成功")
                    if cursor == 0:
                        break
                return True
        except Exception as e:
            logger.error(f"key:{key}删除失败:{str(e)}")
            return False

    async def batch_end_interaction(self):
        """
            批量移除本应用实例的大模型交互标识数据
        """
        pattern = f"{self.key_prefix}:*:{self.app_instance_id}"
        cursor = 0

        while True:
            cursor, keys = await self.redis.scan(cursor=cursor, match=pattern, count=100)
            logger.info(f"批量移除异步任务成功")
            if keys:
                await self.redis.delete(*keys)  # 或者用 await redis.unlink(*keys)
            if cursor == 0:
                break
        return True
    async def is_active(self, scene: ManagerScene, id: str, app_instance_id: Optional[str] = None) -> bool:
        """
        查询某个交互状态是否活跃
        """
        try:
            key = None
            if app_instance_id:
                key = self._make_key(scene, id, app_instance_id)
                return (await self.redis.exists(key)) == 1
            else:
                key = f"{self.key_prefix}:{id}:{scene.value}:*"
                cursor = 0
                result = False
                while True:
                    cursor, keys = await self.redis.scan(cursor=cursor, match=key, count=100)
                    if keys:
                        result = True
                        break
                    if cursor == 0:
                        break
                return result
        except Exception as e:
            logger.error(f"key:{key}的状态查询失败: {str(e)}")
            return False

    async def get_instance_interactions(self, app_instance_id: Optional[str] = None) -> List[str]:
        """
        查询某个应用实例所有活跃的交互（返回 key 列表，格式：scene:platform）
        """
        pattern = f"{self.key_prefix}:*:{app_instance_id}" if app_instance_id else f"{self.key_prefix}:*"
        logger.info(f"查询应用的大模型交互的模式：{pattern}")
        cursor = "0"
        active_list = []
        try:
            while True:
                logger.info(f"instance:{pattern}")
                cursor, keys = await self.redis.scan(cursor=cursor, match=pattern, count=100)
                logger.info(f"keys: {keys}")
                logger.info(f"cursor: {cursor}")
                for key in keys:
                    key_str = key.decode("utf-8") if isinstance(key, bytes) else str(key)
                    value = await self.redis.get(key_str)
                    try:
                        dict_data = json.loads(value)
                    except Exception as e:
                        logger.error(f"key:{key_str}JSON反序列化失败：{str(e)}")
                        dict_data = {
                            "status": "active",
                            "timestamp": int(time.time())
                        }
                    logger.info(f"dict_data: {dict_data}")
                    data = Data.from_dict(dict_data)
                    parts = key_str.split(":")
                    if len(parts) == 4:
                        _, project, scene, instance_id= parts
                        active_list.append({
                            "scene": scene,
                            "business_id": project,
                            "instance_id": instance_id,
                            "created_at": datetime.fromtimestamp(data.timestamp).strftime("%Y-%m-%d %H:%M:%S")
                        })
                if cursor == 0:
                    break
            logger.info(f"应用实例的大模型调用场景信息：{active_list}")
            return active_list
        except Exception as e:
            logger.error(f"查询使用实例的大模型交互状态列表失败：{str(e)}")

