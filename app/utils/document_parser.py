"""
文档解析工具模块
支持多种文档格式的文本提取功能
"""

import os
import platform
import sys
import tempfile
import subprocess
import zipfile
from typing import List, Optional, Dict, Any, TypedDict
from app.core.logging import get_logger

# 获取logger实例
logger = get_logger(__name__)

# 自定义异常类
class DocumentParseError(Exception):
    """文档解析基础异常"""
    pass

class UnsupportedFileTypeError(DocumentParseError):
    """不支持的文件类型异常"""
    pass

class FileNotFoundError(DocumentParseError):
    """文件不存在异常"""
    pass

class EncodingError(DocumentParseError):
    """编码错误异常"""
    pass

# 文件信息类型定义
class FileInfo(TypedDict):
    filename: str
    file_path: str
    file_extension: str
    file_size: int
    is_supported: bool

# 解析器配置类
class ParserConfig:
    """文档解析器配置"""
    
    def __init__(self):
        self._check_dependencies()
    
    def _check_dependencies(self):
        """检查依赖库的可用性"""
        self.docx_available = self._check_docx()
        self.pdf_available = self._check_pdf()
        self.doc_parser_type = self._check_doc_parser()
    
    def _check_docx(self) -> bool:
        try:
            from docx import Document
            return True
        except ImportError:
            return False
    
    def _check_pdf(self) -> bool:
        try:
            import PyPDF2
            return True
        except ImportError:
            return False
    
    def _check_doc_parser(self) -> Optional[str]:
        try:
            import docx2txt
            return "docx2txt"
        except ImportError:
            if platform.system() == "Windows":
                try:
                    import win32com.client
                    return "win32com"
                except ImportError:
                    pass
        return None

# 文档解析相关导入
try:
    from docx import Document  # python-docx
except ImportError:
    Document = None

try:
    import PyPDF2  # PyPDF2
except ImportError:
    PyPDF2 = None

# 跨平台的 .doc 文件处理
try:
    import docx2txt  # docx2txt for .doc files (cross-platform)
    DOC_PARSER_AVAILABLE = True
    DOC_PARSER_TYPE = "docx2txt"
except ImportError:
    docx2txt = None
    DOC_PARSER_AVAILABLE = False
    DOC_PARSER_TYPE = None
    
    # 如果是 Windows 系统，尝试使用 win32com 作为备选
    if platform.system() == "Windows":
        try:
            import win32com.client  # pywin32 for .doc files (Windows only)
            DOC_PARSER_AVAILABLE = True
            DOC_PARSER_TYPE = "win32com"
        except ImportError:
            win32com = None


# 支持的文件扩展名
SUPPORTED_EXTENSIONS = ['.txt', '.md', '.pdf', '.docx', '.doc']


def extract_text_from_pdf(file_path: str) -> str:
    """从PDF文件提取文本"""
    if PyPDF2 is None:
        raise DocumentParseError("PyPDF2库未安装，无法处理PDF文件")
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"PDF文件不存在: {file_path}")
    
    text = ""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
    except PyPDF2.errors.PdfReadError as e:
        raise DocumentParseError(f"PDF文件损坏或格式错误: {str(e)}")
    except Exception as e:
        raise DocumentParseError(f"PDF文件解析失败: {str(e)}")
    
    return text.strip()


def extract_text_from_docx(file_path: str) -> str:
    """从DOCX文件提取文本"""
    if Document is None:
        raise DocumentParseError("python-docx库未安装，无法处理DOCX文件")
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"DOCX文件不存在: {file_path}")
    
    try:
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            if paragraph.text:
                text += paragraph.text + "\n"
        return text.strip()
    except Exception as e:
        raise DocumentParseError(f"DOCX文件解析失败: {str(e)}")


def extract_text_from_doc(file_path: str) -> str:
    """从DOC文件提取文本 - 跨平台支持"""
    if not DOC_PARSER_AVAILABLE:
        raise Exception("未安装DOC文件解析库。请安装 docx2txt (推荐) 或 pywin32 (仅Windows)")
    
    try:
        if DOC_PARSER_TYPE == "docx2txt":
            # 使用跨平台的 docx2txt 库
            text = docx2txt.process(file_path)
            return text.strip() if text else ""
            
        elif DOC_PARSER_TYPE == "win32com":
            # 使用Windows COM组件处理DOC文件
            word = win32com.client.Dispatch("Word.Application")
            word.visible = False
            
            doc = word.Documents.Open(file_path)
            text = doc.Range().Text
            doc.Close()
            word.Quit()
            
            return text.strip()
        else:
            raise Exception("没有可用的DOC文件解析器")
            
    except Exception as e:
        raise Exception(f"DOC文件解析失败: {str(e)}")


def extract_text_from_text_file(file_path: str) -> str:
    """从文本文件提取内容，使用智能编码检测"""
    try:
        # 首先尝试UTF-8编码
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        # 如果UTF-8失败，尝试其他常见编码
        encodings_to_try = ['gbk', 'gb2312', 'gb18030', 'latin1', 'cp1252']
        
        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    logger.info(f"文本文件使用 {encoding} 编码成功解析")
                    return content
            except UnicodeDecodeError:
                continue
        
        # 最后尝试忽略错误的方式解码
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                logger.warning("使用UTF-8忽略错误模式解析文本文件")
                return content
        except Exception:
            raise Exception("文本文件编码格式不支持，请确保文件是常见的文本编码格式")


def extract_text_from_file(file_path: str, file_ext: Optional[str] = None) -> str:
    """
    根据文件扩展名提取文本内容
    
    Args:
        file_path: 文件路径
        file_ext: 文件扩展名（可选，如果不提供则从文件路径中提取）
        
    Returns:
        提取的文本内容
        
    Raises:
        Exception: 当文件格式不支持或解析失败时
    """
    if file_ext is None:
        file_ext = os.path.splitext(file_path)[1].lower()
    else:
        file_ext = file_ext.lower()
    
    if file_ext == '.pdf':
        return extract_text_from_pdf(file_path)
    elif file_ext == '.docx':
        return extract_text_from_docx(file_path)
    elif file_ext == '.doc':
        return extract_text_from_doc(file_path)
    elif file_ext in ['.txt', '.md']:
        return extract_text_from_text_file(file_path)
    else:
        raise Exception(f"不支持的文件格式: {file_ext}")


def is_supported_file_type(filename: str) -> bool:
    """
    检查文件类型是否支持
    
    Args:
        filename: 文件名
        
    Returns:
        是否支持该文件类型
    """
    if not filename:
        return False
    
    file_ext = os.path.splitext(filename)[1].lower()
    return file_ext in SUPPORTED_EXTENSIONS


def get_supported_extensions() -> List[str]:
    """
    获取所有支持的文件扩展名
    
    Returns:
        支持的文件扩展名列表
    """
    return SUPPORTED_EXTENSIONS.copy()


def validate_file_extension(filename: str, raise_exception: bool = True) -> bool:
    """
    验证文件扩展名是否支持
    
    Args:
        filename: 文件名
        raise_exception: 是否在不支持时抛出异常
        
    Returns:
        是否支持该文件类型
        
    Raises:
        UnsupportedFileTypeError: 当文件类型不支持且raise_exception为True时
    """
    if not filename:
        if raise_exception:
            raise UnsupportedFileTypeError("文件名不能为空")
        return False
    
    file_ext = os.path.splitext(filename)[1].lower()
    is_supported = file_ext in SUPPORTED_EXTENSIONS
    
    if not is_supported and raise_exception:
        raise UnsupportedFileTypeError(f"不支持的文件类型 {file_ext}，支持的格式: {', '.join(SUPPORTED_EXTENSIONS)}")
    
    return is_supported


class DocumentParser:
    """文档解析器类，提供面向对象的接口"""
    
    def __init__(self):
        self.supported_extensions = SUPPORTED_EXTENSIONS
    
    def parse_file(self, file_path: str, file_ext: Optional[str] = None) -> str:
        """
        解析文件并提取文本内容
        
        Args:
            file_path: 文件路径
            file_ext: 文件扩展名（可选）
            
        Returns:
            提取的文本内容
        """
        return extract_text_from_file(file_path, file_ext)
    
    def is_supported(self, filename: str) -> bool:
        """检查文件类型是否支持"""
        return is_supported_file_type(filename)
    
    def validate_file(self, filename: str, raise_exception: bool = True) -> bool:
        """验证文件扩展名"""
        return validate_file_extension(filename, raise_exception)
    
    def get_file_info(self, file_path: str) -> FileInfo:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        filename = os.path.basename(file_path)
        file_ext = os.path.splitext(filename)[1].lower()
        file_size = os.path.getsize(file_path)
        
        return FileInfo(
            filename=filename,
            file_path=file_path,
            file_extension=file_ext,
            file_size=file_size,
            is_supported=self.is_supported(filename)
        )


# 创建默认的文档解析器实例
default_parser = DocumentParser() 

def validate_converted_docx(docx_path: str) -> bool:
    """
    验证转换后的 .docx 文件是否有效
    
    Args:
        docx_path: .docx 文件路径
        
    Returns:
        True 如果文件有效，False 否则
    """
    try:
        # 检查文件大小
        file_size = os.path.getsize(docx_path)
        if file_size == 0:
            logger.warning(f"转换后的文件大小为0: {docx_path}")
            return False
        
        # 尝试使用 python-docx 打开文件
        from docx import Document
        doc = Document(docx_path)
        
        # 检查是否有内容
        if len(doc.paragraphs) == 0 and len(doc.tables) == 0:
            logger.warning(f"转换后的文件没有内容: {docx_path}")
            return False
        
        logger.info(f"转换后的 .docx 文件验证成功，段落数: {len(doc.paragraphs)}, 表格数: {len(doc.tables)}")
        return True
        
    except Exception as e:
        logger.warning(f"转换后的 .docx 文件验证失败: {str(e)}")
        return False

def check_libreoffice_available() -> bool:
    """
    检查 LibreOffice 是否可用
    
    Returns:
        True 如果 LibreOffice 可用，False 否则
    """
    try:
        if sys.platform.startswith('win'):
            # Windows 系统下查找 LibreOffice 可执行文件
            possible_paths = [
                r"C:\Program Files\LibreOffice\program\soffice.exe",
                r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
                # 添加其他可能的安装路径
            ]
            
            # 检查是否存在可执行文件
            for path in possible_paths:
                if os.path.exists(path):
                    logger.info(f"在 Windows 上找到 LibreOffice: {path}")
                    return True
                    
            logger.warning("在 Windows 上未找到 LibreOffice 可执行文件")
            return False
        else:
            # Linux/Mac 系统使用命令行检查
            result = subprocess.run(
                ['libreoffice', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                version = result.stdout.strip()
                logger.info(f"LibreOffice 可用，版本: {version}")
                return True
            else:
                logger.warning(f"LibreOffice 版本检查失败: {result.stderr}")
                return False
    except Exception as e:
        logger.warning(f"LibreOffice 可用性检查失败: {str(e)}")
        return False

def sanitize_file_path(file_path: str) -> str:
    """
    安全化文件路径，防止路径遍历攻击
    
    Args:
        file_path: 待验证的文件路径
        
    Returns:
        安全的文件路径
        
    Raises:
        ValueError: 文件路径包含非法字符或超出允许范围
    """
    if not file_path:
        raise ValueError("文件路径不能为空")
    
    # 规范化路径，移除多余的分隔符和相对路径组件
    normalized_path = os.path.normpath(file_path)
    
    # 检查是否包含路径遍历字符
    if '..' in normalized_path.split(os.sep):
        raise ValueError("文件路径包含非法的路径遍历字符")
    
    # 转换为绝对路径进行进一步验证
    abs_path = os.path.abspath(normalized_path)
    
    # 检查文件是否存在
    if not os.path.exists(abs_path):
        raise FileNotFoundError(f"文件不存在: {abs_path}")
    
    # 检查是否为文件（而非目录）
    if not os.path.isfile(abs_path):
        raise ValueError("路径必须指向一个文件")
    
    return abs_path

def validate_filename(filename: str) -> str:
    """
    验证和清理文件名，防止恶意文件名
    
    Args:
        filename: 原始文件名
        
    Returns:
        安全的文件名
        
    Raises:
        ValueError: 文件名包含非法字符或为空
    """
    if not filename:
        raise ValueError("文件名不能为空")
    
    # 定义允许的字符集（字母、数字、点、下划线、连字符）
    # safe_chars = set("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789._-")
    # safe_filename = "".join(c for c in filename if c in safe_chars)
    
    # if not safe_filename:
    #     raise ValueError("文件名包含非法字符")
    #
    # 限制文件名长度，防止过长文件名
    # if len(filename) > 100:
    #     name, ext = os.path.splitext(filename)
    #     filename = name[:95] + ext
    
    # 确保文件名不以点开头（防止隐藏文件）
    if filename.startswith('.'):
        raise ValueError("文件名不以点开头")
    
    return filename

def get_safe_libreoffice_path() -> str:
    """
    安全获取 LibreOffice 可执行文件路径
    
    Returns:
        LibreOffice 可执行文件的安全路径
        
    Raises:
        Exception: 未找到 LibreOffice 或路径不安全
    """
    if sys.platform.startswith('win'):
        # Windows 系统下的预定义安全路径
        possible_paths = [
            r"C:\Program Files\LibreOffice\program\soffice.exe",
            r"C:\Program Files (x86)\LibreOffice\program\soffice.exe"
        ]
        
        for path in possible_paths:
            if os.path.exists(path) and os.path.isfile(path):
                # 验证路径安全性
                abs_path = os.path.abspath(path)
                if abs_path in [os.path.abspath(p) for p in possible_paths]:
                    return abs_path
        
        raise Exception("未找到安全的 LibreOffice 可执行文件")
    else:
        # Linux/Mac 系统使用系统路径中的 libreoffice
        return 'libreoffice'

def convert_doc_to_docx_with_libreoffice(doc_file_path: str) -> str:
    """
    使用 LibreOffice 将 .doc 文件转换为 .docx 格式（安全版本）
    
    Args:
        doc_file_path: .doc 文件路径
        
    Returns:
        转换后的 .docx 文件路径
        
    Raises:
        Exception: 转换失败时抛出异常
    """
    converted_docx_path = None
    temp_output_dir = None
    
    try:
        # 检查 LibreOffice 是否可用
        if not check_libreoffice_available():
            raise Exception("LibreOffice 未安装或不可用，请先安装 LibreOffice")
        
        # 安全验证输入文件路径
        try:
            safe_input_path = sanitize_file_path(doc_file_path)
        except (ValueError, FileNotFoundError) as e:
            raise Exception(f"输入文件路径验证失败: {str(e)}")
        
        logger.info(f"开始使用 LibreOffice 转换 .doc 文件: {safe_input_path}")
        
        # 创建专用的临时输出目录，避免文件名冲突
        temp_output_dir = tempfile.mkdtemp(prefix="libreoffice_convert_", suffix="_temp")
        logger.debug(f"创建临时输出目录: {temp_output_dir}")
        
        # 获取安全的文件名
        original_filename = os.path.basename(safe_input_path)
        try:
            safe_filename = validate_filename(original_filename)
        except ValueError as e:
            raise Exception(f"文件名验证失败: {str(e)}")
        
        name_without_ext = os.path.splitext(safe_filename)[0]
        
        # 获取安全的 LibreOffice 路径
        try:
            soffice_path = get_safe_libreoffice_path()
        except Exception as e:
            raise Exception(f"获取 LibreOffice 路径失败: {str(e)}")
        
        # 构建安全的转换命令
        cmd = [
            soffice_path,
            '--headless',
            '--convert-to', 'docx',
            '--outdir', temp_output_dir,
            safe_input_path
        ]
        
        logger.info(f"执行 LibreOffice 转换命令: {' '.join(['***' if 'soffice' in arg else arg for arg in cmd])}")
        
        # 执行转换命令，添加更多安全限制
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120,  # 增加超时时间到2分钟
            cwd=temp_output_dir,  # 限制工作目录
            env={'PATH': os.environ.get('PATH', ''), 'HOME': os.environ.get('HOME', '')},  # 限制环境变量
        )
        
        if result.returncode == 0:
            # 转换成功，查找生成的 .docx 文件
            expected_docx_path = os.path.join(temp_output_dir, f"{name_without_ext}.docx")
            
            if os.path.exists(expected_docx_path):
                logger.info(f"LibreOffice 转换成功，生成文件: {expected_docx_path}")
                
                # 验证转换后的文件
                if validate_converted_docx(expected_docx_path):
                    converted_docx_path = expected_docx_path
                    return converted_docx_path
                else:
                    raise Exception("转换后的 .docx 文件验证失败")
            else:
                # 查找可能的输出文件（安全遍历）
                try:
                    output_files = []
                    for filename in os.listdir(temp_output_dir):
                        if (filename.endswith('.docx') and 
                            name_without_ext in filename and
                            len(filename) < 200):  # 限制文件名长度
                            output_files.append(filename)
                    
                    if output_files:
                        actual_docx_path = os.path.join(temp_output_dir, output_files[0])
                        logger.info(f"找到转换后的文件: {actual_docx_path}")
                        
                        if validate_converted_docx(actual_docx_path):
                            converted_docx_path = actual_docx_path
                            return converted_docx_path
                        else:
                            raise Exception("转换后的 .docx 文件验证失败")
                    else:
                        raise Exception("未找到转换后的 .docx 文件")
                except OSError as e:
                    raise Exception(f"读取输出目录失败: {str(e)}")
        else:
            error_msg = f"LibreOffice 转换失败，返回码: {result.returncode}"
            if result.stderr:
                # 过滤敏感信息
                filtered_stderr = result.stderr.replace(safe_input_path, '[FILE_PATH]')
                error_msg += f", 错误信息: {filtered_stderr}"
            if result.stdout:
                filtered_stdout = result.stdout.replace(safe_input_path, '[FILE_PATH]')
                error_msg += f", 输出信息: {filtered_stdout}"
            raise Exception(error_msg)
            
    except subprocess.TimeoutExpired:
        logger.warning(f"LibreOffice 转换超时: {doc_file_path}")
        raise Exception("LibreOffice 转换超时，请检查文件大小和复杂度")
    except FileNotFoundError as e:
        logger.error(f"LibreOffice 命令未找到: {str(e)}")
        raise Exception("LibreOffice 命令未找到，请确保 LibreOffice 已正确安装并添加到 PATH")
    except Exception as e:
        logger.error(f"LibreOffice 转换失败: {str(e)}")
        raise Exception(f"文档转换失败: {str(e)}")
    finally:
        # 重要：在 finally 块中清理临时资源
        # 注意：只有在转换失败时才清理转换后的文件，成功时由调用方清理
        if temp_output_dir and os.path.exists(temp_output_dir):
            try:
                # 如果转换失败，清理整个临时目录
                if converted_docx_path is None:
                    import shutil
                    shutil.rmtree(temp_output_dir)
                    logger.debug(f"已清理失败转换的临时目录: {temp_output_dir}")
                else:
                    # 如果转换成功，只清理除了转换结果外的其他文件
                    for filename in os.listdir(temp_output_dir):
                        file_path = os.path.join(temp_output_dir, filename)
                        if file_path != converted_docx_path and os.path.isfile(file_path):
                            try:
                                os.remove(file_path)
                            except:
                                pass
                    logger.debug(f"已清理临时目录中的中间文件: {temp_output_dir}")
            except Exception as cleanup_error:
                logger.warning(f"清理临时文件失败: {str(cleanup_error)}") 