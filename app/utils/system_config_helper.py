from typing import Optional, Union, List
import sys
from app.services.system_config_service import system_config_service
from app.core.logging import get_logger
import time

logger = get_logger(__name__)


class SafeTypeConverter:
    """安全类型转换器，提供边界值验证"""
    
    @staticmethod
    def safe_int(value: Union[str, int, float], default: int = 0, min_val: int = None, max_val: int = None) -> int:
        """
        安全整数转换
        
        Args:
            value: 要转换的值
            default: 默认值
            min_val: 最小值限制
            max_val: 最大值限制
            
        Returns:
            转换后的整数
        """
        try:
            result = int(value)
            
            # 检查整数溢出
            if result > sys.maxsize or result < -sys.maxsize - 1:
                logger.warning(f"值 '{value}' 超出整数范围，使用默认值 {default}")
                return default
            
            # 检查最小值限制
            if min_val is not None and result < min_val:
                logger.warning(f"值 {result} 小于最小值 {min_val}，使用默认值 {default}")
                return default
            
            # 检查最大值限制
            if max_val is not None and result > max_val:
                logger.warning(f"值 {result} 大于最大值 {max_val}，使用默认值 {default}")
                return default
            
            return result
        except (ValueError, TypeError, OverflowError):
            logger.warning(f"值 '{value}' 无法转换为整数，使用默认值 {default}")
            return default
    
    @staticmethod
    def safe_float(value: Union[str, int, float], default: float = 0.0, min_val: float = None, max_val: float = None) -> float:
        """
        安全浮点数转换
        
        Args:
            value: 要转换的值
            default: 默认值
            min_val: 最小值限制
            max_val: 最大值限制
            
        Returns:
            转换后的浮点数
        """
        try:
            result = float(value)
            
            # 检查浮点数溢出
            if result == float('inf') or result == float('-inf'):
                logger.warning(f"值 '{value}' 超出浮点数范围，使用默认值 {default}")
                return default
            
            # 检查NaN
            if result != result:  # NaN检查
                logger.warning(f"值 '{value}' 是NaN，使用默认值 {default}")
                return default
            
            # 检查最小值限制
            if min_val is not None and result < min_val:
                logger.warning(f"值 {result} 小于最小值 {min_val}，使用默认值 {default}")
                return default
            
            # 检查最大值限制
            if max_val is not None and result > max_val:
                logger.warning(f"值 {result} 大于最大值 {max_val}，使用默认值 {default}")
                return default
            
            return result
        except (ValueError, TypeError, OverflowError):
            logger.warning(f"值 '{value}' 无法转换为浮点数，使用默认值 {default}")
            return default
    
    @staticmethod
    def safe_bool(value: Union[str, int, bool], default: bool = False) -> bool:
        """
        安全布尔值转换
        
        Args:
            value: 要转换的值
            default: 默认值
            
        Returns:
            转换后的布尔值
        """
        if isinstance(value, bool):
            return value
        
        if isinstance(value, (int, float)):
            return bool(value)
        
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on', 'enabled')
        
        return default
    
    @staticmethod
    def safe_str(value: Union[str, int, float, bool], default: str = "", max_length: int = 1000) -> str:
        """
        安全字符串转换
        
        Args:
            value: 要转换的值
            default: 默认值
            max_length: 最大长度限制
            
        Returns:
            转换后的字符串
        """
        try:
            result = str(value)
            
            # 检查长度限制
            if len(result) > max_length:
                logger.warning(f"字符串长度 {len(result)} 超过限制 {max_length}，使用默认值 {default}")
                return default
            
            return result
        except Exception:
            logger.warning(f"值 '{value}' 无法转换为字符串，使用默认值 {default}")
            return default


class SystemConfigHelper:
    """系统配置辅助工具类"""
    
    @staticmethod
    async def get_config(key: str, default: str = None, use_cache: bool = True) -> str:
        """
        获取系统配置值
        
        Args:
            key: 配置键
            default: 默认值
            use_cache: 是否使用缓存
            
        Returns:
            配置值，如果不存在则返回默认值
        """
        if not key:
            logger.warning("配置键为空，返回默认值")
            return default or ""
        
        try:
            value = await system_config_service.get_config(key, use_cache)
            return value if value is not None else (default or "")
        except Exception as e:
            logger.error(f"获取配置 {key} 时出错: {e}")
            return default or ""
    
    @staticmethod
    async def get_int_config(key: str, default: int = 0, use_cache: bool = True, min_val: int = None, max_val: int = None) -> int:
        """
        获取整数类型的系统配置值
        
        Args:
            key: 配置键
            default: 默认值
            use_cache: 是否使用缓存
            min_val: 最小值限制
            max_val: 最大值限制
            
        Returns:
            整数配置值
        """
        if not key:
            logger.warning("配置键为空，返回默认值")
            return default
        
        try:
            value = await system_config_service.get_config(key, use_cache)
            if value is None:
                return default
            return SafeTypeConverter.safe_int(value, default, min_val, max_val)
        except Exception as e:
            logger.error(f"获取整数配置 {key} 时出错: {e}")
            return default
    
    @staticmethod
    async def get_float_config(key: str, default: float = 0.0, use_cache: bool = True, min_val: float = None, max_val: float = None) -> float:
        """
        获取浮点数类型的系统配置值
        
        Args:
            key: 配置键
            default: 默认值
            use_cache: 是否使用缓存
            min_val: 最小值限制
            max_val: 最大值限制
            
        Returns:
            浮点数配置值
        """
        if not key:
            logger.warning("配置键为空，返回默认值")
            return default
        
        try:
            value = await system_config_service.get_config(key, use_cache)
            if value is None:
                return default
            return SafeTypeConverter.safe_float(value, default, min_val, max_val)
        except Exception as e:
            logger.error(f"获取浮点数配置 {key} 时出错: {e}")
            return default
    
    @staticmethod
    async def get_bool_config(key: str, default: bool = False, use_cache: bool = True) -> bool:
        """
        获取布尔类型的系统配置值
        
        Args:
            key: 配置键
            default: 默认值
            use_cache: 是否使用缓存
            
        Returns:
            布尔配置值
        """
        if not key:
            logger.warning("配置键为空，返回默认值")
            return default
        
        try:
            value = await system_config_service.get_config(key, use_cache)
            if value is None:
                return default
            return SafeTypeConverter.safe_bool(value, default)
        except Exception as e:
            logger.error(f"获取布尔配置 {key} 时出错: {e}")
            return default
    
    @staticmethod
    async def get_list_config(key: str, default: List[str] = None, separator: str = ',', use_cache: bool = True) -> List[str]:
        """
        获取列表类型的系统配置值（字符串分割）
        
        Args:
            key: 配置键
            default: 默认值
            separator: 分隔符
            use_cache: 是否使用缓存
            
        Returns:
            列表配置值
        """
        if not key:
            logger.warning("配置键为空，返回默认值")
            return default or []
        
        try:
            value = await system_config_service.get_config(key, use_cache)
            if value is None:
                return default or []
            return [item.strip() for item in value.split(separator) if item.strip()]
        except Exception as e:
            logger.error(f"获取列表配置 {key} 时出错: {e}")
            return default or []
    
    @staticmethod
    async def set_config(key: str, value: Union[str, int, float, bool], default_value: Union[str, int, float, bool] = None, category: str = None, description: str = None, order_no: int = None) -> bool:
        """
        设置系统配置值
        
        Args:
            key: 配置键
            value: 配置值
            default_value: 默认值
            category: 配置分类
            description: 配置描述
            order_no: 排序号
            
        Returns:
            是否设置成功
        """
        if not key:
            logger.error("配置键不能为空")
            return False
        
        try:
            # 使用安全字符串转换
            str_value = SafeTypeConverter.safe_str(value, "", 1000)
            str_default_value = SafeTypeConverter.safe_str(default_value, "", 1000) if default_value is not None else None
            
            if not str_value:
                logger.error(f"配置值转换失败: {value}")
                return False
            
            return await system_config_service.set_config(key, str_value, str_default_value, category, description, order_no)
        except Exception as e:
            logger.error(f"设置配置 {key} 时出错: {e}")
            return False
    
    @staticmethod
    async def get_configs_by_category(category: str) -> List[dict]:
        """
        根据分类获取配置列表
        
        Args:
            category: 配置分类
            
        Returns:
            配置列表
        """
        if not category:
            logger.warning("分类参数为空")
            return []
        
        try:
            return await system_config_service.get_configs_by_category(category)
        except Exception as e:
            logger.error(f"获取分类 {category} 的配置时出错: {e}")
            return []
    
    @staticmethod
    async def clear_cache():
        """清除配置缓存"""
        try:
            await system_config_service.clear_cache()
        except Exception as e:
            logger.error(f"清除配置缓存时出错: {e}")
    
    @staticmethod
    async def check_and_clear_locks():
        """检查并清理Redis中的死锁"""
        try:
            from app.core.redis_client import get_redis_client
            from app.core.cache_lock import DistributedCacheLock
            
            redis_client = get_redis_client()
            lock_manager = DistributedCacheLock(redis_client)
            
            # 检查所有以cache_lock:开头的键
            pattern = "cache_lock:*"
            keys = await redis_client.keys(pattern)
            
            if not keys:
                logger.info("没有找到任何锁")
                return
            
            logger.info(f"找到 {len(keys)} 个锁键")
            
            # 检查每个锁的状态
            for key in keys:
                try:
                    # 获取锁的值（时间戳）
                    lock_value = await redis_client.get(key)
                    if lock_value:
                        lock_time = float(lock_value)
                        current_time = time.time()
                        
                        # 如果锁超过60秒，认为是死锁
                        if current_time - lock_time > 60:
                            logger.warning(f"发现死锁: {key}，创建时间: {lock_time}")
                            # 强制释放死锁
                            resource_key = key.replace("cache_lock:", "")
                            await lock_manager.force_release_lock(resource_key)
                        else:
                            logger.debug(f"锁正常: {key}，创建时间: {lock_time}")
                            
                except Exception as e:
                    logger.error(f"检查锁 {key} 时出错: {e}")
                    
        except Exception as e:
            logger.error(f"检查和清理锁时出错: {e}")
    
    @staticmethod
    async def force_release_config_lock(key: str):
        """强制释放指定配置的锁"""
        try:
            from app.core.redis_client import get_redis_client
            from app.core.cache_lock import DistributedCacheLock
            
            redis_client = get_redis_client()
            lock_manager = DistributedCacheLock(redis_client)
            
            await lock_manager.force_release_lock(f"config_delete:{key}")
            logger.info(f"已强制释放配置锁: config_delete:{key}")
            
        except Exception as e:
            logger.error(f"强制释放配置锁时出错: {e}")


# 全局辅助工具实例
config_helper = SystemConfigHelper()

# 使用示例：
# # 获取整数配置，限制范围在1-100之间
# port = await config_helper.get_int_config("server_port", default=8080, min_val=1, max_val=65535)
# 
# # 获取浮点数配置，限制范围在0.0-1.0之间
# threshold = await config_helper.get_float_config("confidence_threshold", default=0.8, min_val=0.0, max_val=1.0)
# 
# # 获取布尔配置
# debug_mode = await config_helper.get_bool_config("debug_mode", default=False)
# 
# # 获取字符串配置
# api_key = await config_helper.get_config("api_key", default="") 