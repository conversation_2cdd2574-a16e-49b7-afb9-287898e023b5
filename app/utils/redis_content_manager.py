import json
from typing import Optional, Dict, List
from app.utils.utils import ContentStatus
from app.core.logging import get_logger
from app.core.redis_client import get_redis_client, get_llm_interaction_instance
from app.utils.enum import ManagerScene

logger = get_logger(__name__)

redis_client = get_redis_client()
llm_interaction_instance = get_llm_interaction_instance()

class Data:
    def __init__(self, content: str, status: str = ContentStatus.NORMAL.value):
        self.status = status
        self.content = content

    def to_dict(self):
        return {"status": self.status, "content": self.content}

    @staticmethod
    def from_dict(data: dict):
        return Data(content=data["content"], status=data["status"])
class ContentData:
    def __init__(self, content: Dict[str, List[Data]]):
        self.read = content["read"]
        self.unread = content["unread"]

class RedisContentManager:
    # id是数据储存对象的唯一标识符号
    def __init__(
        self,
        # 场景值
        scene: ManagerScene
    ):
        self.prefix = f"manager_"
        self.scene = scene
    def _key(
        self,
        id: str,
        suffix: str
    ) -> str:
        return f"{self.prefix}:{self.scene.value}:{str(id)}:{suffix}"

    async def add_content(
        self,
        project_id: str,
        content: str,
        status: str = ContentStatus.NORMAL.value
    ):
        data = Data(content, status)
        key = self._key(project_id, "unread")
        await redis_client.rpush(key, json.dumps(data.to_dict()))
        logger.info(f"[Redis] 添加内容到 {key}: {data.to_dict()}")

    async def read_next_chunk(self, project_id: str) -> Optional[Data]:
        unread_key = self._key(project_id, "unread")
        read_key = self._key(project_id, "read")

        raw = await redis_client.lpop(unread_key)
        if not raw:
            logger.info("未读信息里面没有内容了")
            return None

        data = Data.from_dict(json.loads(raw))
        await redis_client.rpush(read_key, json.dumps(data.to_dict()))

        if data.status == ContentStatus.ERROR.value:
            logger.warning("读取到错误信息，清除项目数据")
            await self.clear_project(project_id)
            await llm_interaction_instance.end_interaction(
                scene=self.scene,
                id=project_id
            )

        return data

    async def clear_project(self, project_id: str):
        await redis_client.delete(
            self._key(project_id, "read"),
            self._key(project_id, "unread")
        )
        logger.error(f"[Redis] 清空项目 {project_id} 的内容")

    async def get_project_content(self, project_id: str) -> ContentData:
        read_key = self._key(project_id, "read")
        unread_key = self._key(project_id, "unread")
        read = await redis_client.lrange(read_key, 0, -1)
        unread = await redis_client.lrange(unread_key, 0, -1)
        return ContentData({
            "read": [Data.from_dict(json.loads(x)) for x in (read or [])],
            "unread": [Data.from_dict(json.loads(x)) for x in (unread or [])],
        })
