import json
import aiohttp
import asyncio
import traceback
from typing import Dict, List, Optional, Callable
import os
from pydantic import UUID4
from app.core.config import settings
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.utils.constants import ProductType
from app.api.repository.llm_call_log import create_llm_call_log, update_llm_call
from app.utils.enum import ErrorType

# 获取logger实例
logger = get_logger(__name__)

async def stream_llm_and_save(
    messages: List[Dict[str, str]],
    apiKey,
    apiUrl,
    model,
    user: UserResponse,
    flag: str,
    callback: Callable[[str], None],
    complete_callback: Callable[[dict, str], None],
    error_callback: Callable[[str], None],
    related_id: Optional[UUID4] = None,
    is_thinking: Optional[bool] = False,
    product_type = ProductType.DOCGEN.value,
) -> str:
    """
    流式调用LLM，实时保存响应内容到文件，并返回完整响应
    
    Args:
        messages: 聊天消息列表
        model: 模型名称
        callback: 可选的回调函数，每接收到一个chunk就调用一次
        complete_callback: 可选的完成回调函数，生成完成时调用
    
    Returns:
        完整的模型响应文本
    """
    logger.info(f"流式调用LLM并保存到文件，模型: {model}")
    if not apiKey:
        err_msg = "未设置API_KEY环境变量"
        logger.error(err_msg)
        error_callback and await error_callback(err_msg)
        # raise ValueError("未设置API_KEY环境变量")
    logger.info(f"开始请求API: {model}")
    headers = {
        "Authorization": f"Bearer {apiKey}",
        "X-Title": "Hi-IdeaGen",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": messages,
        "stream": True,
        "enable_thinking": is_thinking,
        "temperature": settings.LLM_DEFAULT_TEMPERATURE,
        "top_k": settings.LLM_DEFAULT_TOP_K,
        "top_p": settings.LLM_DEFAULT_TOP_P,
        # 流式输出的最后一行才返回usage（百炼的特有）
        "stream_options": {
            "include_usage": True
        }
    }
    
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"流式LLM请求，超时设置: {timeout.total}秒,温度: {settings.LLM_DEFAULT_TEMPERATURE},上采样top_k: {settings.LLM_DEFAULT_TOP_K},上采样top_p: {settings.LLM_DEFAULT_TOP_P}")
    
    llm_log = await create_llm_call_log(
        current_user=user,
        model_name=model,
        model_api_key=apiKey,
        model_api_url=apiUrl,
        response="",
        messages=messages,
        product_type=product_type,
        related_id=related_id,
        flag=flag
    )
    try:
        full_response = ""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                apiUrl,
                headers=headers,
                json=payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    error_msg = f"API 错误: 状态码 {resp.status}, 错误详情: {error_text}"
                    logger.error(error_msg)
                    error_callback and await error_callback(error_msg)
                    return f"API请求错误: {error_msg}"
                
                # 流式响应并写入文件
                logger.debug(f"开始接收流式LLM响应并写入文件")
                chunk_count = 0
                token_consume = {
                    "input": 0,
                    "output": 0,
                    "open_router_id": ""
                }
                
                # 使用追加模式写入文件
                try:
                    async for line in resp.content:
                        try:
                            line_str = line.decode('utf-8').strip()
                            if not line_str:
                                continue
                            
                            # 跳过 "data: " 前缀
                            if line_str.startswith("data: "):
                                line_str = line_str[6:]
                            
                            # 处理流结束标记
                            if line_str == "[DONE]":
                                logger.debug("流式响应结束")
                                break
                            # 解析 JSON 数据
                            try:
                                data = json.loads(line_str)
                                token_consume["open_router_id"] = data.get("id")
                                # print(open_router_id)
                                if 'usage' in data and data["usage"] and data["usage"].get("prompt_tokens"):
                                    token_consume["input"] = data.get("usage").get("prompt_tokens")
                                    token_consume["output"] = data.get("usage").get("completion_tokens")
                                
                                # 提取内容增量
                                if 'choices' in data and len(data['choices']) > 0:
                                    delta = data['choices'][0].get('delta', {})
                                    if 'content' in delta and delta['content']:
                                        chunk = delta['content']

                                        # is_not_empty = chunk.strip() and not chunk.isspace()
                                        # 只有当chunk包含非空白字符时，才将其拼接到full_response
                                        if chunk:
                                            full_response += chunk
                                        logger.info(f"LLM返回的文字：{chunk[:100]}")
                                        # 如果有回调函数，则调用
                                        if callback and chunk:
                                            await callback(chunk)
                                        
                                        chunk_count += 1
                            except json.JSONDecodeError as je:
                                logger.error(f"无法解析 JSON '{line_str[:100]}...': {str(je)}")
                            except Exception as e:
                                error_msg = f"处理响应数据时出错: {str(e)}, 数据: {line_str[:100]}..."
                                logger.error(error_msg)
                                error_callback and await error_callback(error_msg)
                        except UnicodeDecodeError as ue:
                            error_msg = f"解码响应时出错: {str(ue)}"
                            logger.error(error_msg)
                            error_callback and await error_callback(error_msg)
                        except Exception as e:
                            error_msg = f"处理响应行时出错: {str(e)}"
                            logger.error(error_msg)
                            error_callback and await error_callback(error_msg)
                            
                except Exception as e:
                    error_msg = f"读取响应流时出错: {str(e)}"
                    logger.error(error_msg)
                    error_callback and await error_callback(error_msg)
                
                logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                
                # 如果没有收到任何内容，记录更多的调试信息
                if not full_response:
                    logger.info(f"{str(messages)}")
                    result = ErrorType.NOT_VALID_TEXT.value
                    logger.warning(result)
                    if error_callback:
                        await error_callback(result)
                    # 写入一些信息到文件，表明请求成功但没有收到内容
                    return f"请求完成，但未收到有效内容"
                
                # 调用完成回调
                if complete_callback:
                    try:
                        logger.info("LLM流式已经完成了。现在调用complete_callback函数")
                        logger.info(f"token消耗：{token_consume}")
                        await complete_callback(token_consume, full_response)
                        await update_llm_call(
                            log_id=llm_log.id,
                            response=full_response
                        )
                    except Exception as e:
                        error_msg = f"执行完成回调时出错: {str(e)}"
                        logger.error(error_msg)
                        error_callback and await error_callback(error_msg)
                
                return full_response
                    
    except asyncio.TimeoutError as te:
        error_msg = f"OpenRouter API 请求超时 ({timeout.total} 秒)"
        logger.error(error_msg)
        error_callback and await error_callback(error_msg)
        return f"请求超时: {error_msg}"
    except aiohttp.ClientError as ce:
        error_msg = f"HTTP客户端错误: {str(ce)}"
        logger.error(error_msg)
        # 这个错误发生后，还会继续返回数据，所以不需要进行错误处理
        # error_callback and await error_callback(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"调用OpenRouter时发生错误: {str(e)}"
        logger.error(error_msg)
        error_callback and await error_callback(error_msg)
        return error_msg 
    except asyncio.CancelledError:
        logger.warning("请求被取消")
        return "请求被取消"