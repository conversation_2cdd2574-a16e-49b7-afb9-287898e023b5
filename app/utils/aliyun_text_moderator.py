# coding=utf-8
# python version >= 3.6
import json
import logging
from typing import Optional, Dict, Any
from alibabacloud_green20220302.client import Client
from alibabacloud_green20220302 import models
from alibabacloud_tea_openapi.models import Config
from app.core.logging import get_logger
from app.services.system_config_service import system_config_service

logger = get_logger(__name__)


class AliyunTextModerator:
    """阿里云文本审核客户端（简化版）"""
    
    def __init__(self, access_key_id: str, access_key_secret: str, region: str = 'cn-hangzhou'):
        """初始化客户端"""
        config = Config(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret,
            connect_timeout=10000,  # 10秒
            read_timeout=30000,     # 30秒
            region_id=region,
            endpoint=f'green-cip.{region}.aliyuncs.com'
        )
        self.client = Client(config)
    
    async def moderate_text(self, content: str, service: str = 'comment_detection_pro') -> Any:
        """
        文本审核
        
        Args:
            content: 待审核文本
            service: 服务类型，默认comment_detection_pro
            
        Returns:
            Any: 审核结果（阿里云SDK响应对象）
        """
        try:
            # 构建请求参数
            service_parameters = {'content': content}
            request = models.TextModerationPlusRequest(
                service=service,
                service_parameters=json.dumps(service_parameters)
            )
            
            # 调用API
            response = self.client.text_moderation_plus(request)
            logger.info(f"文本审核响应: {response}")
            if response.status_code == 200:
                result = response.body
                logger.info(f"审核成功: {result}")
                return result
            else:
                logger.error(f"API调用失败: {response.status_code}")
                return {"error": f"API调用失败: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"文本审核异常: {e}")
            return {"error": str(e)}
    
    


async def get_aliyun_config() -> Dict[str, str]:
    """获取阿里云配置"""
    try:
        access_key_id = await system_config_service.get_config("aliyun_access_key_id") or ""
        access_key_secret = await system_config_service.get_config("aliyun_access_key_secret") or ""
        enabled = await system_config_service.get_config("aliyun_text_moderation_enabled") or "OFF"
        region = await system_config_service.get_config("aliyun_access_region") or "cn-shanghai"
        
        return {
            "access_key_id": access_key_id,
            "access_key_secret": access_key_secret,
            "enabled": enabled,
            "region": region
        }
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return {"enabled": "OFF", "region": "cn-shanghai"}


async def moderate_text_simple(content: str, service: str = 'comment_detection_pro') -> Optional[Dict[str, Any]]:
    """
    简单的文本审核函数
    
    Args:
        content: 待审核文本
        service: 服务类型
        
    Returns:
        Dict[str, Any]: 审核结果（JSON格式），失败返回None
    """
    try:
        config = await get_aliyun_config()
        logger.info(f"文本审核配置: {config}")
        # 检查服务是否启用
        if config["enabled"].upper() != "ON":
            logger.warning("阿里云文本审核服务未启用")
            return None
        
        # 检查配置
        if not config["access_key_id"] or not config["access_key_secret"]:
            logger.error("阿里云访问密钥配置不完整")
            return None
        
        # 创建客户端并审核
        moderator = AliyunTextModerator(
            access_key_id=config["access_key_id"],
            access_key_secret=config["access_key_secret"],
            region=config["region"]
        )
        
        # 获取原始响应对象
        result = await moderator.moderate_text(content, service)
        
        # 将阿里云SDK响应对象转换为JSON格式
        if result and hasattr(result, 'to_map'):
            # 如果响应对象有to_map方法，使用它转换为字典
            json_result = result.to_map()
            logger.info(f"转换为JSON格式: {json_result}")
            return json_result
        elif result and hasattr(result, '__dict__'):
            # 如果没有to_map方法，尝试使用__dict__
            json_result = result.__dict__
            logger.info(f"使用__dict__转换为JSON格式: {json_result}")
            return json_result
        elif isinstance(result, dict):
            # 如果已经是字典格式，直接返回
            logger.info(f"结果已经是字典格式: {result}")
            return result
        else:
            # 其他情况，尝试转换为字符串再解析
            logger.warning(f"无法转换响应对象类型: {type(result)}")
            return {"error": f"无法转换响应对象类型: {type(result)}"}
        
    except Exception as e:
        logger.error(f"文本审核失败: {e}")
        return None 