import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from typing import List, Dict, Tuple, Optional
import os
from datetime import datetime
import colorsys
import re
from app.core.config import settings
from app.services.prompts import search_data_keyword_user_prompt
from app.services.llm_service import call_llm
# from app.api.repository.user_default_model import get_user_model
from app.utils.enum import UseCase
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
import json
from app.api.repository.upload_file import write, remove_local_file
from app.utils.utils import get_font

logger = get_logger(__name__)

def read_byte(file_path: str):
    abs_path = os.path.join(os.getcwd(), file_path)
    with open(abs_path,"rb") as f:
        ppt_bytes = f.read()
    return ppt_bytes

# 设置中文字体支持
# CJKmainfont, mainfont, mathfont, monofont = get_font()
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'STHeiti']
plt.rcParams['axes.unicode_minus'] = False

SUPPORT_CHART_LIST = [
    "柱状图",
    "折线图",
    "饼图",
    "散点图"
]
DATA_DEMO_MAP = {
    "柱状图": '''我需要的JSON数据格式如下：{
        "title": "数据表的标题",
        "data": "数据表字典，键为类别名称，值为数值",
        "x_label": "X轴的标签"
    }，下面是我给你的范例：
        {
            "data": {"产品A": 120, "产品B": 150, "产品C": 90, "产品D": 200},
            "title": "产品销售统计图",
            "x_label": "产品类型"
        }
    ''',
    '折线图': '''我需要的JSON数据格式如下：
        {
            "data": "数据字典，键为系列名称，值为数值列表",
            "title": "数据表的标题",
            "x_data": "X轴数据列表"
            "x_label": "X轴的标签"
        }，
        下面是我给你的范例：
        {
            "data": {
                "销售额": [100, 120, 140, 160, 180, 200],
                "利润": [20, 25, 30, 35, 40, 45]
            }
            "title": "月度经营数据统计图",
            "x_data": ["1月", "2月", "3月", "4月", "5月", "6月"],
            "x_label": "月份"
        }
    ''',
    '饼图': '''我需要的JSON数据格式如下：
        {
            "data": "数据字典，键为类别名称，值为数值",
            "title": "数据表的标题"
        }，
        下面是我给你的范例：
        {
            "data": {"苹果": 30, "香蕉": 25, "橙子": 20, "葡萄": 15, "其他": 10},
            "title": "水果占比图"
        }
    ''',
    '散点图': '''我需要的JSON数据格式如下：
        {
            "x_data": "X轴数据列表",
            "y_data": "Y轴数据列表",
            "title": "数据表的标题",
            "x_label": "X轴的标签",
            "y_label": "Y轴的标签"
        }，
        下面是我给你的范例：
        {
            "x_data": [1, 2, 3],
            "y_data": [2, 4, 6],
            "title": "数据分布散点图",
            "x_label": "X变量",
            "y_label": "Y变量"
        }
    '''
}
def get_figure_type(
    description: str
) -> Optional[str]:
    """
    获取图表名称
    
    Args:
        description: 图例说明 
    
    Returns:
        返回图表名称
    """
    figure = None
    for item in SUPPORT_CHART_LIST:
        if item in description:
            figure = item
            break
    return figure

def get_demo_data(
    figure_name: str
) -> Optional[str]:
    """
    获取图表的数据格式要求
    
    Args:
        figure_name: 图名称 
    
    Returns:
        返回图表的数据格式要求
    """
    if figure_name:
        return DATA_DEMO_MAP.get(figure_name)
    return None
def extract_chart_placeholders(
    text: str
) -> List[str]:
    matches = re.findall(r'【图表占位符：(.*?)】', text)
    return matches
def replace_text_with_markdown_image_remove(
    input_text: str,
    target_text: str,
    image_url: str,
):
    """
    将文本中的特定字符串替换为Markdown图片格式
    
    参数:
        input_text: 原始文本
        target_text: 需要被替换的文本
        image_url: 图片URL
    
    返回:
        替换后的文本
    """
    markdown_image = f"![{target_text}]({image_url})"
    replaced_text = f"【图表占位符：{target_text}】"
    if not image_url:
        return input_text.replace(replaced_text, "")
    return input_text.replace(replaced_text, markdown_image)

def generate_colors(n: int):
    """
    生成 n 个互相区分度较高的 RGB 十六进制颜色字符串
    
    Args:
        n (int): 需要的颜色数量
    
    Returns:
        List[str]: 如 ['#FF0000', '#00FF00', '#0000FF', ...]
    """
    colors = []
    for i in range(n):
        hue = i / n  # 色调均分
        lightness = 0.5  # 保持亮度一致
        saturation = 0.9  # 高饱和度
        rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
        hex_color = '#%02X%02X%02X' % tuple(int(x * 255) for x in rgb)
        colors.append(hex_color)
    return colors
async def get_search_keywords(
  title: str,
  description: str,
  current_user: UserResponse,
  api_url: str,
  api_key: str,
  model: str,
  is_thinking: Optional[bool] = False
):
    try:
        # user_model = await get_user_model(
        #     current_user=current_user,
        #     use_case=UseCase.PROJECT_CONFIG_NEED.value
        # )
        keywords = await call_llm(
            apiKey=api_key,
            apiUrl=api_url,
            model=model,
            flag="调用大模型生成搜索图表数据的关键词列表",
            messages=[
                {
                    "role": "user",
                    "content": search_data_keyword_user_prompt(
                        title=title,
                        description=description,
                        count=settings.FIGURE_SEARCH_KEYWORD_COUNT
                    )
                }
            ],
            is_thinking=is_thinking
        )
        return json.loads(keywords)
    except Exception as e:
        logger.error(str(e))
        raise e
def create_bar_chart(
    data: Dict[str, float],
    title: str,
    x_label: str,
    y_label: str = "数值",
    figsize: Tuple[int, int] = (10, 6),
    color: str = "skyblue"
) -> str:
    """
    绘制柱状图并保存
    
    Args:
        data: 数据字典，键为类别名称，值为数值
        title: 图表标题
        x_label: X轴标签
        y_label: Y轴标签
        figsize: 图表尺寸
        color: 柱状图颜色
        width: 柱子宽度，0.4相当于约12px（默认0.8）
    
    Returns:
        保存的文件路径
    """
    try:
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        timestamp = int(datetime.now().timestamp() * 1000)
        file_path = f"pictures/bar_chart_{timestamp}.png"
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 提取数据
        categories = list(data.keys())
        values = list(data.values())
        
        # 绘制柱状图
        bars = ax.bar(categories, values, color=color, width=0.2)
        
        # 设置标题和标签
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel(x_label, fontsize=12)
        ax.set_ylabel(y_label, fontsize=12)
        
        # 在柱状图上添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                f'{value:.1f}', ha='center', va='bottom', fontsize=10)
        
        # 设置网格
        ax.grid(axis='y', alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 确保保存目录存在
        os.makedirs(os.path.dirname(file_path) if os.path.dirname(file_path) else '.', exist_ok=True)
        
        # 保存图片
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()

        write(file_path, read_byte(file_path))
        remove_local_file(file_path)
        logger.info(f"柱状图绘制成功：{file_path}")
        return file_path
        
    except Exception as e:
        print(f"绘制柱状图时出错: {str(e)}")
        return None


def create_line_chart(
    data: Dict[str, List[float]],
    x_data: List[str],
    title: str,
    x_label: str,
    y_label: str = "数值",
    figsize: Tuple[int, int] = (12, 6),
    colors: Optional[List[str]] = None
) -> str:
    """
    绘制折线图并保存
    
    Args:
        data: 数据字典，键为系列名称，值为数值列表
        x_data: X轴数据列表
        title: 图表标题
        x_label: X轴标签
        y_label: Y轴标签
        figsize: 图表尺寸
        colors: 线条颜色列表
    
    Returns:
        保存的文件路径
    """
    try:
        timestamp = int(datetime.now().timestamp() * 1000)
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = f"pictures/line_chart_{timestamp}.png"
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 默认颜色
        if colors is None:
            colors = generate_colors(len(data))
        
        # 绘制多条折线
        for i, (series_name, values) in enumerate(data.items()):
            color = colors[i % len(colors)]
            ax.plot(x_data, values, marker='o', linewidth=2, 
                markersize=6, label=series_name, color=color)
        
        # 设置标题和标签
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel(x_label, fontsize=12)
        ax.set_ylabel(y_label, fontsize=12)
        
        # 添加图例
        ax.legend(loc='best', fontsize=10)
        
        # 设置网格
        ax.grid(True, alpha=0.3)
        
        # 调整X轴标签角度
        plt.xticks(rotation=45)
        
        # 调整布局
        plt.tight_layout()
        
        # 确保保存目录存在
        os.makedirs(os.path.dirname(file_path) if os.path.dirname(file_path) else '.', exist_ok=True)
        
        # 保存图片
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()

        write(file_path, read_byte(file_path))
        remove_local_file(file_path)
        logger.info(f"折线图绘制成功：{file_path}")
        return file_path
        
    except Exception as e:
        print(f"绘制折线图时出错: {str(e)}")
        return None


def create_pie_chart(
    data: Dict[str, float],
    title: str,
    figsize: Tuple[int, int] = (8, 8),
    colors: Optional[List[str]] = None,
    explode: Optional[List[float]] = None
) -> str:
    """
    绘制饼图并保存
    
    Args:
        data: 数据字典，键为类别名称，值为数值
        title: 图表标题
        figsize: 图表尺寸
        colors: 饼图颜色列表
        explode: 突出显示的扇形（可选）
    
    Returns:
        保存的文件路径
    """
    try:
        timestamp = int(datetime.now().timestamp() * 1000)
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = f"pictures/pie_chart_{timestamp}.png"
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 提取数据
        labels = list(data.keys())
        values = list(data.values())
        
        # 默认颜色
        if colors is None:
            colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
        
        # 绘制饼图
        wedges, texts, autotexts = ax.pie(
            values, 
            labels=labels, 
            autopct='%1.1f%%', 
            colors=colors,
            explode=explode,
            shadow=False,
            startangle=90
        )
        
        # 设置标题
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        
        # 调整文本样式
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontsize(14)
            autotext.set_fontweight('bold')
        for text in texts:
            text.set_color("#333")
            text.set_fontsize(14)
            text.set_fontweight('bold')
        # 确保饼图是圆形的
        ax.axis('equal')
        
        # 调整布局
        plt.tight_layout()
        
        # 确保保存目录存在
        os.makedirs(os.path.dirname(file_path) if os.path.dirname(file_path) else '.', exist_ok=True)
        
        # 保存图片
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()

        write(file_path, read_byte(file_path))
        remove_local_file(file_path)
        
        logger.info(f"饼图绘制成功：{file_path}")
        return file_path
        
    except Exception as e:
        print(f"绘制饼图时出错: {str(e)}")
        return None


def create_scatter_plot(
    x_data: List[float],
    y_data: List[float],
    title: str,
    x_label: str,
    y_label: str,
    figsize: Tuple[int, int] = (10, 8),
    color: str = "blue",
    alpha: float = 0.6,
    size: float = 50,
    labels: Optional[List[str]] = None
) -> str:
    """
    绘制散点图并保存
    
    Args:
        x_data: X轴数据
        y_data: Y轴数据
        title: 图表标题
        x_label: X轴标签
        y_label: Y轴标签
        save_path: 保存路径
        figsize: 图表尺寸
        color: 点的颜色
        alpha: 透明度
        size: 点的大小
        labels: 数据点标签（可选）
    
    Returns:
        保存的文件路径
    """
    try:
        timestamp = int(datetime.now().timestamp() * 1000)
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = f"pictures/scatter_chart_{timestamp}.png"
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制散点图
        scatter = ax.scatter(x_data, y_data, c=color, alpha=alpha, s=size, edgecolors='black', linewidth=0.5)
        
        # 设置标题和标签
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel(x_label, fontsize=12)
        ax.set_ylabel(y_label, fontsize=12)
        
        # 如果提供了标签，添加到散点图上
        if labels:
            for i, label in enumerate(labels):
                if i < len(x_data) and i < len(y_data):
                    ax.annotate(label, (x_data[i], y_data[i]), 
                               xytext=(5, 5), textcoords='offset points',
                               fontsize=8, alpha=0.7)
        
        # 添加拟合线（可选）
        # if len(x_data) > 1 and len(y_data) > 1:
        #     z = np.polyfit(x_data, y_data, 1)
        #     p = np.poly1d(z)
        #     ax.plot(x_data, p(x_data), "r--", alpha=0.8, linewidth=2, label=f'拟合线: y={z[0]:.2f}x+{z[1]:.2f}')
        #     ax.legend(loc='best', fontsize=10)
        
        # 设置网格
        ax.grid(True, alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 确保保存目录存在
        os.makedirs(os.path.dirname(file_path) if os.path.dirname(file_path) else '.', exist_ok=True)
        
        # 保存图片
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()

        write(file_path, read_byte(file_path))
        remove_local_file(file_path)
        
        logger.info(f"散点图绘制成功：{file_path}")
        return file_path
        
    except Exception as e:
        print(f"绘制散点图时出错: {str(e)}")
        return None


# # 示例使用函数
# def example_usage():
#     """
#     示例使用方法
#     """
#     # 示例数据
#     bar_data = {"产品A": 120, "产品B": 150, "产品C": 90, "产品D": 200}
#     line_data = {
#         "销售额": [100, 120, 140, 160, 180, 200],
#         "利润": [20, 25, 30, 35, 40, 45]
#     }
#     x_months = ["1月", "2月", "3月", "4月", "5月", "6月"]
#     pie_data = {"苹果": 30, "香蕉": 25, "橙子": 20, "葡萄": 15, "其他": 10}
    
#     # 生成散点图数据
#     x_scatter = [i + np.random.normal(0, 0.5) for i in range(20)]
#     y_scatter = [2*i + np.random.normal(0, 2) for i in x_scatter]
    
#     print("正在创建示例图表...")
    
#     # 柱状图
#     # create_bar_chart(
#     #     bar_data, 
#     #     title="产品销售统计", 
#     #     x_label="产品", 
#     #     y_label="销售量"
#     # )
    
#     # # 折线图
#     # create_line_chart(
#     #     line_data,
#     #     x_months,
#     #     title="月度销售趋势",
#     #     x_label="月份",
#     #     y_label="金额（万元）",
#     # )
    
#     # # 饼图
#     # create_pie_chart(
#     #     pie_data,
#     #     title="水果销售占比"
#     # )
    
#     # # 散点图
#     create_scatter_plot(
#         x_scatter,
#         y_scatter,
#         title="数据分布散点图",
#         x_label="X变量",
#         y_label="Y变量"
#     )
    
#     print(f"图表已保存:")


# if __name__ == "__main__":
#     example_usage()
