import json
import hashlib
import os
from typing import Dict, List, Optional, Any
from tortoise.exceptions import DoesNotExist
from tortoise.functions import Max
from app.models.system_config import SystemConfig
from app.core.logging import get_logger
from app.core.redis_client import get_redis_client
from app.core.cache_lock import CacheConsistencyManager

logger = get_logger(__name__)

# 配置文件路径
CONFIG_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "scripts", "system_configs.json")

# Redis缓存键前缀
CACHE_PREFIX = "system_config"
CACHE_TTL = 3600  # 缓存过期时间（秒）
 

def load_default_configs() -> List[Dict]:
    """从JSON文件加载默认配置"""
    try:
        if not os.path.exists(CONFIG_FILE_PATH):
            logger.warning(f"配置文件不存在: {CONFIG_FILE_PATH}")
            return []
        
        with open(CONFIG_FILE_PATH, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        if 'configs' not in config_data:
            logger.error("配置文件格式错误，缺少 'configs' 字段")
            return []
        
        logger.info(f"成功加载配置文件: {CONFIG_FILE_PATH}")
        return config_data['configs']
    
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return []


# 获取默认配置数据
DEFAULT_SYSTEM_CONFIGS = load_default_configs()


class SystemConfigService:
    """系统配置服务"""
    
    def __init__(self):
        self._redis_client = get_redis_client()
        self._cache_manager = CacheConsistencyManager(self._redis_client)
    
    def _get_cache_key(self, key: str) -> str:
        """生成Redis缓存键"""
        return f"{CACHE_PREFIX}:{key}"
    
    def _calculate_config_md5(self, configs: List[Dict]) -> str:
        """
        计算配置内容的MD5值
        
        统一MD5计算策略：
        1. 按key排序以确保一致性
        2. 只计算关键字段：key, category, description, defaultValue, orderNo
        3. 使用统一的JSON序列化格式
        """
        if not configs:
            return ""
        
        # 提取关键字段并排序
        normalized_configs = []
        for config in configs:
            normalized_config = {
                "key": config.get("key", ""),
                "category": config.get("category", ""),
                "description": config.get("description", ""),
                "defaultValue": config.get("defaultValue", ""),
                "orderNo": config.get("orderNo", 0)
            }
            normalized_configs.append(normalized_config)
        
        # 按key排序以确保一致性
        sorted_configs = sorted(normalized_configs, key=lambda x: x['key'])
        
        # 使用统一的JSON序列化格式
        config_json = json.dumps(sorted_configs, sort_keys=True, ensure_ascii=False, separators=(',', ':'))
        return hashlib.md5(config_json.encode('utf-8')).hexdigest()
    

    
    async def _get_current_md5(self) -> Optional[str]:
        """获取当前配置的MD5值"""
        try:
            config = await SystemConfig.get(key="defaultSystemSettingJsonMd5", is_deleted=False)
            return config.value
        except DoesNotExist:
            return None
    
    async def _update_md5(self, md5_value: str):
        """更新MD5值"""
        config, created = await SystemConfig.get_or_create(
            key="defaultSystemSettingJsonMd5",
            defaults={
                "category": "system",
                "description": "系统配置JSON的MD5值，用于检测配置变化",
                "value": md5_value,
                "default_value": "",
                "order_no": 0
            }
        )
        if not created:
            config.value = md5_value
            await config.save()
    
    async def sync_default_configs(self) -> Dict[str, Any]:
        """
        同步默认配置到数据库
        
        同步规则：
        1. 首次启动时，JSON文件的defaultValue会同步到数据库的default_value和value
        2. 后续启动时，如果JSON文件修改了，只更新数据库的default_value，保留用户的value
        3. 获取配置时，优先使用value，如果value为空则使用default_value
        """
        try:
            current_configs = DEFAULT_SYSTEM_CONFIGS
            if not current_configs:
                logger.warning("没有找到默认配置数据")
                return {"changed": False, "added": 0, "updated": 0, "deleted": 0}
            
            # 使用统一的MD5计算策略
            current_md5 = self._calculate_config_md5(current_configs)
            stored_md5 = await self._get_current_md5()
            
            logger.info(f"配置文件路径: {CONFIG_FILE_PATH}")
            logger.info(f"当前配置MD5: {current_md5}")
            logger.info(f"存储的MD5: {stored_md5}")
            
            if stored_md5 == current_md5:
                logger.info("系统配置无变化，跳过同步")
                return {"changed": False, "added": 0, "updated": 0, "deleted": 0}
            
            logger.info(f"检测到配置变化，开始同步...")
            
            added_count = 0
            updated_count = 0
            
            for config_data in current_configs:
                try:
                    # 验证必需字段
                    if not config_data.get("key"):
                        logger.warning(f"跳过无效配置项: 缺少key字段")
                        continue
                    
                    config, created = await SystemConfig.get_or_create(
                        key=config_data["key"],
                        defaults={
                            "category": config_data.get("category", "system"),
                            "description": config_data.get("description", ""),
                            "value": config_data.get("value", ""),
                            "default_value": config_data.get("defaultValue", ""),
                            "order_no": config_data.get("orderNo", 0)
                        }
                    )
                    
                    if created:
                        added_count += 1
                        logger.info(f"新增配置: {config_data['key']}")
                    else:
                        # 只更新默认值、描述和排序，保留用户的value值
                        needs_update = False
                        if config.default_value != config_data.get("defaultValue", ""):
                            config.default_value = config_data.get("defaultValue", "")
                            needs_update = True
                            logger.info(f"更新默认值: {config_data['key']} = {config_data.get('defaultValue', '')}")
                        
                        if config.description != config_data.get("description", ""):
                            config.description = config_data.get("description", "")
                            needs_update = True
                        
                        if config.order_no != config_data.get("orderNo", 0):
                            config.order_no = config_data.get("orderNo", 0)
                            needs_update = True
                        
                        if needs_update:
                            await config.save()
                            updated_count += 1
                            logger.info(f"更新配置: {config_data['key']}")
                            
                except Exception as e:
                    logger.error(f"同步配置 {config_data.get('key', 'unknown')} 时出错: {e}")
            
            # 更新MD5值
            await self._update_md5(current_md5)
            
            # 清除Redis缓存
            await self.clear_cache()
            
            result = {
                "changed": True,
                "added": added_count,
                "updated": updated_count,
                "deleted": 0
            }
            
            logger.info(f"系统配置同步完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"同步系统配置时出错: {e}")
            raise
    
    async def get_config(self, key: str, use_cache: bool = True) -> Optional[str]:
        """
        获取单个配置值，支持Redis缓存
        
        值优先级：value > default_value
        - value: 用户自定义的当前值
        - default_value: 来自JSON文件的默认值
        """
        if not key:
            logger.warning("配置键为空")
            return None
        
        cache_key = self._get_cache_key(key)
        
        # 尝试从Redis缓存获取
        if use_cache:
            try:
                cached_value = await self._redis_client.get(cache_key)
                if cached_value is not None:
                    # 确保返回字符串类型
                    if isinstance(cached_value, bytes):
                        cached_value = cached_value.decode('utf-8')
                    logger.debug(f"从Redis缓存获取配置: {key}")
                    return cached_value
            except Exception as e:
                logger.warning(f"从Redis缓存获取配置失败: {e}")
        
        # 从数据库获取
        try:
            config = await SystemConfig.get(key=key, is_deleted=False)
            # 优先使用用户自定义的value，如果为空则使用默认值
            value = config.value if config.value is not None else config.default_value
            
            # 存入Redis缓存
            if use_cache and value is not None:
                try:
                    await self._redis_client.set(cache_key, value, ex=CACHE_TTL)
                    logger.debug(f"配置已缓存到Redis: {key}")
                except Exception as e:
                    logger.warning(f"缓存配置到Redis失败: {e}")
            
            return value
            
        except DoesNotExist:
            logger.warning(f"配置项 {key} 不存在")
            return None
        except Exception as e:
            logger.error(f"获取配置 {key} 时出错: {e}")
            return None
    
    async def config_exists(self, key: str) -> bool:
        """检查配置是否存在"""
        if not key:
            return False
        
        try:
            await SystemConfig.get(key=key, is_deleted=False)
            return True
        except DoesNotExist:
            return False
        except Exception as e:
            logger.error(f"检查配置 {key} 是否存在时出错: {e}")
            return False
    
    async def set_config(self, key: str, value: str = None, default_value: str = None, category: str = None, description: str = None, order_no: int = None) -> bool:
        """设置配置值 - 使用分布式锁确保缓存一致性"""
        if not key:
            logger.error("配置键不能为空")
            return False
        
        async def _update_config():
            """实际更新配置的函数"""
            try:
                # 如果没有指定order_no，则获取当前最大order_no + 1
                if order_no is None:
                    max_order = await SystemConfig.filter(is_deleted=False).aggregate(max_order=Max('order_no'))
                    next_order = (max_order['max_order'] or 0) + 1
                else:
                    next_order = order_no
                
                config, created = await SystemConfig.get_or_create(
                    key=key,
                    defaults={
                        "category": category or "custom",
                        "description": description or f"自定义配置: {key}",
                        "value": value,
                        "default_value": default_value,
                        "order_no": next_order
                    }
                )
                
                if not created:
                    if value is not None:
                        config.value = value
                    if default_value is not None:
                        config.default_value = default_value
                    if category:
                        config.category = category
                    if description:
                        config.description = description
                    if order_no is not None:
                        config.order_no = order_no
                    await config.save()
                
                # 清除相关缓存
                await self._cache_manager.invalidate_cache_pattern(f"{CACHE_PREFIX}:{key}")
                
                logger.info(f"设置配置 {key} = {value}, default_value = {default_value}, order_no = {next_order}")
                return True
                
            except Exception as e:
                logger.error(f"设置配置 {key} 时出错: {e}")
                return False
        
        # 使用分布式锁执行更新
        return await self._cache_manager._lock_manager.with_lock(
            f"config_update:{key}", 
            _update_config
        )
    
    async def get_configs_by_category(self, category: str) -> List[Dict[str, Any]]:
        """根据分类获取配置列表"""
        if not category:
            logger.warning("分类参数为空")
            return []
        
        try:
            configs = await SystemConfig.filter(
                category=category,
                is_deleted=False
            ).order_by('order_no', 'key')
            
            result = []
            for config in configs:
                result.append({
                    "id": str(config.id),
                    "key": config.key,
                    "category": config.category,
                    "description": config.description,
                    "value": config.value,
                    "default_value": config.default_value,
                    "order_no": config.order_no,
                    "created_at": config.created_at,
                    "updated_at": config.updated_at
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取分类 {category} 的配置时出错: {e}")
            return []
    
    async def get_all_configs(self) -> List[Dict[str, Any]]:
        """获取所有配置"""
        try:
            configs = await SystemConfig.filter(
                is_deleted=False
            ).order_by('order_no', 'key')
            
            result = []
            for config in configs:
                result.append({
                    "id": str(config.id),
                    "key": config.key,
                    "category": config.category,
                    "description": config.description,
                    "value": config.value,
                    "default_value": config.default_value,
                    "order_no": config.order_no,
                    "created_at": config.created_at,
                    "updated_at": config.updated_at
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取所有配置时出错: {e}")
            return []
    
    async def delete_config(self, key: str) -> bool:
        """删除配置（软删除） - 使用分布式锁确保缓存一致性"""
        if not key:
            logger.error("配置键不能为空")
            return False
        
        async def _delete_config():
            """实际删除配置的函数"""
            try:
                config = await SystemConfig.get(key=key)
                config.is_deleted = True
                await config.save()
                
                # 清除相关缓存
                await self._cache_manager.invalidate_cache_pattern(f"{CACHE_PREFIX}:{key}")
                
                logger.info(f"删除配置: {key}")
                return True
                
            except DoesNotExist:
                logger.warning(f"配置 {key} 不存在")
                return False
            except Exception as e:
                logger.error(f"删除配置 {key} 时出错: {e}")
                return False
        
        # 使用分布式锁执行删除，增加重试机制
        try:
            return await self._cache_manager._lock_manager.with_lock(
                f"config_delete:{key}", 
                _delete_config
            )
        except Exception as e:
            logger.error(f"删除配置 {key} 时锁操作失败: {e}")
            
            # 尝试强制释放锁并重试
            try:
                await self._cache_manager._lock_manager.force_release_lock(f"config_delete:{key}")
                logger.info(f"已强制释放锁 config_delete:{key}，准备重试")
                
                # 重试一次
                return await self._cache_manager._lock_manager.with_lock(
                    f"config_delete:{key}", 
                    _delete_config
                )
            except Exception as retry_e:
                logger.error(f"重试删除配置 {key} 仍然失败: {retry_e}")
                return False
    
    async def clear_cache(self):
        """清除Redis缓存 - 使用分布式锁确保一致性"""
        async def _clear_cache():
            """实际清除缓存的函数"""
            try:
                pattern = f"{CACHE_PREFIX}:*"
                keys = await self._redis_client.keys(pattern)
                if keys:
                    await self._redis_client.delete(*keys)
                    logger.info(f"已清除 {len(keys)} 个系统配置缓存")
                else:
                    logger.info("没有找到需要清除的系统配置缓存")
                    
                # 发布缓存失效消息
                await self._cache_manager.invalidate_cache_pattern(pattern)
                
            except Exception as e:
                logger.error(f"清除Redis缓存时出错: {e}")
        
        # 使用分布式锁执行清除
        await self._cache_manager._lock_manager.with_lock("cache_clear", _clear_cache)

    async def get_config_by_id(self, config_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取配置详情"""
        if not config_id:
            logger.warning("配置ID为空")
            return None
        
        try:
            config = await SystemConfig.get(id=config_id, is_deleted=False)
            return {
                "id": str(config.id),
                "key": config.key,
                "category": config.category,
                "description": config.description,
                "value": config.value,
                "default_value": config.default_value,
                "order_no": config.order_no,
                "created_at": config.created_at,
                "updated_at": config.updated_at
            }
        except DoesNotExist:
            logger.warning(f"配置ID {config_id} 不存在")
            return None
        except Exception as e:
            logger.error(f"根据ID获取配置 {config_id} 时出错: {e}")
            return None

    async def update_config_by_id(self, config_id: str, value: str = None, default_value: str = None, category: str = None, description: str = None, order_no: int = None) -> bool:
        """根据ID更新配置 - 使用分布式锁确保缓存一致性"""
        if not config_id:
            logger.error("配置ID不能为空")
            return False
        
        async def _update_config():
            """实际更新配置的函数"""
            try:
                config = await SystemConfig.get(id=config_id, is_deleted=False)
                
                if value is not None:
                    config.value = value
                if default_value is not None:
                    config.default_value = default_value
                if category is not None:
                    config.category = category
                if description is not None:
                    config.description = description
                if order_no is not None:
                    config.order_no = order_no
                
                await config.save()
                
                # 清除相关缓存
                await self._cache_manager.invalidate_cache_pattern(f"{CACHE_PREFIX}:{config.key}")
                
                logger.info(f"更新配置 {config.key} (ID: {config_id})")
                return True
                
            except DoesNotExist:
                logger.warning(f"配置ID {config_id} 不存在")
                return False
            except Exception as e:
                logger.error(f"更新配置ID {config_id} 时出错: {e}")
                return False
        
        # 使用分布式锁执行更新
        return await self._cache_manager._lock_manager.with_lock(
            f"config_update_by_id:{config_id}", 
            _update_config
        )

    async def delete_config_by_id(self, config_id: str) -> bool:
        """根据ID删除配置（软删除） - 使用分布式锁确保缓存一致性"""
        if not config_id:
            logger.error("配置ID不能为空")
            return False
        
        async def _delete_config():
            """实际删除配置的函数"""
            try:
                config = await SystemConfig.get(id=config_id, is_deleted=False)
                config.is_deleted = True
                await config.save()
                
                # 清除相关缓存
                await self._cache_manager.invalidate_cache_pattern(f"{CACHE_PREFIX}:{config.key}")
                
                logger.info(f"删除配置: {config.key} (ID: {config_id})")
                return True
                
            except DoesNotExist:
                logger.warning(f"配置ID {config_id} 不存在")
                return False
            except Exception as e:
                logger.error(f"删除配置ID {config_id} 时出错: {e}")
                return False
        
        # 使用分布式锁执行删除
        return await self._cache_manager._lock_manager.with_lock(
            f"config_delete_by_id:{config_id}", 
            _delete_config
        )


# 全局系统配置服务实例
system_config_service = SystemConfigService() 