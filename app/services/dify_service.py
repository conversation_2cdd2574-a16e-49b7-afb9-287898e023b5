import json
import aiohttp
import asyncio
from typing import Dict, List, Optional, Any, AsyncGenerator, Union
from app.core.config import settings
from app.core.logging import get_logger
from pydantic import BaseModel
from typing import Literal
from enum import Enum
import os
from datetime import datetime, timedelta

class ContentStatus(str, Enum):
  """内容状态"""
  ERROR = "error"  # 错误状态
  NORMAL = "normal"  # 正常文本
  HEART_BEAT = "heart_beat"

class FileItem(BaseModel):
  type: Literal[
    "document","image","audio","video","custom"
  ]
  transfer_method: Literal["remote_url", "local_file"]
  url: Optional[str] = None  # 如果是 remote_url，填这个
  upload_file_id: Optional[str] = None  # 如果是 local_file，填这个
# 获取logger实例
logger = get_logger(__name__)


class DifyService:
    """Dify API服务类"""
    
    def __init__(self, api_key: str, base_url: str):
        """
        初始化Dify服务
        
        Args:
            api_key: Dify API密钥
            base_url: Dify API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    async def send_chat_message(
        self,
        query: str,
        user: str,
        inputs: Optional[Dict[str, Any]] = None,
        response_mode: str = "streaming",
        conversation_id: Optional[str] = None,
        files: Optional[List[FileItem]] = None,
        auto_generate_name: bool = True
    ) -> Union[Dict[str, Any], AsyncGenerator[str, None]]:
        """
        发送对话消息到Dify API
        
        Args:
            query: 用户输入/提问内容
            user: 用户标识，应用内唯一
            inputs: 允许传入App定义的各变量值
            response_mode: 响应模式，streaming或blocking
            conversation_id: 会话ID，用于继续之前的对话
            files: 文件列表，仅当模型支持Vision能力时可用
            auto_generate_name: 自动生成会话标题
            
        Returns:
            - 如果response_mode为"blocking": 返回完整的API响应字典
            - 如果response_mode为"streaming": 返回AsyncGenerator[str, None]，可用于FastAPI StreamingResponse
        """
        logger.info(f"DifyService.send_chat_message - 开始发送对话消息 用户:{user} 模式:{response_mode} 会话:{conversation_id or 'N/A'}")
        
        # 构建请求体
        payload = {
            "query": query,
            "user": user,
            "response_mode": response_mode,
            "auto_generate_name": auto_generate_name,
        }
        
        if inputs:
            payload["inputs"] = inputs
        if conversation_id:
            payload["conversation_id"] = conversation_id
        if files:
            # 将FileItem对象转换为字典
            payload["files"] = [file.model_dump() for file in files]
            logger.debug(f"DifyService.send_chat_message - 包含文件 用户:{user} 文件数:{len(files)}")
        
        # 设置超时 - 调整为更合理的时间
        timeout = aiohttp.ClientTimeout(total=600)  # 10分钟
        logger.debug(f"DifyService.send_chat_message - 请求参数 用户:{user} 查询长度:{len(query)} 超时:{timeout.total}s")
        
        if response_mode == "blocking":
            # 阻塞模式处理
            try:
                async with aiohttp.ClientSession() as session:
                    url = f"{self.base_url}/chat-messages"
                    logger.debug(f"DifyService.send_chat_message - 发送阻塞请求 用户:{user} URL:{url}")
                    
                    async with session.post(
                        url,
                        headers=self.headers,
                        json=payload,
                        timeout=timeout
                    ) as resp:
                        if resp.status != 200:
                            error_text = await resp.text()
                            logger.error(f"DifyService.send_chat_message - 阻塞API错误 用户:{user} 状态码:{resp.status}")
                            logger.error(f"DifyService.send_chat_message - 错误详情 用户:{user}: {error_text}")
                            raise Exception(f"Dify API错误: {resp.status} - {error_text}")
                        
                        # 阻塞模式，直接返回完整响应
                        result = await resp.json()
                        message_id = result.get('message_id', 'N/A')
                        logger.info(f"DifyService.send_chat_message - 阻塞响应成功 用户:{user} 消息ID:{message_id}")
                        return result
            except asyncio.TimeoutError:
                error_msg = f"DifyService.send_chat_message - 阻塞请求超时 用户:{user} 超时:{timeout.total}s"
                logger.error(error_msg)
                raise Exception("Dify API请求超时")
            except Exception as e:
                error_msg = f"DifyService.send_chat_message - 阻塞调用异常 用户:{user} 错误:{str(e)}"
                logger.error(error_msg)
                raise Exception(f"Dify API调用失败: {str(e)}")
        else:
            # 流式模式处理 - 修复上下文管理器问题
            return self._safe_stream_wrapper(payload, timeout)

    async def _stream_chat_response(self, payload: Dict[str, Any], timeout: aiohttp.ClientTimeout) -> AsyncGenerator[str, None]:
        """
        流式响应处理，确保正确管理连接上下文
        
        Args:
            payload: 请求负载
            timeout: 超时配置
            
        Yields:
            SSE格式的数据行
        """
        logger.debug("开始流式处理Dify响应")
        session = None
        resp = None
        
        try:
            session = aiohttp.ClientSession()
            url = f"{self.base_url}/chat-messages"
            logger.debug(f"向Dify发送流式请求: {url}")
            
            resp = await session.post(
                url,
                headers=self.headers,
                json=payload,
                timeout=timeout
            )
            
            if resp.status != 200:
                error_text = await resp.text()
                logger.error(f"Dify API 错误: 状态码 {resp.status}")
                logger.error(f"错误详情: {error_text}")
                error_msg = f"Dify API错误: {resp.status} - {error_text}"
                yield f"data: {json.dumps({'status': ContentStatus.ERROR.value, 'content': error_msg})}\n\n"
                return
            
            logger.debug("开始读取流式响应内容")
            # 在响应正常的情况下进行流式处理
            line_count = 0
            buffer = b""

            # 使用iter_chunked来避免"Chunk too big"错误，设置较小的chunk大小
            async for chunk in resp.content.iter_chunked(8192):  # 8KB chunks
                try:
                    buffer += chunk

                    # 按行分割处理
                    while b'\n' in buffer:
                        line_bytes, buffer = buffer.split(b'\n', 1)

                        try:
                            line_str = line_bytes.decode('utf-8').strip()
                            line_count += 1

                            if not line_str:
                                # 空行跳过，不发送心跳包以提高性能
                                continue

                            # 处理SSE格式
                            if line_str.startswith("data: "):
                                data_str = line_str[6:]  # 移除"data: "前缀

                                # 处理结束标记
                                if data_str == "[DONE]":
                                    logger.debug(f"流式响应正常结束，处理了 {line_count} 行")
                                    yield f"data: {json.dumps({'status': 'completed'})}\n\n"
                                    return

                                try:
                                    # 验证JSON格式
                                    event_data = json.loads(data_str)
                                    event_type = event_data.get("event", "")
                                    logger.debug(f"处理事件: {event_type}")

                                    if event_type == "error":
                                        error_msg = f"Dify返回错误: {event_data}"
                                        logger.error(error_msg)
                                        yield f"data: {json.dumps({'status': ContentStatus.ERROR.value, 'content': error_msg})}\n\n"
                                        return
                                    else:
                                        # 直接转发原始SSE数据，简化结构
                                        yield f"data: {data_str}\n\n"
                                except json.JSONDecodeError as e:
                                    logger.warning(f"无法解析SSE数据: {data_str}, 错误: {e}")
                                    # 对于无法解析的数据，尝试作为普通文本处理
                                    yield f"data: {json.dumps({'status': ContentStatus.NORMAL.value, 'content': data_str})}\n\n"
                                    continue
                        except UnicodeDecodeError as e:
                            logger.warning(f"Unicode解码错误: {e}")
                            continue
                        except Exception as e:
                            logger.error(f"处理单行数据时发生异常: {e}")
                            continue

                except Exception as e:
                    logger.error(f"处理chunk数据时发生异常: {e}")
                    continue

            # 处理剩余的buffer数据
            if buffer:
                try:
                    line_str = buffer.decode('utf-8').strip()
                    if line_str and line_str.startswith("data: "):
                        data_str = line_str[6:]
                        if data_str != "[DONE]":
                            try:
                                event_data = json.loads(data_str)
                                yield f"data: {data_str}\n\n"
                            except json.JSONDecodeError:
                                yield f"data: {json.dumps({'status': ContentStatus.NORMAL.value, 'content': data_str})}\n\n"
                except UnicodeDecodeError as e:
                    logger.warning(f"处理剩余buffer时Unicode解码错误: {e}")
                except Exception as e:
                    logger.error(f"处理剩余buffer时发生异常: {e}")
                    
        except asyncio.TimeoutError:
            error_msg = "Dify API请求超时"
            logger.error(error_msg)
            yield f"data: {json.dumps({'status': ContentStatus.ERROR.value, 'content': error_msg})}\n\n"
        except aiohttp.ClientError as e:
            error_msg = f"HTTP客户端错误: {str(e)}"
            logger.error(error_msg)
            yield f"data: {json.dumps({'status': ContentStatus.ERROR.value, 'content': error_msg})}\n\n"
        except Exception as e:
            error_msg = f"流式响应处理失败: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.debug(f"异常详情:\n{traceback.format_exc()}")
            # 发送错误事件
            yield f"data: {json.dumps({'status': ContentStatus.ERROR.value, 'content': error_msg})}\n\n"
        finally:
            # 确保资源正确清理
            try:
                if resp:
                    resp.close()
                if session:
                    await session.close()
                logger.debug("HTTP连接资源已清理")
            except Exception as cleanup_error:
                logger.warning(f"清理HTTP连接时出错: {cleanup_error}")
        
        logger.info("Dify流式响应处理完成")

    async def _safe_stream_wrapper(self, payload: Dict[str, Any], timeout: aiohttp.ClientTimeout) -> AsyncGenerator[str, None]:
        """
        安全的流式响应包装器，确保所有异常都被正确处理
        
        Args:
            payload: 请求负载
            timeout: 超时配置
            
        Yields:
            SSE格式的数据行
        """
        try:
            async for data in self._stream_chat_response(payload, timeout):
                yield data
        except Exception as e:
            # 捕获任何未处理的异常并转换为SSE错误消息
            error_msg = f"流式响应包装器捕获异常: {str(e)}"
            logger.error(error_msg)
            try:
                yield f"data: {json.dumps({'status': ContentStatus.ERROR.value, 'content': error_msg})}\n\n"
            except Exception as json_error:
                # 如果连JSON序列化都失败了，发送一个简单的错误消息
                logger.error(f"JSON序列化失败: {json_error}")
                yield f"data: {{'status': 'error', 'content': 'Internal server error'}}\n\n"

    async def get_conversations(self, user: str, last_id: Optional[str] = None, limit: int = 20, pinned: Optional[bool] = None, filter_days: Optional[int] = None) -> Dict[str, Any]:
        """
        获取会话列表
        
        Args:
            user: 用户标识
            last_id: 上一页最后一条记录的 ID（用于分页）
            limit: 返回数量限制
            pinned: 是否只返回置顶会话
            filter_days: 过滤天数，只返回指定天数内的会话，默认为None（不过滤）
            
        Returns:
            会话列表
        """
        logger.info(f"获取用户会话列表: {user}, 过滤天数: {filter_days}")
        
        params = {"user": user, "limit": limit}
        if last_id:
            params["last_id"] = last_id
        if pinned is not None:
            params["pinned"] = pinned
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/conversations"
                
                async with session.get(
                    url,
                    headers=self.headers,
                    params=params
                ) as resp:
                    if resp.status != 200:
                        error_text = await resp.text()
                        logger.error(f"获取会话列表失败: {resp.status} - {error_text}")
                        raise Exception(f"获取会话列表失败: {resp.status}")
                    
                    result = await resp.json()
                    
                    # 如果设置了时间过滤，进行客户端过滤
                    if filter_days is not None and filter_days > 0:
                        cutoff_timestamp = datetime.now().timestamp() - (filter_days * 24 * 60 * 60)
                        logger.info(f"应用时间过滤: {filter_days}天，截止时间戳: {cutoff_timestamp}")
                        
                        # 过滤会话数据
                        original_data = result.get('data', [])
                        filtered_data = []
                        
                        for conversation in original_data:
                            created_at = conversation.get('created_at', 0)
                            if created_at >= cutoff_timestamp:
                                filtered_data.append(conversation)
                            else:
                                logger.debug(f"过滤掉会话: {conversation.get('id')}, 创建时间: {created_at}")
                        
                        result['data'] = filtered_data
                        logger.info(f"时间过滤完成: 原始数量 {len(original_data)}, 过滤后数量: {len(filtered_data)}")
                    
                    logger.info(f"成功获取会话列表，数量: {len(result.get('data', []))}")
                    return result
                    
        except Exception as e:
            logger.error(f"获取会话列表失败: {str(e)}")
            raise

    async def stop_chat_message(self, task_id: str, user: str) -> Dict[str, Any]:
        """
        停止流式对话消息生成
        
        Args:
            task_id: 任务ID，可在流式返回Chunk中获取
            user: 用户标识，必须和发送消息接口传入user保持一致
            
        Returns:
            停止操作的结果
        """
        logger.info(f"停止对话消息生成: task_id={task_id}, user={user}")
        
        # 构建请求体
        payload = {
            "user": user
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/chat-messages/{task_id}/stop"
                logger.debug(f"向Dify发送停止请求: {url}")
                
                async with session.post(
                    url,
                    headers=self.headers,
                    json=payload
                ) as resp:
                    if resp.status != 200:
                        error_text = await resp.text()
                        logger.error(f"停止对话消息失败: {resp.status} - {error_text}")
                        raise Exception(f"停止对话消息失败: {resp.status} - {error_text}")
                    
                    result = await resp.json()
                    logger.info(f"成功停止对话消息生成: {result}")
                    return result
                    
        except Exception as e:
            logger.error(f"停止对话消息失败: {str(e)}")
            raise

    async def upload_file(self, file_path: str, user: str, file_type: Optional[str] = None) -> Dict[str, Any]:
        """
        上传文件到Dify
        参考：https://docs.dify.ai/api-reference/%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C/%E4%B8%8A%E4%BC%A0%E6%96%87%E4%BB%B6
        
        Args:
            file_path: 本地文件路径
            user: 用户标识，必填项
            file_type: 文件类型，可选值为 "document", "image", "audio", "video", "custom"
        Returns:
            上传成功后的响应JSON
        """
        logger.info(f"上传文件到Dify: {file_path}, 用户: {user}, 文件类型: {file_type}")
        
        url = f"{self.base_url}/files/upload"
        headers = self.headers.copy()
        headers.pop("Content-Type", None)  # multipart请求不能有Content-Type头，由aiohttp自动生成
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise Exception(f"文件不存在: {file_path}")
        
        try:
            async with aiohttp.ClientSession() as session:
                with open(file_path, "rb") as f:
                    form = aiohttp.FormData()
                    # 使用os.path.basename获取文件名，兼容不同操作系统的路径分隔符
                    filename = os.path.basename(file_path)
                    # 添加文件字段
                    form.add_field('file', f, filename=filename)
                    # 添加必填的user字段
                    form.add_field('user', user)
                    # 添加文件类型字段（如果提供）
                    if file_type:
                        form.add_field('type', file_type)
                        
                    async with session.post(url, headers=headers, data=form) as resp:
                        if resp.status not in (200, 201):
                            error_text = await resp.text()
                            logger.error(f"Dify文件上传失败: {resp.status} - {error_text}")
                            raise Exception(f"Dify文件上传失败: {resp.status} - {error_text}")
                        result = await resp.json()
                        logger.info(f"Dify文件上传成功: {result}")
                        return result
        except Exception as e:
            logger.error(f"Dify文件上传异常: {str(e)}")
            raise

    async def get_messages(self, conversation_id: str, user: str, first_id: Optional[str] = None, limit: int = 20) -> Dict[str, Any]:
        """
        获取会话历史消息
        滚动加载形式返回历史聊天记录，第一页返回最新 limit 条，即：倒序返回
        
        Args:
            conversation_id: 会话ID
            user: 用户标识
            first_id: 第一条消息ID（用于分页加载）
            limit: 返回消息数量限制，默认20
            
        Returns:
            消息历史记录
        """
        logger.info(f"查询会话历史消息: conversation_id={conversation_id}, user={user}, first_id={first_id}, limit={limit}")
        
        params = {
            "conversation_id": conversation_id,
            "user": user,
            "limit": limit
        }
        if first_id:
            params["first_id"] = first_id
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/messages"
                
                async with session.get(
                    url,
                    headers=self.headers,
                    params=params
                ) as resp:
                    if resp.status != 200:
                        error_text = await resp.text()
                        logger.error(f"获取会话历史消息失败: {resp.status} - {error_text}")
                        raise Exception(f"获取会话历史消息失败: {resp.status}")
                    
                    result = await resp.json()
                    logger.info(f"获取会话历史消息成功，数量: {len(result.get('data', []))}")
                    return result
                    
        except Exception as e:
            logger.error(f"获取会话历史消息失败: {str(e)}")
            raise

    async def delete_conversation(self, conversation_id: str, user: str) -> Dict[str, Any]:
        """
        删除会话
        
        Args:
            conversation_id: 会话ID
            user: 用户标识
            
        Returns:
            删除操作的结果
        """
        logger.info(f"删除会话: conversation_id={conversation_id}, user={user}")
        
        # 构建请求体
        payload = {
            "user": user
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/conversations/{conversation_id}"
                logger.info(f"向Dify发送删除会话请求: {url}")
                
                async with session.delete(
                    url,
                    headers=self.headers,
                    json=payload
                ) as resp:
                    logger.info(f"删除会话响应: {resp.status}")
                    if resp.status == 204:
                        # 204 No Content 表示删除成功
                        logger.info(f"成功删除会话: {conversation_id}")
                        return {"result": "success", "message": "会话已删除"}
                    elif resp.status == 404:
                        error_text = await resp.text()
                        logger.warning(f"会话不存在: {conversation_id}, 错误: {error_text}")
                        # 对于404错误，返回成功，因为目标（删除会话）已经达成
                        return {"result": "success", "message": "会话不存在或已被删除"}
                    elif resp.status == 200:
                        result = await resp.json()
                        logger.info(f"成功删除会话: {result}")
                        return result
                    else:
                        error_text = await resp.text()
                        logger.error(f"删除会话失败: {resp.status} - {error_text}")
                        raise Exception(f"删除会话失败: {resp.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"删除会话失败: {str(e)}")
            raise

    async def rename_conversation(self, conversation_id: str, user: str, name: str = "", auto_generate: bool = True) -> Dict[str, Any]:
        """
        重命名会话
        
        Args:
            conversation_id: 会话ID
            user: 用户标识
            name: 新的会话名称，如果为空且auto_generate为True，则自动生成
            auto_generate: 是否自动生成会话名称
            
        Returns:
            重命名操作的结果
        """
        logger.info(f"重命名会话: conversation_id={conversation_id}, user={user}, name={name}, auto_generate={auto_generate}")
        
        # 构建请求体
        payload = {
            "name": name,
            "auto_generate": auto_generate,
            "user": user
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/conversations/{conversation_id}/name"
                logger.debug(f"向Dify发送重命名会话请求: {url}")
                
                async with session.post(
                    url,
                    headers=self.headers,
                    json=payload
                ) as resp:
                    if resp.status == 404:
                        error_text = await resp.text()
                        logger.warning(f"会话不存在: {conversation_id}, 错误: {error_text}")
                        raise Exception(f"会话不存在或已被删除")
                    elif resp.status != 200:
                        error_text = await resp.text()
                        logger.error(f"重命名会话失败: {resp.status} - {error_text}")
                        raise Exception(f"重命名会话失败: {resp.status} - {error_text}")
                    
                    result = await resp.json()
                    logger.info(f"成功重命名会话: {result}")
                    return result
                    
        except Exception as e:
            logger.error(f"重命名会话失败: {str(e)}")
            raise

# 便捷函数，用于快速创建和使用Dify服务
async def create_dify_service(api_key: str, base_url: str) -> DifyService:
    """
    创建Dify服务实例
    
    Args:
        api_key: Dify API密钥
        base_url: Dify API基础URL
        
    Returns:
        DifyService实例
    """
    return DifyService(api_key, base_url)

