from typing import Tuple
from app.core.logging import get_logger
from app.api.repository import upload_file
from app.utils.utils import read_file_content
from app.services.text_moderation_service import text_moderation_service

logger = get_logger(__name__)

class ContentModerationService:
    """内容审核服务 - 统一处理文件内容审核逻辑"""
    
    async def verify_text_content(self, content: str, description: str = "文本") -> Tuple[bool, str]:
        """
        验证文本内容是否安全
        
        Args:
            content: 文本内容
            description: 内容描述（用于日志）
            
        Returns:
            Tuple[bool, str]: (是否安全, 错误信息)
        """
        try:
            # 检查文本是否为空
            if not content or not content.strip():
                return False, "文本内容不能为空"
            
            # 调用文本审核服务
            is_content_safe = await text_moderation_service.is_content_safe(
                content=content
            )
            
            if not is_content_safe:
                return False, "您输入的文本内容违规"
            
            logger.info(f"{description}内容审核通过")
            return True, ""
            
        except Exception as e:
            logger.error(f"{description}内容审核失败: {str(e)}")
            return False, f"文本内容审核失败: {str(e)}"
    
    async def verify_file_content(self, file_path: str, description: str = "文件") -> Tuple[bool, str]:
        """
        验证文件内容是否安全
        
        Args:
            file_path: 文件路径
            description: 文件描述（用于日志）
            
        Returns:
            Tuple[bool, str]: (是否安全, 错误信息)
        """
        try:
            # 下载文件到本地
            upload_file.download_file(file_path)
            
            try:
                # 读取文件内容
                document_text = read_file_content(file_path)
                
                # 检查提取的文本是否为空
                if not document_text.strip():
                    return False, "文档中没有提取到文本内容"
                
                # 调用文本审核服务
                is_content_safe = await text_moderation_service.is_content_safe(
                    content=document_text
                )
                
                if not is_content_safe:
                    return False, "您输入的文档/文本内容违规"
                
                logger.info(f"{description}内容审核通过")
                return True, ""
                
            finally:
                # 清理本地文件
                try:
                    upload_file.remove_local_file(file_path)
                except Exception as cleanup_error:
                    logger.warning(f"清理本地文件失败: {cleanup_error}")
                    
        except Exception as e:
            logger.error(f"{description}内容审核失败: {str(e)}")
            return False, f"文档内容审核失败: {str(e)}"

# 创建全局实例
content_moderation_service = ContentModerationService() 