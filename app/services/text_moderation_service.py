import logging
from typing import Dict, Any, Optional
from app.utils.aliyun_text_moderator import moderate_text_simple
from app.core.logging import get_logger
from app.services.system_config_service import system_config_service

logger = get_logger(__name__)

class TextModerationService:
    """文本审核服务（简化版）"""
    
    @staticmethod
    async def check_text(
        content: str,
        service: str = 'comment_detection_pro'
    ) -> Dict[str, Any]:
        """
        检查文本内容
        
        Args:
            content: 待审核的文本内容
            service: 审核服务类型
            
        Returns:
            Dict: 审核结果
        """
        if not content or not content.strip():
            return {
                "passed": True,
                "is_risky": False,
                "labels": [],
                "message": "内容为空，无需审核"
            }
        
        try:
            # 调用阿里云审核服务
            result = await moderate_text_simple(content, service)
            logger.info(f"==================文本审核结果: {result}")
            
            if result is None:
                # 审核服务不可用，采用宽松策略
                logger.warning("文本审核服务不可用，采用宽松策略")
                return {
                    "passed": True,
                    "is_risky": False,
                    "labels": [],
                    "message": "审核服务不可用，内容已通过"
                }
            
            # 检查是否有错误
            if isinstance(result, dict) and "error" in result:
                logger.error(f"文本审核返回错误: {result['error']}")
                return {
                    "passed": True,
                    "is_risky": False,
                    "labels": [],
                    "message": f"审核服务错误，内容已通过: {result['error']}"
                }
            
            # 解析阿里云返回的JSON响应
            try:
                # 现在result是JSON格式的字典，可以直接访问
                
                # 检查业务状态码
                if result.get('Code') != 200:
                    message = result.get('Message', '未知错误')
                    logger.error(f"业务错误: {message}")
                    return {
                        "passed": True,
                        "is_risky": False,
                        "labels": [],
                        "message": f"业务错误，内容已通过: {message}"
                    }
                
                # 获取数据
                data = result.get('Data', {})
                
                
                # 检查是否有风险标签 - 从Result数组中提取
                results = data.get('Result', [])
                labels = []
                is_risky = False
                
                # 定义安全标签（表示内容安全的标签）
                safe_labels = {'nonLabel', 'normal', 'safe', 'pass'}
                
                if results and isinstance(results, list):
                    for item in results:
                        if isinstance(item, dict) and item.get('Label'):
                            label = item.get('Label', '')
                            if label and label not in safe_labels:
                                # 只有非安全标签才认为是风险标签
                                labels.append(label)
                                is_risky = True
                
                # 检查风险等级 - 只有high和medium才认为有风险
                risk_level = data.get('RiskLevel', 'low')
                if risk_level in ['high']:
                    is_risky = True
                elif risk_level in ['none', 'low', 'medium']:
                    # none和low表示无风险或低风险，确保is_risky为False
                    is_risky = False
                
                # 记录详细的审核结果
                passed = not is_risky
                
                logger.info(f"审核结果详情 - 通过: {passed}, 有风险: {is_risky}, 风险等级: {risk_level}, 标签: {labels}")
                
                # 返回审核结果
                return {
                    "passed": passed,
                    "is_risky": is_risky,
                    "labels": labels,
                    "risk_level": risk_level,
                    "message": "审核完成"
                }
                
            except Exception as parse_error:
                logger.error(f"解析阿里云响应失败: {parse_error}")
                # 解析失败时采用宽松策略
                return {
                    "passed": True,
                    "is_risky": False,
                    "labels": [],
                    "message": f"解析响应失败，内容已通过: {str(parse_error)}"
                }
            
        except Exception as e:
            logger.error(f"文本审核异常: {e}")
            # 发生异常时采用宽松策略
            return {
                "passed": True,
                "is_risky": False,
                "labels": [],
                "message": f"审核异常，内容已通过: {str(e)}"
            }
    
    @staticmethod
    async def check_user_content(
        content: str,
        content_type: str = "comment_detection_pro"
    ) -> Dict[str, Any]:
        """
        检查用户生成的内容
        
        Args:
            content: 待审核的内容
            content_type: 内容类型 
            
        Returns:
            Dict: 审核结果
        """
        # 从系统配置获取默认的审核服务类型
        default_service = await system_config_service.get_config("aliyun_text_moderation_default_service")
        
        # 如果配置中没有设置，使用默认值
        if not default_service:
            default_service = "comment_detection_pro"
            logger.warning("未找到默认审核服务配置，使用默认值: comment_detection_pro")
        
        logger.info(f"用户内容审核，内容类型: {content_type}, 使用服务: {default_service}")
        
        return await TextModerationService.check_text(content, default_service)
    

    @staticmethod
    def get_risk_description(check_result: Dict[str, Any]) -> str:
        """获取风险描述"""
        if not check_result.get("is_risky", False):
            return "内容安全"
        
        labels = check_result.get("labels", [])
        if labels:
            return f"检测到违规内容: {', '.join(labels)}"
        
        return "检测到风险内容"

    @staticmethod
    async def is_content_safe(
        content: str,
        content_type: str = "comment"
    ) -> bool:
        """
        检查PPT文档内容是否安全（统一处理审核逻辑）
        
        Args:
            content: 待审核的文档内容
            content_type: 内容类型
        Returns:
            bool: True表示内容安全可以生成，False表示内容违规不能生成
            
        """
        try:
            # 检查内容是否为空
            if not content or not content.strip():
                return True  # 空内容认为是安全的
            
            # 如果内容长度超过600字符，需要进行拆分
            max_length = 600
            content_parts = []
            
            if len(content) <= max_length:
                # 内容长度在限制内，直接使用
                content_parts = [content]
            else:
                # 内容长度超过限制，需要拆分
                logger.info(f"内容长度 {len(content)} 超过 {max_length} 字符，进行拆分处理")
                
                # 直接按600字符进行拆分
                start = 0
                while start < len(content):
                    end = start + max_length
                    if end > len(content):
                        end = len(content)
                    
                    # 提取当前段
                    part = content[start:end].strip()
                    if part:  # 只添加非空段落
                        content_parts.append(part)
                    
                    start = end
                
                logger.info(f"内容已拆分为 {len(content_parts)} 个部分")
            
            # 对每个部分进行审核
            for i, part in enumerate(content_parts):
                logger.info(f"正在审核第 {i+1}/{len(content_parts)} 部分，长度: {len(part)} 字符")
                
                # 调用文本审核服务
                moderation_result = await TextModerationService.check_user_content(
                    content=part,
                    content_type=content_type
                )
                
                # 检查审核结果
                if not moderation_result.get("passed", True):
                    logger.warning(f"第 {i+1} 部分内容审核未通过: {moderation_result.get('message', '未知原因')}")
                    return False  # 有任何一部分不通过就返回False
            
            # 所有部分都通过审核
            logger.info("所有内容部分都通过审核，内容安全")
            return True
            
        except Exception as e:
            logger.error(f"内容安全审核异常: {e}")
            # 审核服务异常时，为了安全起见，拒绝生成
            return False


# 创建服务实例
text_moderation_service = TextModerationService() 