"""
钱包服务工厂
根据用户是否在saas_user_mapping表中判断是否需要调用预扣费接口，
并为SaaS用户创建对应的适配器实例
"""
import importlib
from typing import Optional, Type

from app.core.logging import get_logger
from app.services.wallet.base_adapter import WalletServiceInterface
from app.models.saas_platform import SaasPlatform

logger = get_logger(__name__)

async def get_wallet_adapter(saas_platform: SaasPlatform) -> Optional[WalletServiceInterface]:
    """
       根据SaaS平台配置获取适配器实例

       Args:
           saas_platform: SaaS平台配置

       Returns:
           WalletServiceInterface: 适配器实例
       """
    try:
        adapter_class_map = {
            # 默认适配器
            "default": "app.services.wallet.adapters.default_adapter.DefaultWalletAdapter"
        }

        # 优先使用平台特定的适配器，如果没有则使用默认适配器
        adapter_class_path = adapter_class_map.get(saas_platform.platform) or adapter_class_map["default"]

        # 解析适配器类路径
        module_path, class_name = adapter_class_path.rsplit('.', 1)

        # 动态导入模块
        module = importlib.import_module(module_path)

        # 获取适配器类
        adapter_class: Type[WalletServiceInterface] = getattr(module, class_name)

        # 创建实例
        adapter_instance = adapter_class(saas_platform)

        return adapter_instance
    except Exception as e:
        logger.error(f"SaaS Wallet 适配器创建失败: {str(e)}")
        return None


