"""
钱包适配器基类和接口定义
提供通用的日志记录、错误处理和状态管理功能
"""
from abc import ABC

from app.core.logging import get_logger
from app.services.wallet.interface import (WalletServiceInterface, PreChargeRequest, TransactionStatus, PreChargeResponse, OrderStatus, ChargeCancelRequest, ChargeCancelResponse, ChargeConfirmResponse, ChargeConfirmRequest)
from app.models.saas_wallet_transaction import WalletTransaction
from app.models.saas_platform import SaasPlatform

logger = get_logger(__name__)

class BaseWalletAdapter(WalletServiceInterface, ABC):
    """
    钱包适配器基类
    提供通用功能，具体平台只需实现核心业务逻辑
    """
    
    def __init__(self, saas_platform: SaasPlatform):
        """
        初始化适配器
        
        Args:
            saas_platform: SaaS平台配置对象
        """
        self.saas_platform = saas_platform
        self.pre_charge_url = saas_platform.pre_charge_url
        self.charge_confirm_url = saas_platform.charge_confirm_url
        self.charge_cancel_url = saas_platform.charge_cancel_url
        self.api_key = saas_platform.api_key

    async def pre_charge(self, request: PreChargeRequest) -> PreChargeResponse:
        """
        预计费用
        """
        wallet_transaction = await WalletTransaction.create(
            user_id=request.user_id,
            count=request.count,
            agent_id=request.agent_id,
            platform=self.saas_platform.platform,
            transaction_state=TransactionStatus.PRE_CHARGING
        )
        order_id = wallet_transaction.id
        logger.info(f"预计费-生成交易记录，ID:{order_id}")
        request.order_id = str(order_id)
        # 调用具体实现
        response = await self._do_pre_charge(request)
        logger.info(f"预计费-调用具体实现返回:{response}")

        if response.success and response.pre_charge_id:
            wallet_transaction.pre_charge_id = str(response.pre_charge_id)
            wallet_transaction.transaction_state = TransactionStatus.PRE_CHARGE_SUCCESS
        else:
            wallet_transaction.transaction_state = TransactionStatus.PRE_CHARGE_FAILED

        await wallet_transaction.save()
        return response

    
    async def confirm(self, request: ChargeConfirmRequest):
        """
        计费确认
        """
        # 查找相关的预计费交易记录
        wallet_transaction = await WalletTransaction.filter(
            id=request.order_id
        ).first()

        if wallet_transaction is None:
            logger.error(f"计费确认 - 未查询到 OrderId: {request.order_id} 的交易记录")
            return

        if wallet_transaction.transaction_state == TransactionStatus.CHARGING:
            logger.error(f"计费确认 - OrderId: {request.order_id} 的交易记录正在确认中，无法再次确认")
            return

        if wallet_transaction.transaction_state == TransactionStatus.CHARGE_SUCCESS:
            logger.error(f"计费确认 - OrderId: {request.order_id} 的交易记录已成功确认，无法再次确认")
            return

        if wallet_transaction.transaction_state == TransactionStatus.PRE_CHARGE_CANCELLING:
            logger.error(f"计费确认 - OrderId: {request.order_id} 的交易记录正在取消中，无法再次确认")
            return

        if wallet_transaction.transaction_state == TransactionStatus.PRE_CHARGE_CANCEL_SUCCESS:
            logger.error(f"计费确认 - OrderId: {request.order_id} 的交易记录已成功取消，无法再次确认")
            return

        # 更新交易状态为确认中
        wallet_transaction.transaction_state = TransactionStatus.CHARGING
        # 订单（业务）状态更新
        wallet_transaction.order_state = OrderStatus.CONFIRM.value
        # 订单ID赋值
        wallet_transaction.business_id = request.business_id
        # 先保存订单状态
        await wallet_transaction.save()

        # 调用具体实现
        response = await self._do_confirm(request)
        logger.info(f"计费确认-调用具体实现返回:{response}")

        if response.success:
            # 交易状态更新为成功
            wallet_transaction.transaction_state = TransactionStatus.CHARGE_SUCCESS
        else:
            # 交易状态更新为失败
            wallet_transaction.transaction_state = TransactionStatus.CHARGE_FAILED

        # 保存交易状态
        await wallet_transaction.save()

    async def cancel(self, request: ChargeCancelRequest):
        """
        计费取消
        """
        # 查找相关的预计费交易记录
        wallet_transaction = await WalletTransaction.filter(
            id=request.order_id
        ).first()

        if wallet_transaction is None:
            logger.error(f"计费取消 - 未查询到 OrderId: {request.order_id} 的交易记录")
            return

        if wallet_transaction.transaction_state == TransactionStatus.PRE_CHARGE_CANCELLING:
            logger.error(f"计费取消 - OrderId: {request.order_id} 的交易记录正在取消中，无法再次取消")
            return

        if wallet_transaction.transaction_state == TransactionStatus.PRE_CHARGE_CANCEL_SUCCESS:
            logger.error(f"计费取消 - OrderId: {request.order_id} 的交易记录已成功取消，无法再次取消")
            return

        if wallet_transaction.transaction_state == TransactionStatus.CHARGING:
            logger.error(f"计费取消 - OrderId: {request.order_id} 的交易记录正在确认中，无法再次取消")
            return

        if wallet_transaction.transaction_state == TransactionStatus.CHARGE_SUCCESS:
            logger.error(f"计费取消 - OrderId: {request.order_id} 的交易记录已成功确认，无法再次取消")
            return

         # 更新交易状态为取消中
        wallet_transaction.transaction_state = TransactionStatus.PRE_CHARGE_CANCELLING
        # 订单（业务）状态更新
        wallet_transaction.order_state = OrderStatus.CANCEL.value
        # 订单ID赋值
        wallet_transaction.business_id = request.business_id
        # 保存订单状态
        await wallet_transaction.save()

        # 调用具体实现
        response = await self._do_cancel(request)
        logger.info(f"计费取消-调用具体实现返回:{response}")
        if response.success:
            # 交易状态更新为成功
            wallet_transaction.transaction_state = TransactionStatus.PRE_CHARGE_CANCEL_SUCCESS
        else:
            # 交易状态更新为失败
            wallet_transaction.transaction_state = TransactionStatus.PRE_CHARGE_CANCEL_FAILED

        # 保存交易状态
        await wallet_transaction.save()


    # 抽象方法，由具体适配器实现
    async def _do_pre_charge(self, request: PreChargeRequest) -> PreChargeResponse:
        """具体的预计费实现，由子类重写"""
        raise NotImplementedError("子类必须实现 _do_pre_charge 方法")
    
    async def _do_confirm(self, request: ChargeConfirmRequest) -> ChargeConfirmResponse:
        """具体的计费确认实现，由子类重写"""
        raise NotImplementedError("子类必须实现 _do_confirm 方法")

    async def _do_cancel(self, request: ChargeCancelRequest) -> ChargeCancelResponse:
        """具体的计费取消实现，由子类重写"""
        raise NotImplementedError("子类必须实现 _do_cancel 方法")