"""
通用默认钱包适配器
"""

import httpx
import json

from app.core.config import settings
from app.core.logging import get_logger
from app.services.wallet.base_adapter import BaseWalletAdapter
from app.services.wallet.interface import PreChargeRequest, PreChargeResponse, ChargeCancelRequest, \
    ChargeCancelResponse, ChargeConfirmRequest, ChargeConfirmResponse
from app.utils.encryption_util import encrypt_data

logger = get_logger(__name__)

class DefaultWalletAdapter(BaseWalletAdapter):
    """
    通用默认钱包适配器
    """

    def __init__(self, saas_platform):
        super().__init__(saas_platform)
        self.timeout = 60

    async def _do_pre_charge(self, request: PreChargeRequest) -> PreChargeResponse:
        """
        实现通用的预计费逻辑（加密版本）
        """
        try:
            # 构建请求参数
            request_data = {
                "userId": request.external_id,
                "externalOrderId": request.order_id,
                "agentClientId": request.agent_id,
                "callCount": request.count,
                "expireMinutes": request.expire_minutes,
            }

            # 加密请求数据

            final_request = encrypt_data(request_data, settings.AES_ENCRYPT_KEY, settings.HMAC_KEY)

            # 发送HTTP请求到预计费URL
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                headers = {
                    "Content-Type": "application/json",
                    "X-API-KEY": self.api_key
                }
                logger.info(f"SaaS平台-{self.saas_platform.platform_name}-预计费：{self.pre_charge_url}，"
                            f"请求头：{headers},请求内容-加密前：{json.dumps(request_data)},请求内容-加密后：{json.dumps(final_request)}")

                response = await client.post(
                    self.pre_charge_url,
                    json=final_request,
                    headers=headers,
                )
                
                response_data = response.json()

                logger.info(f"SaaS平台-{self.saas_platform.platform_name}-预计费：{self.pre_charge_url}，"
                            f"响应内容：{response_data}")
            # 解析响应
            if response.status_code == 200 and response_data.get("success"):
                return PreChargeResponse(
                    success=True,
                    order_id=response_data.get("data").get("externalOrderId"),
                    pre_charge_id=response_data.get("data").get("preChargeId")
                )
            else:
                return PreChargeResponse(
                    success=False,
                    message=response_data.get("message", "预计费失败"),
                )
        except Exception as e:
            logger.error(f"预计费 请求异常: {str(e)}")
            return PreChargeResponse(
                success=False,
                message=f"系统繁忙，请稍后重试！"
            )

    async def _do_confirm(self, request: ChargeConfirmRequest) -> ChargeConfirmResponse:
        """
        实现通用的计费确认逻辑（加密版本）
        """
        try:
            # 构建请求参数
            request_data = {
                "externalOrderId": request.order_id,
                "actualCallCount": request.count
            }

            # 加密请求数据
            final_request = encrypt_data(request_data, settings.AES_ENCRYPT_KEY, settings.HMAC_KEY)
            
            # 发送HTTP请求到回执URL
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                headers = {
                    "Content-Type": "application/json",
                    "X-API-KEY": self.api_key
                }
                logger.info(f"SaaS平台-{self.saas_platform.platform_name}-计费确认：{self.charge_confirm_url}，"
                            f"请求头：{headers},请求内容-加密前：{json.dumps(request_data)},请求内容-加密后：{json.dumps(final_request)}")
                response = await client.post(
                    self.charge_confirm_url,
                    json=final_request,
                    headers=headers
                )
                
                response_data = response.json()
                logger.info(f"SaaS平台-{self.saas_platform.platform_name}-计费确认：{self.charge_confirm_url}，"
                            f"响应内容：{response_data}")
            # 解析响应
            if response.status_code == 200 and response_data.get("success"):
                return ChargeConfirmResponse(
                    success=True,
                )
            else:
                return ChargeConfirmResponse(
                    success=False,
                    message=response_data.get("message", "计费确认失败"),
                )
        except Exception as e:
            logger.error(f"计费确认 请求异常: {str(e)}")
            return ChargeConfirmResponse(
                success=False,
                message=f"系统繁忙，请稍后重试！",
            )

    async def _do_cancel(self, request: ChargeCancelRequest) -> ChargeCancelResponse:
        """
        实现通用的计费取消逻辑（加密版本）
        """
        try:
            # 构建请求参数
            request_data = {
                "externalOrderId": request.order_id
            }

            # 加密请求数据
            final_request = encrypt_data(request_data, settings.AES_ENCRYPT_KEY, settings.HMAC_KEY)

            # 发送HTTP请求到回执URL
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                headers = {
                    "Content-Type": "application/json",
                    "X-API-KEY": self.api_key
                }
                logger.info(f"SaaS平台-{self.saas_platform.platform_name}-计费取消：{self.charge_cancel_url}，"
                            f"请求头：{headers}, 请求内容-加密前：{json.dumps(request_data)},请求内容-加密后：{json.dumps(final_request)}")
                response = await client.post(
                    self.charge_cancel_url,
                    json=final_request,
                    headers=headers
                )

                response_data = response.json()
                logger.info(f"SaaS平台-{self.saas_platform.platform_name}-计费取消：{self.charge_cancel_url}，"
                            f"响应内容：{response_data}")
            # 解析响应
            if response.status_code == 200 and response_data.get("success"):
                return ChargeCancelResponse(
                    success=True,
                )
            else:
                return ChargeCancelResponse(
                    success=False,
                    message=response_data.get("message", "计费取消失败"),
                )
        except Exception as e:
            logger.error(f"计费取消 请求异常: {str(e)}")
            return ChargeCancelResponse(
                success=False,
                message=f"系统繁忙，请稍后重试！",
            )
