from typing import Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

class TransactionStatus(Enum):
    """交易状态枚举"""
    PRE_CHARGING = "PRE_CHARGING"                                  # 预算中
    PRE_CHARGE_SUCCESS = "PRE_CHARGE_SUCCESS"                      # 预算成功
    PRE_CHARGE_FAILED = "PRE_CHARGE_FAILED"                        # 预算失败

    # 计费确认阶段
    CHARGING = "CHARGING"                                           # 计费中
    CHARGE_SUCCESS = "CHARGE_SUCCESS"                               # 计费成功
    CHARGE_FAILED = "CHARGE_FAILED"                                 # 计费失败

    # 预计费取消阶段
    PRE_CHARGE_CANCELLING = "PRE_CHARGE_CANCELLING"                  # 预计费取消中
    PRE_CHARGE_CANCEL_SUCCESS = "PRE_CHARGE_CANCEL_SUCCESS"          # 预计费取消成功
    PRE_CHARGE_CANCEL_FAILED = "PRE_CHARGE_CANCEL_FAILED"            # 预计费取消失败

class OrderStatus(Enum):
    """订单（业务）状态枚举"""
    CONFIRM = "CONFIRM" # 确认，表示智能体业务成功
    CANCEL = "CANCEL"   # 取消，表示智能体业务失败

class AGENT(Enum):
    """智能体枚举"""
    CHAT = "chat"
    HOMEWORK = "homework"
    PAPER = "paper"
    PPT = "ppt"
    AI_TRACES = "ai-traces"
    HALLUCINATION = "hallucination"

@dataclass
class PreChargeRequest:
    """预计费"""
    user_id: str # 本系统用户id
    external_id: str # 第三方用户id
    agent_id: str #使用的智能体agentId
    expire_minutes: int
    order_id: Optional[str] = None #交易订单ID
    count: int = 1 #预计费次数

@dataclass
class PreChargeResponse:
    """预计费结果"""
    success: bool
    order_id: Optional[str] = None
    pre_charge_id: Optional[str] = None
    message: Optional[str] = None

@dataclass
class ChargeConfirmRequest:
    """计费确认请求"""
    order_id: str
    business_id: Optional[str] = None # 业务id
    count: int = 1

@dataclass
class ChargeConfirmResponse:
    """计费确认结果"""
    success: bool
    message: Optional[str] = None

@dataclass
class ChargeCancelRequest:
    """计费取消请求"""
    order_id: str
    business_id: Optional[str] = None # 业务id

@dataclass
class ChargeCancelResponse:
    """计费取消结果"""
    success: bool
    message: Optional[str] = None

class WalletServiceInterface(ABC):
    """
    钱包服务接口
    所有钱包适配器都必须实现这个接口
    """

    @abstractmethod
    async def pre_charge(self, request: PreChargeRequest) -> PreChargeResponse:
        """
        预计费
        """
        pass

    @abstractmethod
    async def confirm(self, request: ChargeConfirmRequest) -> ChargeConfirmResponse:
        """
        计费确认
        """
        pass

    @abstractmethod
    async def cancel(self, request: ChargeCancelRequest) -> ChargeCancelResponse:
        """
        计费取消
        """
        pass
