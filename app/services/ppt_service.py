"""
PPT生成服务模块

本模块提供基于文档内容的智能PPT生成功能，支持多种模板类型和分阶段生成。

主要功能：
1. 文档内容解析和分析
2. 基于AI的PPT内容生成
3. 多种PPT模板类型支持（商务、创意、学术等）
4. 分阶段生成（基础信息 + 详细内容）
5. PPT文件创建和模板填充
6. 完整的文件管理和记录跟踪

核心流程：
文档上传 -> 内容解析 -> AI分析生成 -> 模板填充 -> PPT文件输出

支持的模板类型：
- graduation: 毕业答辩模板（8页内容）- 适合学术答辩
- creative: 创意模板（12页内容）- 适合产品展示  
- simple: 简约模板（8页内容）- 适合快速汇报

技术特色：
- JSON Schema约束确保输出格式一致性
- 分阶段生成提高内容质量和可控性
- 完善的错误处理和fallback机制
- 依赖注入支持更好的测试性
- 实时模型配置查询确保配置时效性

Author: AI助手
Created: 2024
Last Modified: 2024
"""

import os
import json
import re
import tempfile
import asyncio
from typing import Dict, List, Optional, Any, Union

from app.api.repository import upload_file
from app.api.repository.local_upload_file import remove_local_file
from app.api.repository.user_default_model import get_user_model
from app.utils.enum import UseCase
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor


from app.core.config import settings
from app.core.logging import get_logger
from app.services.llm_service import call_llm, call_llm_with_format_json
from app.services.prompts import (
    get_ppt_basic_info_prompt,
    get_ppt_content_slides_by_title_prompt,
    get_ppt_template_specific_instructions,
    get_ppt_focus_instruction
)
from app.api.schemas.user import UserResponse
from app.api.schemas.ppt import PPTGenerateResponse, PPTTemplateType
from app.services.wallet.interface import AGENT
from app.services.wallet.service import wallet_service


# PPT配置常量
class PPTConfig:
    """
    PPT生成服务的配置常量类
    
    集中管理PPT生成过程中的各种配置参数，避免硬编码，提高代码可维护性
    """
    # 内容生成相关配置
    DEFAULT_PAGES_PER_TITLE = 2  # 每个标题默认生成的页数
    MAX_CONTENT_PREVIEW_LENGTH = 8000  # 文档内容预览的最大长度（字符数）
    DEFAULT_CONTENT_SLIDE_COUNT = 4  # 默认内容页数量
    
    # 数据结构限制配置
    MAX_AGENDA_ITEMS = 4  # 议程项目的最大数量
    
    # 不同PPT模板类型对应的内容页数量配置
    # 根据模板的设计容量和使用场景确定每种模板支持的最大内容页数
    TEMPLATE_SLIDE_COUNTS = {
        PPTTemplateType.GRADUATION.value: 8,    # 毕业答辩模板：适合学术答辩，8页内容
        PPTTemplateType.CREATIVE.value: 12,     # 创意模板：适合产品展示，12页内容  
        PPTTemplateType.SIMPLE.value: 8,        # 简约模板：适合快速汇报，8页内容
    }

# 基础PPT数据结构工厂函数
def create_base_ppt_template(content_slide_count: int = PPTConfig.DEFAULT_CONTENT_SLIDE_COUNT) -> Dict[str, Any]:
    """
    创建基础PPT模板数据结构
    
    这是一个工厂函数，用于动态生成PPT数据结构的模板。
    通过传入不同的内容页数量，可以生成适合不同PPT模板类型的数据结构。
    
    Args:
        content_slide_count: 需要生成的内容页数量，默认使用配置中的默认值
        
    Returns:
        Dict[str, Any]: 包含完整PPT结构的字典，包括：
            - 基础信息：标题、副标题、日期、作者等
            - 议程信息：中文议程、英文议程、子标题
            - 内容页面：动态生成指定数量的内容页结构
            - 其他信息：结论、联系方式、文档来源等
            
    Note:
        - 所有字段初始值为空字符串或空数组
        - 内容页面数量可根据模板类型动态调整
        - 每个内容页包含4个要点，每个要点包含标题和内容
    """
    return {
        # 基础信息字段
        "main_title": "",           # PPT主标题
        "subtitle": "",             # PPT副标题  
        "date": "",                 # 日期信息
        "author": "",               # 作者信息
        "school": "",               # 学校或机构信息
        "guidance": "",             # 指导信息
        
        # 议程相关字段（固定4个议程项目）
        "agenda": [""] * PPTConfig.MAX_AGENDA_ITEMS,        # 中文议程
        "agenda_en": [""] * PPTConfig.MAX_AGENDA_ITEMS,     # 英文议程
        "agenda_sub": [""] * PPTConfig.MAX_AGENDA_ITEMS,    # 议程子标题
        
        # 动态内容页面（根据模板类型调整数量）
        "content_slides": [
            {
                "title": "",        # 内容页标题
                "points": [         # 内容页要点（固定4个要点）
                    {"title": "", "content": ""},  # 要点1：标题+内容
                    {"title": "", "content": ""},  # 要点2：标题+内容
                    {"title": "", "content": ""},  # 要点3：标题+内容
                    {"title": "", "content": ""}   # 要点4：标题+内容
                ]
            } for _ in range(content_slide_count)  # 根据参数动态生成指定数量的内容页
        ],
        
        # 其他信息字段
        "contact_info": "",         # 联系方式信息
        "document_source": ""       # 文档来源信息
    }

# 注意：PPT_TEMPLATE_DATA_FORMAT 已删除，因为未使用
# 如需要可以动态调用 create_base_ppt_template() 函数

# 注意：PPT_BASIC_INFO_SCHEMA 已删除，因为使用动态生成的Schema
# 参见 _create_dynamic_basic_info_schema() 方法

# 注意：PPT_CONTENT_SLIDE_SCHEMA 和 PPT_CONTENT_SLIDES_ARRAY_SCHEMA 已删除
# 因为使用动态生成的Schema，参见 _create_dynamic_slides_schema() 方法

from app.models.model_config import ModelConfig
from app.models.co_ai_ppt import CoAiPpt
from app.models.upload_file import UploadFile as UploadFileModel
from app.models.user import User
from app.utils.document_parser import default_parser, DocumentParser

logger = get_logger(__name__)

# 注意：fill_with_data_format 函数已删除，因为未使用
# 当前代码直接使用LLM返回的JSON数据，无需额外格式化

class PPTService:
    """
    PPT生成服务类
    
    提供完整的PPT生成功能，包括：
    1. 基于文档内容的智能PPT生成
    2. 分阶段内容生成（基础信息 + 详细内容）
    3. 多种PPT模板类型支持
    4. 文件管理和模板处理
    5. 占位符替换和动态内容填充
    
    核心流程：
    - 解析文档内容 -> 生成基础信息 -> 生成详细内容 -> 创建PPT文件
    
    支持的模板类型：
    - graduation: 毕业答辩模板（8页内容）
    - creative: 创意模板（12页内容）
    - simple: 简约模板（8页内容）
    """
    
    def __init__(self, document_parser: Optional[DocumentParser] = None):
        """
        初始化PPT生成服务
        
        Args:
            document_parser: 文档解析器实例，如果不提供则使用默认解析器
            
        Note:
            - 自动创建必要的目录结构
            - 注入文档解析器依赖，支持依赖注入模式
        """
        # 配置目录路径
        self.base_dir = "llm_file"          # 生成文件的基础目录
        self.template_dir = "templates/ppt"  # PPT模板文件存放目录
        
        # 依赖注入：文档解析器
        self.document_parser = document_parser or default_parser
        
        # 确保必要的目录存在
        os.makedirs(self.base_dir, exist_ok=True)      # 创建生成文件目录
        os.makedirs(self.template_dir, exist_ok=True)  # 创建模板目录
    
 


    async def generate_ppt_basic_info_from_document(
        self,
        document_content: str,
        document_title: str,
        ppt_type: str = "graduation",
        current_user: User = None
    ) -> Dict[str, Any]:
        """
        第一阶段：基于文档内容生成PPT基础信息和内容页标题

        这是分阶段生成的第一步，主要任务是分析文档内容，提取核心信息，
        生成PPT的基础框架，包括标题、议程、内容页标题等结构性信息。

        Args:
            document_content: 文档文本内容，用于分析和提取信息
            document_title: 文档标题，作为PPT主标题的参考
            ppt_type: PPT模板类型，决定内容页数量和风格，默认为"business"

        Returns:
            Dict[str, Any]: 包含基础信息和内容页标题的结构化数据，包括：
                - main_title: PPT主标题
                - subtitle: PPT副标题
                - author: 作者信息
                - agenda: 议程项目（中文）
                - agenda_en: 议程项目（英文）
                - agenda_sub: 议程子标题
                - content_slide_titles: 内容页标题列表
                - 其他基础字段

        Process:
            1. 根据模板类型确定内容页数量
            2. 构建针对性的提示词
            3. 使用JSON Schema约束输出格式或文本解析
            4. 调用大语言模型生成基础信息
            5. 解析并返回结构化数据
            6. 失败时返回默认基础信息

        Note:
            - 支持多种模板类型，每种类型有不同的内容页数量
            - 兼容JSON Schema和文本解析两种模式
            - 有完善的错误处理和fallback机制
        """
        logger.info(f"第一阶段：生成PPT基础信息，标题: '{document_title}', 模板: {ppt_type}")

        # 根据PPT模板类型调整prompt和内容页数量
        template_specific_instructions = get_ppt_template_specific_instructions(ppt_type)
        content_slide_count = self._get_content_slide_count_by_template(ppt_type)

        # 获取模型配置以检查是否支持JSON Schema
        model_config = await get_user_model(
          current_user=current_user,
          use_case=UseCase.AI_PPT_GENERATE.value
        )
        supports_json_schema = self._model_supports_json_schema(model_config.model_name)

        # 构建第一阶段的prompt（使用prompts.py中的模板）
        prompt = get_ppt_basic_info_prompt(
            document_title=document_title,
            document_content=document_content[:8000],
            template_specific_instructions=template_specific_instructions,
            content_slide_count=content_slide_count,
            use_json_schema=supports_json_schema
        )

        messages = [
            {"role": "user", "content": prompt}
        ]

        try:
            # 检查模型是否支持JSON Schema
            if supports_json_schema:
                logger.info(f"模型 {model_config.model_name} 支持JSON Schema，使用结构化输出")
                # 创建动态JSON Schema（根据模板类型调整内容页标题数量）
                dynamic_schema = self._create_dynamic_basic_info_schema(content_slide_count)

                # 定义 response_format 使用动态JSON Schema
                response_format = {
                    "type": "json_schema",
                    "json_schema": {
                        "name": "ppt_basic_info_result",
                        "strict": True,
                        "schema": dynamic_schema
                    }
                }

                # 调用大模型生成基础信息，使用JSON Schema约束
                response = await call_llm_with_format_json(
                    messages=messages,
                    flag="ppt_basic_info_generation",
                    model=model_config.model_name,
                    api_key=model_config.api_key,
                    api_url=model_config.api_url,
                    response_format=response_format
                )

                if not response:
                    raise ValueError("大模型返回空响应")

                # 解析JSON
                basic_info = json.loads(response)
                logger.info(f"第一阶段成功生成基础信息（JSON Schema模式），包含 {len(basic_info.get('content_slide_titles', []))} 个内容页标题")
                return basic_info
            else:
                logger.info(f"模型 {model_config.model_name} 不支持JSON Schema，使用文本解析模式")
                # 使用普通LLM调用，然后解析文本响应
                response = await call_llm(
                    messages=messages,
                    flag="ppt_basic_info_generation",
                    model=model_config.model_name,
                    apiKey=model_config.api_key,
                    apiUrl=model_config.api_url
                )

                if not response:
                    raise ValueError("大模型返回空响应")

                # 解析文本响应为JSON
                basic_info = self._parse_text_response_to_basic_info(response, content_slide_count)
                logger.info(f"第一阶段成功生成基础信息（文本解析模式），包含 {len(basic_info.get('content_slide_titles', []))} 个内容页标题")
                return basic_info

        except Exception as e:
            logger.error(f"生成PPT基础信息失败: {str(e)}")
            # 返回默认基础信息作为fallback
            return self._get_default_basic_info_from_document(document_title, document_content, ppt_type)



    def _create_dynamic_slides_schema(self, pages_count: int) -> Dict[str, Any]:
        """根据页数创建动态的Slides Schema"""
        return {
            "type": "object",
            "properties": {
                "slides": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {
                                "type": "string",
                                "description": "内容页标题"
                            },
                            "points": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "title": {
                                            "type": "string",
                                            "description": "要点标题"
                                        },
                                        "content": {
                                            "type": "string",
                                            "description": "要点详细内容"
                                        }
                                    },
                                    "required": ["title", "content"],
                                    "additionalProperties": False
                                },
                                "minItems": 4,
                                "maxItems": 4,
                                "description": "4个要点，每个包含标题和内容"
                            }
                        },
                        "required": ["title", "points"],
                        "additionalProperties": False
                    },
                    "minItems": pages_count,
                    "maxItems": pages_count,
                    "description": f"根据标题生成的内容页数组，需要生成{pages_count}页"
                }
            },
            "required": ["slides"],
            "additionalProperties": False
        }

    async def generate_content_slides_by_title(
        self,
        document_content: str,
        document_title: str,
        slide_title: str,
        slide_index: int,
        pages_count: int,
        ppt_type: str = "graduation",
        current_user: User = None
    ) -> List[Dict[str, Any]]:
        """
        根据单个标题生成多个内容页（数组格式）

        Args:
            document_content: 文档文本内容
            document_title: 文档标题
            slide_title: 内容页标题
            slide_index: 内容页索引（0-3）
            pages_count: 该标题需要生成的页数
            ppt_type: PPT模板类型

        Returns:
            内容页数组 [{title, points}, {title, points}, ...]
        """
        logger.info(f"根据标题生成多页内容，标题: '{slide_title}', 索引: {slide_index}, 页数: {pages_count}")

        # 获取模型配置以检查是否支持JSON Schema
        model_config = await get_user_model(
            current_user=current_user,
            use_case=UseCase.AI_PPT_GENERATE.value
        )
        supports_json_schema = self._model_supports_json_schema(model_config.model_name)

        # 构建prompt（使用prompts.py中的模板）
        prompt = get_ppt_content_slides_by_title_prompt(
            document_title=document_title,
            slide_title=slide_title,
            pages_count=pages_count,
            document_content=document_content[:6000],
            use_json_schema=supports_json_schema
        )

        messages = [
            {"role": "user", "content": prompt}
        ]

        try:
            # 检查模型是否支持JSON Schema
            if supports_json_schema:
                logger.info(f"模型 {model_config.model_name} 支持JSON Schema，使用结构化输出生成内容页")
                # 根据pages_count动态创建Schema
                dynamic_schema = self._create_dynamic_slides_schema(pages_count)

                # 定义 response_format 使用动态JSON Schema
                response_format = {
                    "type": "json_schema",
                    "json_schema": {
                        "name": "ppt_content_slides_array_result",
                        "strict": True,
                        "schema": dynamic_schema
                    }
                }

                # 调用大模型生成内容页数组，使用JSON Schema约束
                response = await call_llm_with_format_json(
                    messages=messages,
                    flag="ppt_content_slides_array_generation",
                    model=model_config.model_name,
                    api_key=model_config.api_key,
                    api_url=model_config.api_url,
                    response_format=response_format
                )

                if not response:
                    raise ValueError("大模型返回空响应")

                # 解析JSON
                result = json.loads(response)
                slides = result.get("slides", [])

                # 为每个页面添加页码信息到标题
                for i, slide in enumerate(slides):
                    if "title" in slide:
                        slide["title"] = f"{slide_title} (第{i+1}部分)"

                logger.info(f"成功生成标题 '{slide_title}' 的 {len(slides)} 页内容（JSON Schema模式）")
                return slides
            else:
                logger.info(f"模型 {model_config.model_name} 不支持JSON Schema，使用文本解析模式生成内容页")
                # 使用普通LLM调用，然后解析文本响应
                response = await call_llm(
                    messages=messages,
                    flag="ppt_content_slides_array_generation",
                    model=model_config.model_name,
                    apiKey=model_config.api_key,
                    apiUrl=model_config.api_url
                )

                if not response:
                    raise ValueError("大模型返回空响应")

                # 解析文本响应为内容页数组
                slides = self._parse_text_response_to_content_slides(response, slide_title, pages_count)
                logger.info(f"成功生成标题 '{slide_title}' 的 {len(slides)} 页内容（文本解析模式）")
                return slides

        except Exception as e:
            logger.error(f"根据标题生成多页内容失败: {str(e)}")
            # 返回默认内容页作为fallback
            default_slides = []
            for page_num in range(pages_count):
                default_slide = self._get_default_content_slide(slide_title, slide_index, page_num + 1)
                default_slides.append(default_slide)
            return default_slides



    async def generate_ppt_content_from_document_staged_array(
        self, 
        document_content: str, 
        document_title: str,
        ppt_type: str = "graduation",
        max_content_slides: int = None,
        current_user: User = None
    ) -> Dict[str, Any]:
        """
        分阶段并发生成PPT内容的主方法（保持顺序输出）
        
        核心特性：
        - 第一阶段：顺序生成基础信息和内容页标题
        - 第二阶段：并发生成各标题的详细内容，但保持结果顺序
        - 智能容量分配：根据模板实际容量动态分配页数
        
        Args:
            document_content: 文档文本内容
            document_title: 文档标题
            ppt_type: PPT模板类型
            max_content_slides: 模板支持的最大内容页数，如果为None则使用默认逻辑
            
        Returns:
            完整的PPT内容结构
            
        Performance:
            - 相比顺序处理，并发模式可提高3-5倍生成速度
            - 使用asyncio.gather()确保结果顺序与输入顺序一致
        """
        logger.info(f"开始分阶段并发生成PPT内容，标题: '{document_title}'")
        
        # 第一阶段：生成基础信息和内容页标题
        basic_info = await self.generate_ppt_basic_info_from_document(
            document_content, document_title, ppt_type, current_user
        )
        
        # 获取内容页标题
        content_slide_titles = basic_info.get("content_slide_titles", [])
        
        # 如果没有标题，生成默认标题（数量由LLM决定，不固定）
        if not content_slide_titles:
            content_slide_titles = [
                f"{document_title}概述与背景",
                f"{document_title}核心内容",
                f"{document_title}关键要点",
                f"{document_title}总结展望"
            ]
        
        # 数组模式：根据模板规格动态计算每个标题生成的页数
        total_titles = len(content_slide_titles)  # 使用实际的标题数量
        
        # 根据模板的实际内容页数量动态计算每个标题的页数
        if max_content_slides is not None and total_titles > 0:
            # 基础页数：平均分配模板页数
            pages_per_title = max_content_slides // total_titles
            # 余数页数：分配给前几个标题
            extra_pages = max_content_slides % total_titles
            
            # 确保每个标题至少有1页
            if pages_per_title == 0:
                pages_per_title = 1
                total_titles = min(total_titles, max_content_slides)  # 限制标题数量
                extra_pages = 0
        else:
            # 如果没有模板限制，使用默认配置
            pages_per_title = PPTConfig.DEFAULT_PAGES_PER_TITLE
            extra_pages = 0
        
        logger.info(f"智能分配策略：模板支持 {max_content_slides} 页，{total_titles} 个标题")
        logger.info(f"每个标题基础 {pages_per_title} 页，前 {extra_pages} 个标题额外 +1 页")
        logger.info(f"预计生成 {total_titles * pages_per_title + extra_pages} 页内容，完全匹配模板容量")
        
        # 第二阶段：并发生成各个内容页标题的详细内容（保持顺序）
        logger.info("🚀 开始并发生成内容页详细内容")
        
        # 创建并发任务
        tasks = []
        for i, slide_title in enumerate(content_slide_titles[:total_titles]):
            # 计算当前标题实际生成的页数（前extra_pages个标题多1页）
            current_pages = pages_per_title + (1 if i < extra_pages else 0)
            
            # 创建任务
            task = self._generate_single_title_content(
                document_content=document_content,
                document_title=document_title,
                slide_title=slide_title,
                slide_index=i,
                pages_count=current_pages,
                ppt_type=ppt_type,
                current_user=current_user
            )
            tasks.append(task)
        
        # 并发执行所有任务，保持顺序
        slides_results = await asyncio.gather(*tasks)
        
        # 按顺序合并所有结果
        content_slides = []
        for i, slides_array in enumerate(slides_results):
            content_slides.extend(slides_array)
            slide_title = content_slide_titles[i] if i < len(content_slide_titles) else f"标题{i+1}"
            logger.info(f"✅ 完成第 {i+1} 个标题内容生成: '{slide_title}', 共 {len(slides_array)} 页")
        
        # 组装完整的PPT内容
        complete_content = {
            "main_title": basic_info.get("main_title", document_title),
            "subtitle": basic_info.get("subtitle", "基于文档内容的智能演示"),
            "date": basic_info.get("date", ""),
            "author": basic_info.get("author", "AI智能助手"),
            "school": basic_info.get("school", ""),
            "guidance": basic_info.get("guidance", ""),
            "agenda": basic_info.get("agenda", []),
            "agenda_en": basic_info.get("agenda_en", []),
            "agenda_sub": basic_info.get("agenda_sub", []),
            "content_slides": content_slides,
            "contact_info": basic_info.get("contact_info", f"感谢您的关注 | 基于文档: {document_title}"),
            "document_source": f"来源文档: {document_title}"
        }
        
        logger.info(f"🎯 并发PPT内容生成完成，共 {len(content_slides)} 页内容，完全匹配模板 {max_content_slides} 页容量")
        return complete_content

    async def _generate_single_title_content(
        self,
        document_content: str,
        document_title: str,
        slide_title: str,
        slide_index: int,
        pages_count: int,
        ppt_type: str,
        current_user: User = None
    ) -> List[Dict[str, Any]]:
        """
        为单个标题生成内容页（包含异常处理）
        
        这是并发处理的基础单元，每个任务独立处理一个标题的内容生成，
        确保并发安全和异常隔离。
        
        Args:
            document_content: 文档文本内容
            document_title: 文档标题
            slide_title: 内容页标题
            slide_index: 标题索引
            pages_count: 该标题需要生成的页数
            ppt_type: PPT模板类型
            
        Returns:
            List[Dict[str, Any]]: 该标题生成的所有内容页数组
            
        Note:
            - 包含完整的异常处理，失败时返回默认内容
            - 确保每个任务的独立性，避免互相干扰
        """
        try:
            # 为标题生成指定页数的内容
            slides_array = await self.generate_content_slides_by_title(
                document_content, document_title, slide_title, slide_index, pages_count, ppt_type, current_user
            )
            return slides_array
        except Exception as e:
            logger.error(f"标题 '{slide_title}' (索引{slide_index}) 内容生成失败: {str(e)}")
            # 使用默认内容作为fallback
            default_slides = []
            for page_num in range(pages_count):
                default_slide = self._get_default_content_slide(slide_title, slide_index, page_num + 1)
                default_slides.append(default_slide)
            logger.info(f"已为标题 '{slide_title}' 生成 {len(default_slides)} 页默认内容")
            return default_slides
    
    def _get_content_slide_count_by_template(self, template_type: str) -> int:
        """根据PPT模板类型返回第一阶段要生成的内容页标题数量"""
        # 第一阶段固定生成DEFAULT_CONTENT_SLIDE_COUNT个标题
        # 第二阶段根据模板实际容量动态分配每个标题的页数
        count = PPTConfig.DEFAULT_CONTENT_SLIDE_COUNT
        logger.info(f"第一阶段固定生成 {count} 个内容页标题，第二阶段根据模板 '{template_type}' 容量动态分配页数")
        return count
    
    def _model_supports_json_schema(self, model_name: str) -> bool:
        """
        检查模型是否支持JSON Schema约束输出

        Args:
            model_name: 模型名称

        Returns:
            bool: 是否支持JSON Schema
        """
        from app.core.config import settings
        
        # 检查是否启用模型校验
        if not settings.ENABLE_JSON_SCHEMA_MODEL_CHECK:
            # 如果关闭校验，所有模型都使用文本解析模式
            logger.info(f"JSON Schema模型校验已关闭，模型 {model_name} 将使用文本解析模式")
            return False
        
        # 启用校验时，检查模型是否在支持列表中
        model_lower = model_name.lower()
        for supported_model in settings.JSON_SCHEMA_SUPPORTED_MODELS:
            if supported_model.lower() in model_lower:
                logger.info(f"模型 {model_name} 在支持列表中，将使用JSON Schema")
                return True

        # 不在支持列表中，使用文本解析模式
        logger.info(f"模型 {model_name} 不在支持列表中，将使用文本解析模式")
        return False

    def _extract_and_clean_json(self, response: str) -> str:
        """
        从响应中提取和清洗JSON内容

        Args:
            response: 模型的文本响应

        Returns:
            str: 清洗后的JSON字符串，如果没有找到返回None
        """
        import re

        # 尝试多种方式提取JSON
        json_patterns = [
            # 直接的JSON格式
            r'^\s*\{.*\}\s*$',
            # 被代码块包围的JSON
            r'```json\s*(\{.*?\})\s*```',
            r'```\s*(\{.*?\})\s*```',
            # 被其他文本包围的JSON
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, response, re.DOTALL | re.MULTILINE)
            for match in matches:
                # 如果是元组，取第一个元素（捕获组）
                json_str = match if isinstance(match, str) else match[0] if match else ""

                # 清洗JSON字符串
                cleaned = self._clean_json_string(json_str)
                if cleaned and self._is_valid_json_structure(cleaned):
                    return cleaned

        # 如果没有找到完整的JSON，尝试从文本中构建
        return self._construct_json_from_text(response)

    def _clean_json_string(self, json_str: str) -> str:
        """
        清洗JSON字符串

        Args:
            json_str: 原始JSON字符串

        Returns:
            str: 清洗后的JSON字符串
        """
        if not json_str.strip():
            return ""

        # 移除前后的空白字符
        cleaned = json_str.strip()

        # 确保以{开始，}结束
        if not cleaned.startswith('{'):
            # 查找第一个{
            start_idx = cleaned.find('{')
            if start_idx >= 0:
                cleaned = cleaned[start_idx:]
            else:
                return ""

        if not cleaned.endswith('}'):
            # 查找最后一个}
            end_idx = cleaned.rfind('}')
            if end_idx >= 0:
                cleaned = cleaned[:end_idx + 1]
            else:
                return ""

        # 修复常见的JSON格式问题
        # 1. 修复单引号为双引号
        cleaned = re.sub(r"'([^']*)':", r'"\1":', cleaned)
        cleaned = re.sub(r":\s*'([^']*)'", r': "\1"', cleaned)

        # 2. 修复缺失的逗号
        cleaned = re.sub(r'"\s*\n\s*"', '",\n"', cleaned)
        cleaned = re.sub(r'}\s*\n\s*{', '},\n{', cleaned)

        # 3. 移除多余的逗号
        cleaned = re.sub(r',\s*}', '}', cleaned)
        cleaned = re.sub(r',\s*]', ']', cleaned)

        return cleaned

    def _is_valid_json_structure(self, json_str: str) -> bool:
        """
        检查JSON字符串是否有效

        Args:
            json_str: JSON字符串

        Returns:
            bool: 是否有效
        """
        try:
            json.loads(json_str)
            return True
        except:
            return False

    def _construct_json_from_text(self, response: str) -> str:
        """
        从文本中构建JSON

        Args:
            response: 文本响应

        Returns:
            str: 构建的JSON字符串
        """
        # 这里可以实现从自然语言中提取信息并构建JSON的逻辑
        # 暂时返回空字符串，让后续的文本解析处理
        return ""

    def _validate_and_complete_basic_info(self, data: Dict[str, Any], content_slide_count: int) -> Dict[str, Any]:
        """
        验证和补全基础信息JSON结构

        Args:
            data: 解析的JSON数据
            content_slide_count: 需要的内容页标题数量

        Returns:
            Dict[str, Any]: 验证和补全后的数据
        """
        # 确保所有必需字段存在
        required_fields = {
            "main_title": "",
            "subtitle": "",
            "date": "",
            "author": "",
            "school": "",
            "guidance": "",
            "agenda": ["", "", "", ""],
            "agenda_en": ["", "", "", ""],
            "agenda_sub": ["", "", "", ""],
            "content_slide_titles": [""] * content_slide_count
        }

        for field, default_value in required_fields.items():
            if field not in data:
                data[field] = default_value
            elif field in ["agenda", "agenda_en", "agenda_sub"]:
                # 确保数组长度为4
                if not isinstance(data[field], list):
                    data[field] = default_value
                else:
                    while len(data[field]) < 4:
                        data[field].append("")
                    data[field] = data[field][:4]  # 只保留前4个
            elif field == "content_slide_titles":
                # 确保内容页标题数量正确
                if not isinstance(data[field], list):
                    data[field] = default_value
                else:
                    while len(data[field]) < content_slide_count:
                        data[field].append("")
                    data[field] = data[field][:content_slide_count]  # 只保留需要的数量

        return data

    def _validate_and_complete_content_slides(self, slides: List[Dict[str, Any]], slide_title: str, pages_count: int) -> List[Dict[str, Any]]:
        """
        验证和补全内容页slides结构

        Args:
            slides: 解析的slides数据
            slide_title: 内容页标题
            pages_count: 需要的页数

        Returns:
            List[Dict[str, Any]]: 验证和补全后的slides
        """
        if not isinstance(slides, list):
            slides = []

        # 确保有足够的页面
        while len(slides) < pages_count:
            slides.append({
                "title": f"{slide_title} (第{len(slides)+1}部分)",
                "points": []
            })

        # 只保留需要的页数
        slides = slides[:pages_count]

        # 验证每个页面的结构
        for i, slide in enumerate(slides):
            if not isinstance(slide, dict):
                slide = {}
                slides[i] = slide

            # 确保有title字段
            if "title" not in slide or not slide["title"]:
                slide["title"] = f"{slide_title} (第{i+1}部分)"

            # 确保有points字段且为数组
            if "points" not in slide or not isinstance(slide["points"], list):
                slide["points"] = []

            # 确保每页有4个要点
            points = slide["points"]
            while len(points) < 4:
                points.append({
                    "title": f"要点{len(points)+1}",
                    "content": f"关于{slide_title}的相关内容"
                })

            # 只保留前4个要点
            slide["points"] = points[:4]

            # 验证每个要点的结构
            for j, point in enumerate(slide["points"]):
                if not isinstance(point, dict):
                    point = {}
                    slide["points"][j] = point

                if "title" not in point or not point["title"]:
                    point["title"] = f"要点{j+1}"

                if "content" not in point or not point["content"]:
                    point["content"] = f"关于{slide_title}的相关内容"

        return slides

    def _parse_text_response_to_basic_info(self, response: str, content_slide_count: int) -> Dict[str, Any]:
        """
        解析文本响应为基础信息JSON格式

        Args:
            response: 模型的文本响应
            content_slide_count: 需要的内容页标题数量

        Returns:
            Dict[str, Any]: 解析后的基础信息
        """
        try:
            # 首先尝试提取和清洗JSON
            cleaned_json = self._extract_and_clean_json(response)
            if cleaned_json:
                try:
                    parsed_json = json.loads(cleaned_json)
                    logger.info("成功解析模型返回的JSON格式响应")
                    # 验证和补全JSON结构
                    return self._validate_and_complete_basic_info(parsed_json, content_slide_count)
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析失败: {str(e)}，尝试文本解析")

            # 如果不是JSON格式，尝试从文本中提取信息
            logger.info("使用文本解析模式提取基础信息")

            # 使用正则表达式或简单的文本解析
            import re

            # 初始化结果
            result = {
                "main_title": "",
                "subtitle": "",
                "date": "",
                "author": "",
                "school": "",
                "guidance": "",
                "agenda": ["", "", "", ""],
                "agenda_en": ["", "", "", ""],
                "agenda_sub": ["", "", "", ""],
                "content_slide_titles": [""] * content_slide_count
            }

            # 按行解析，更精确地提取各个字段
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 提取主标题
                if line.startswith('主标题:') or line.startswith('主标题：'):
                    result["main_title"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                elif line.startswith('main_title:') or line.startswith('main_title：'):
                    result["main_title"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

                # 提取副标题
                elif line.startswith('副标题:') or line.startswith('副标题：'):
                    result["subtitle"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                elif line.startswith('subtitle:') or line.startswith('subtitle：'):
                    result["subtitle"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

                # 提取日期
                elif line.startswith('日期:') or line.startswith('日期：'):
                    result["date"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                elif line.startswith('date:') or line.startswith('date：'):
                    result["date"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

                # 提取作者
                elif line.startswith('作者:') or line.startswith('作者：'):
                    result["author"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                elif line.startswith('author:') or line.startswith('author：'):
                    result["author"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

                # 提取学校
                elif line.startswith('学校:') or line.startswith('学校：'):
                    result["school"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                elif line.startswith('school:') or line.startswith('school：'):
                    result["school"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

                # 提取指导
                elif line.startswith('指导:') or line.startswith('指导：'):
                    result["guidance"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                elif line.startswith('guidance:') or line.startswith('guidance：'):
                    result["guidance"] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

                # 提取议程项目
                elif line.startswith('议程') and (':' in line or '：' in line):
                    # 提取议程编号和内容
                    if line.startswith('议程1:') or line.startswith('议程1：'):
                        result["agenda"][0] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('议程2:') or line.startswith('议程2：'):
                        result["agenda"][1] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('议程3:') or line.startswith('议程3：'):
                        result["agenda"][2] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('议程4:') or line.startswith('议程4：'):
                        result["agenda"][3] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

                # 提取议程英文
                elif line.startswith('议程英文') and (':' in line or '：' in line):
                    if line.startswith('议程英文1:') or line.startswith('议程英文1：'):
                        result["agenda_en"][0] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('议程英文2:') or line.startswith('议程英文2：'):
                        result["agenda_en"][1] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('议程英文3:') or line.startswith('议程英文3：'):
                        result["agenda_en"][2] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('议程英文4:') or line.startswith('议程英文4：'):
                        result["agenda_en"][3] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

                # 提取议程子标题
                elif line.startswith('议程子标题') and (':' in line or '：' in line):
                    if line.startswith('议程子标题1:') or line.startswith('议程子标题1：'):
                        result["agenda_sub"][0] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('议程子标题2:') or line.startswith('议程子标题2：'):
                        result["agenda_sub"][1] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('议程子标题3:') or line.startswith('议程子标题3：'):
                        result["agenda_sub"][2] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('议程子标题4:') or line.startswith('议程子标题4：'):
                        result["agenda_sub"][3] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

                # 提取内容页标题
                elif line.startswith('内容页标题') and (':' in line or '：' in line):
                    if line.startswith('内容页标题1:') or line.startswith('内容页标题1：'):
                        if len(result["content_slide_titles"]) > 0:
                            result["content_slide_titles"][0] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('内容页标题2:') or line.startswith('内容页标题2：'):
                        if len(result["content_slide_titles"]) > 1:
                            result["content_slide_titles"][1] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('内容页标题3:') or line.startswith('内容页标题3：'):
                        if len(result["content_slide_titles"]) > 2:
                            result["content_slide_titles"][2] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()
                    elif line.startswith('内容页标题4:') or line.startswith('内容页标题4：'):
                        if len(result["content_slide_titles"]) > 3:
                            result["content_slide_titles"][3] = line.split(':', 1)[1].strip() if ':' in line else line.split('：', 1)[1].strip()

            # 如果没有提取到足够的内容页标题，使用默认标题
            if not any(result["content_slide_titles"]):
                result["content_slide_titles"] = [
                    f"内容页{i+1}" for i in range(content_slide_count)
                ]

            logger.info(f"文本解析完成，提取到 {len([t for t in result['content_slide_titles'] if t])} 个内容页标题")
            return result

        except Exception as e:
            logger.error(f"文本解析失败: {str(e)}")
            # 返回基本的默认结构
            return {
                "main_title": "演示文稿",
                "subtitle": "基于文档内容生成",
                "date": "",
                "author": "",
                "school": "",
                "guidance": "",
                "agenda": ["背景介绍", "核心内容", "关键要点", "总结展望"],
                "agenda_en": ["Background", "Core Content", "Key Points", "Summary"],
                "agenda_sub": ["基础概述", "详细分析", "重点梳理", "未来规划"],
                "content_slide_titles": [f"内容页{i+1}" for i in range(content_slide_count)]
            }

    def _parse_text_response_to_content_slides(self, response: str, slide_title: str, pages_count: int) -> List[Dict[str, Any]]:
        """
        解析文本响应为内容页数组格式

        Args:
            response: 模型的文本响应
            slide_title: 内容页标题
            pages_count: 需要生成的页数

        Returns:
            List[Dict[str, Any]]: 解析后的内容页数组
        """
        try:
            # 首先尝试提取和清洗JSON
            cleaned_json = self._extract_and_clean_json(response)
            if cleaned_json:
                try:
                    parsed_json = json.loads(cleaned_json)
                    if "slides" in parsed_json:
                        slides = parsed_json["slides"]
                        logger.info(f"成功解析模型返回的JSON格式内容页响应，共 {len(slides)} 页")
                        # 验证和补全slides结构
                        return self._validate_and_complete_content_slides(slides, slide_title, pages_count)
                    else:
                        logger.warning("JSON中没有找到slides字段，尝试文本解析")
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析失败: {str(e)}，尝试文本解析内容页")

            # 如果不是JSON格式，尝试从文本中提取内容页信息
            logger.info("使用文本解析模式提取内容页信息")

            import re

            # 初始化结果数组
            slides = []

            # 尝试按页面分割文本
            # 查找页面分隔符，如"第1页"、"页面1"、"Page 1"等
            page_patterns = [
                r'第\s*(\d+)\s*页',
                r'页面\s*(\d+)',
                r'Page\s*(\d+)',
                r'(\d+)\s*\.',
                r'## (.+)',  # Markdown标题格式
                r'### (.+)'  # Markdown子标题格式
            ]

            # 尝试分割页面
            pages_text = []
            current_text = response

            # 简单按段落分割，每个段落作为一页
            paragraphs = [p.strip() for p in response.split('\n\n') if p.strip()]

            # 如果段落数量不够，按行分割
            if len(paragraphs) < pages_count:
                lines = [line.strip() for line in response.split('\n') if line.strip()]
                # 将行按组分配给页面
                lines_per_page = max(1, len(lines) // pages_count)
                for i in range(pages_count):
                    start_idx = i * lines_per_page
                    end_idx = start_idx + lines_per_page if i < pages_count - 1 else len(lines)
                    page_text = '\n'.join(lines[start_idx:end_idx])
                    pages_text.append(page_text)
            else:
                pages_text = paragraphs[:pages_count]

            # 首先尝试按照格式化输出解析
            formatted_slides = self._parse_formatted_content_slides(response, slide_title, pages_count)
            if formatted_slides:
                logger.info(f"成功使用格式化解析，生成 {len(formatted_slides)} 页内容")
                return formatted_slides

            # 如果格式化解析失败，使用通用文本解析
            # 为每个页面生成结构化内容
            for i, page_text in enumerate(pages_text):
                if i >= pages_count:
                    break

                # 提取要点
                points = self._extract_points_from_text(page_text)

                # 确保有4个要点
                while len(points) < 4:
                    points.append({
                        "title": f"要点{len(points)+1}",
                        "content": f"关于{slide_title}的相关内容"
                    })

                # 只保留前4个要点
                points = points[:4]

                slide = {
                    "title": f"{slide_title} (第{i+1}部分)",
                    "points": points
                }
                slides.append(slide)

            # 如果没有生成足够的页面，补充默认页面
            while len(slides) < pages_count:
                page_num = len(slides) + 1
                default_slide = {
                    "title": f"{slide_title} (第{page_num}部分)",
                    "points": [
                        {"title": f"要点1", "content": f"关于{slide_title}的第{page_num}部分内容"},
                        {"title": f"要点2", "content": f"关于{slide_title}的第{page_num}部分内容"},
                        {"title": f"要点3", "content": f"关于{slide_title}的第{page_num}部分内容"},
                        {"title": f"要点4", "content": f"关于{slide_title}的第{page_num}部分内容"}
                    ]
                }
                slides.append(default_slide)

            logger.info(f"文本解析完成，生成 {len(slides)} 页内容")
            return slides

        except Exception as e:
            logger.error(f"内容页文本解析失败: {str(e)}")
            # 返回默认内容页
            default_slides = []
            for i in range(pages_count):
                default_slide = {
                    "title": f"{slide_title} (第{i+1}部分)",
                    "points": [
                        {"title": f"要点1", "content": f"关于{slide_title}的第{i+1}部分内容"},
                        {"title": f"要点2", "content": f"关于{slide_title}的第{i+1}部分内容"},
                        {"title": f"要点3", "content": f"关于{slide_title}的第{i+1}部分内容"},
                        {"title": f"要点4", "content": f"关于{slide_title}的第{i+1}部分内容"}
                    ]
                }
                default_slides.append(default_slide)
            return default_slides

    def _extract_points_from_text(self, text: str) -> List[Dict[str, str]]:
        """
        从文本中提取要点信息

        Args:
            text: 文本内容

        Returns:
            List[Dict[str, str]]: 要点列表，每个要点包含title和content
        """
        import re

        points = []

        # 尝试匹配各种要点格式
        point_patterns = [
            r'(\d+)\.\s*(.+?)[:：]\s*(.+)',  # "1. 标题: 内容"
            r'[•·]\s*(.+?)[:：]\s*(.+)',     # "• 标题: 内容"
            r'-\s*(.+?)[:：]\s*(.+)',       # "- 标题: 内容"
            r'(\d+)\)\s*(.+?)[:：]\s*(.+)', # "1) 标题: 内容"
            r'【(.+?)】\s*(.+)',            # "【标题】内容"
            r'## (.+)',                     # Markdown格式
            r'### (.+)'                     # Markdown格式
        ]

        for pattern in point_patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.DOTALL)
            for match in matches:
                if len(match) == 3:  # 包含序号的格式
                    _, title, content = match
                    points.append({
                        "title": title.strip()[:50],  # 限制标题长度
                        "content": content.strip()[:200]  # 限制内容长度
                    })
                elif len(match) == 2:  # 不包含序号的格式
                    title, content = match
                    points.append({
                        "title": title.strip()[:50],
                        "content": content.strip()[:200]
                    })
                elif len(match) == 1:  # 只有标题的格式
                    title = match[0]
                    points.append({
                        "title": title.strip()[:50],
                        "content": f"关于{title.strip()}的详细内容"
                    })

        # 如果没有匹配到要点，尝试智能提取
        if not points:
            points = self._smart_extract_points_from_text(text)

        return points

    def _smart_extract_points_from_text(self, text: str) -> List[Dict[str, str]]:
        """
        智能从文本中提取要点信息

        Args:
            text: 文本内容

        Returns:
            List[Dict[str, str]]: 要点列表
        """
        import re

        points = []

        # 按句子分割文本
        sentences = re.split(r'[。！？\n]', text)
        sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 5]

        # 尝试识别标题模式
        title_patterns = [
            r'^(\d+)[\.\)、]\s*(.+)',  # "1. 内容" 或 "1) 内容" 或 "1、内容"
            r'^[一二三四五六七八九十]\s*[、\.]\s*(.+)',  # "一、内容"
            r'^[①②③④⑤⑥⑦⑧⑨⑩]\s*(.+)',  # "①内容"
            r'^[（\(]\s*[一二三四五六七八九十\d]+\s*[）\)]\s*(.+)',  # "(一)内容"
        ]

        for sentence in sentences[:8]:  # 最多处理8个句子
            sentence = sentence.strip()
            if len(sentence) < 5:
                continue

            # 检查是否匹配标题模式
            title_found = False
            for pattern in title_patterns:
                match = re.match(pattern, sentence)
                if match:
                    if len(match.groups()) == 2:  # 有序号的情况
                        title = match.group(2).strip()
                    else:  # 只有内容的情况
                        title = match.group(1).strip()

                    # 尝试分割标题和内容
                    if '：' in title or ':' in title:
                        parts = re.split('[：:]', title, 1)
                        if len(parts) == 2:
                            point_title, point_content = parts
                            points.append({
                                "title": point_title.strip()[:50],
                                "content": point_content.strip()[:200]
                            })
                        else:
                            points.append({
                                "title": title[:30],  # 取前30字符作为标题
                                "content": title[:200]
                            })
                    else:
                        # 如果没有明显的标题内容分割，智能分割
                        if len(title) > 30:
                            # 长句子，取前面部分作为标题
                            point_title = title[:20] + "..."
                            point_content = title
                        else:
                            # 短句子，整个作为标题，生成相关内容
                            point_title = title
                            point_content = f"关于{title}的详细说明和分析"

                        points.append({
                            "title": point_title[:50],
                            "content": point_content[:200]
                        })

                    title_found = True
                    break

            # 如果没有匹配到标题模式，但是句子有一定长度，也作为要点
            if not title_found and len(sentence) > 10:
                # 尝试分割标题和内容
                if '：' in sentence or ':' in sentence:
                    parts = re.split('[：:]', sentence, 1)
                    if len(parts) == 2:
                        title, content = parts
                        points.append({
                            "title": title.strip()[:50],
                            "content": content.strip()[:200]
                        })
                    else:
                        points.append({
                            "title": sentence[:20] + "..." if len(sentence) > 20 else sentence,
                            "content": sentence[:200]
                        })
                else:
                    # 智能提取关键词作为标题
                    keywords = self._extract_keywords_from_sentence(sentence)
                    if keywords:
                        points.append({
                            "title": keywords[:50],
                            "content": sentence[:200]
                        })
                    else:
                        points.append({
                            "title": sentence[:20] + "..." if len(sentence) > 20 else sentence,
                            "content": sentence[:200]
                        })

            # 最多提取4个要点
            if len(points) >= 4:
                break

        # 如果还是没有提取到足够的要点，补充默认要点
        while len(points) < 4:
            points.append({
                "title": f"要点{len(points)+1}",
                "content": "相关内容的详细说明"
            })

        return points[:4]  # 只返回前4个要点

    def _extract_keywords_from_sentence(self, sentence: str) -> str:
        """
        从句子中提取关键词作为标题

        Args:
            sentence: 句子内容

        Returns:
            str: 提取的关键词
        """
        import re

        # 移除标点符号
        clean_sentence = re.sub(r'[，。！？；：、""''（）【】\\s]+', '', sentence)

        # 如果句子很短，直接返回
        if len(clean_sentence) <= 10:
            return clean_sentence

        # 尝试提取前面的关键部分
        # 查找常见的关键词模式
        keyword_patterns = [
            r'(技术|方法|策略|方案|模式|系统|平台|框架|算法|模型)',
            r'(发展|应用|实施|构建|设计|优化|改进|创新)',
            r'(分析|研究|探讨|讨论|评估|验证|测试)',
        ]

        for pattern in keyword_patterns:
            matches = re.findall(pattern, sentence)
            if matches:
                # 找到关键词，提取包含关键词的短语
                for match in matches:
                    index = sentence.find(match)
                    if index >= 0:
                        # 提取关键词前后的内容
                        start = max(0, index - 5)
                        end = min(len(sentence), index + len(match) + 5)
                        keyword_phrase = sentence[start:end].strip()
                        if len(keyword_phrase) > 3:
                            return keyword_phrase

        # 如果没有找到关键词，返回前面的部分
        return sentence[:15] + "..." if len(sentence) > 15 else sentence

    def _parse_formatted_content_slides(self, response: str, slide_title: str, pages_count: int) -> List[Dict[str, Any]]:
        """
        解析格式化的内容页输出

        Args:
            response: 模型的文本响应
            slide_title: 内容页标题
            pages_count: 需要生成的页数

        Returns:
            List[Dict[str, Any]]: 解析后的内容页数组，如果解析失败返回None
        """
        try:
            import re

            slides = []

            # 按页面分割
            for page_num in range(1, pages_count + 1):
                # 查找页面标题
                page_title_pattern = f"第{page_num}页标题[:：]\\s*(.+)"
                page_title_match = re.search(page_title_pattern, response)

                if page_title_match:
                    page_title = page_title_match.group(1).strip()
                else:
                    page_title = f"{slide_title} (第{page_num}部分)"

                # 提取该页面的4个要点
                points = []
                for point_num in range(1, 5):
                    # 查找要点标题和内容
                    title_pattern = f"要点{point_num}标题[:：]\\s*(.+)"
                    content_pattern = f"要点{point_num}内容[:：]\\s*(.+)"

                    title_match = re.search(title_pattern, response)
                    content_match = re.search(content_pattern, response)

                    if title_match and content_match:
                        point_title = title_match.group(1).strip()
                        point_content = content_match.group(1).strip()

                        points.append({
                            "title": point_title[:50],  # 限制长度
                            "content": point_content[:200]  # 限制长度
                        })
                    else:
                        # 如果没有找到格式化的要点，添加默认要点
                        points.append({
                            "title": f"要点{point_num}",
                            "content": f"关于{slide_title}的第{page_num}页第{point_num}个要点内容"
                        })

                slide = {
                    "title": page_title,
                    "points": points
                }
                slides.append(slide)

            # 检查是否成功解析到有效内容
            valid_slides = 0
            for slide in slides:
                valid_points = 0
                for point in slide["points"]:
                    # 检查是否是有意义的标题（不是默认的"要点X"格式）
                    if not point["title"].startswith("要点") and len(point["title"]) > 3:
                        valid_points += 1
                    # 或者内容不是默认模板内容
                    elif not point["content"].startswith("关于") and len(point["content"]) > 30:
                        valid_points += 1

                # 如果这一页有至少2个有效要点，认为是有效页面
                if valid_points >= 2:
                    valid_slides += 1

            # 如果解析到的有效内容太少，返回None表示解析失败
            if valid_slides == 0:
                logger.info("格式化解析没有找到有效内容，将使用通用文本解析")
                return None

            logger.info(f"格式化解析成功，生成 {len(slides)} 页内容，其中 {valid_slides} 页包含有效内容")
            return slides

        except Exception as e:
            logger.error(f"格式化内容页解析失败: {str(e)}")
            return None

    def _create_dynamic_basic_info_schema(self, content_slide_count: int) -> Dict[str, Any]:
        """创建动态的基础信息JSON Schema（根据内容页数量调整）"""
        return {
            "type": "object",
            "properties": {
                "main_title": {
                    "type": "string",
                    "description": "PPT主标题"
                },
                "subtitle": {
                    "type": "string",
                    "description": "PPT副标题"
                },
                "date": {
                    "type": "string",
                    "description": "日期信息"
                },
                "author": {
                    "type": "string",
                    "description": "作者信息"
                },
                "school": {
                    "type": "string",
                    "description": "学校或机构信息"
                },
                "guidance": {
                    "type": "string",
                    "description": "指导信息"
                },
                "agenda": {
                    "type": "array",
                    "items": {"type": "string"},
                    "minItems": 4,
                    "maxItems": 4,
                    "description": "4个议程项目（中文）"
                },
                "agenda_en": {
                    "type": "array",
                    "items": {"type": "string"},
                    "minItems": 4,
                    "maxItems": 4,
                    "description": "4个议程项目（英文）"
                },
                "agenda_sub": {
                    "type": "array",
                    "items": {"type": "string"},
                    "minItems": 4,
                    "maxItems": 4,
                    "description": "4个议程项目的子标题"
                },
                "content_slide_titles": {
                    "type": "array",
                    "items": {"type": "string"},
                    "minItems": content_slide_count,
                    "maxItems": content_slide_count,
                    "description": f"{content_slide_count}个内容页标题"
                }
            },
            "required": [
                "main_title", "subtitle", "date", "author", "agenda",
                "agenda_en", "agenda_sub", "content_slide_titles"
            ],
            "additionalProperties": False
        }
    
    def _get_default_content_from_document(self, document_title: str, document_content: str) -> Dict[str, Any]:
        """基于文档生成默认PPT内容作为fallback"""
        from datetime import datetime
        
        # 简单分析文档内容，提取关键词
        content_preview = document_content[:1000]  # 取前1000字符
        
        return {
            "main_title": document_title,
            "subtitle": "基于文档内容的智能演示",
            "date": datetime.now().strftime("%Y年%m月"),
            "author": "AI智能助手",
            "agenda": [
                f"{document_title}背景介绍",
                "核心内容分析",
                "关键要点梳理",
                "实施建议",
                "总结与展望"
            ],
            "content_slides": [
                {
                    "title": f"{document_title}背景介绍",
                    "points": [
                        {"title": "文档内容概述", "content": "文档内容概述和背景"},
                        {"title": "主要讨论的问题或主题", "content": "主要讨论的问题或主题"},
                        {"title": "文档的重要性和价值", "content": "文档的重要性和价值"},
                        {"title": "预期达成的目标", "content": "预期达成的目标"}
                    ]
                },
                {
                    "title": "核心内容分析",
                    "points": [
                        {"title": "文档中的关键信息提取", "content": "文档中的关键信息提取"},
                        {"title": "主要观点和论述逻辑", "content": "主要观点和论述逻辑"},
                        {"title": "重要数据和事实依据", "content": "重要数据和事实依据"},
                        {"title": "核心概念和定义说明", "content": "核心概念和定义说明"}
                    ]
                },
                {
                    "title": "关键要点梳理",
                    "points": [
                        {"title": "文档的主要结论", "content": "文档的主要结论"},
                        {"title": "重要的发现和洞察", "content": "重要的发现和洞察"},
                        {"title": "需要关注的关键点", "content": "需要关注的关键点"},
                        {"title": "可行性分析和建议", "content": "可行性分析和建议"}
                    ]
                }
            ],
            "contact_info": f"感谢您的关注 | 基于文档: {document_title}",
            "document_source": f"来源文档: {document_title}"
        }

    def _get_default_basic_info_from_document(self, document_title: str, document_content: str, ppt_type: str = "graduation") -> Dict[str, Any]:
        """生成默认的基础信息作为fallback"""
        from datetime import datetime
        
        # 根据模板类型生成相应数量的内容页标题
        content_slide_count = self._get_content_slide_count_by_template(ppt_type)
        
        # 生成基础的内容页标题
        base_titles = [
            f"{document_title}概述与背景",
            "核心内容深度分析",
            "关键发现与洞察",
            "应用建议与展望",
            "实施方案与步骤",
            "风险评估与对策",
            "效果预期与评估",
            "资源需求与配置",
            "时间规划与里程碑",
            "团队协作与分工",
            "质量控制与监督",
            "总结与未来展望"
        ]
        
        # 根据需要的数量截取或扩展标题
        if content_slide_count <= len(base_titles):
            content_slide_titles = base_titles[:content_slide_count]
        else:
            # 如果需要的数量超过基础标题，则扩展标题
            content_slide_titles = base_titles.copy()
            for i in range(len(base_titles), content_slide_count):
                content_slide_titles.append(f"{document_title}深度解析{i-len(base_titles)+1}")
        
        return {
            "main_title": document_title,
            "subtitle": "基于文档内容的智能演示",
            "date": datetime.now().strftime("%Y年%m月"),
            "author": "AI智能助手",
            "school": "",
            "guidance": "",
            "agenda": [
                f"{document_title}背景介绍",
                "核心内容分析",
                "关键要点梳理",
                "实施建议",
                "总结与展望"
            ],
            "agenda_en": [
                "Background Introduction",
                "Core Content Analysis", 
                "Key Points Summary",
                "Implementation Suggestions",
                "Summary and Outlook"
            ],
            "agenda_sub": [
                "背景与意义",
                "深度解析",
                "要点归纳",
                "可行性方案",
                "未来展望"
            ],
            "content_slide_titles": content_slide_titles
        }

    def _get_default_content_slide(self, slide_title: str, slide_index: int, page_number: int = 1) -> Dict[str, Any]:
        """生成默认的内容页作为fallback"""
        if page_number == 1:
            # 第一页：基础内容
            return {
                "title": f"{slide_title} (第1部分)",
                "points": [
                    {"title": f"{slide_title}的核心概念", "content": f"关于{slide_title}的基本定义和核心概念"},
                    {"title": "基本原理和机制", "content": f"{slide_title}的基本原理和工作机制"},
                    {"title": "发展背景和历史", "content": f"{slide_title}的发展背景和历史沿革"},
                    {"title": "基础特征描述", "content": f"{slide_title}的基础特征和主要表现"}
                ]
            }
        else:
            # 第二页：深入内容
            return {
                "title": f"{slide_title} (第2部分)",
                "points": [
                    {"title": "深入分析和解读", "content": f"{slide_title}的深入分析和详细解读"},
                    {"title": "实际应用案例", "content": f"{slide_title}在实际工作中的典型应用案例"},
                    {"title": "发展趋势展望", "content": f"{slide_title}的未来发展趋势和展望"},
                    {"title": "重要性和价值", "content": f"{slide_title}的重要性和实际价值体现"}
                ]
            }

    async def generate_ppt_from_document(
        self, 
        document_content: str, 
        user_id: str,
        filename: str,
        ppt_type: str = "graduation",
        upload_file_record: UploadFileModel = None,
        original_file_path: str = None,
        current_user: User = None
    ) -> PPTGenerateResponse:
        """
        基于文档内容的完整PPT生成流程
        
        这是PPT生成服务的核心方法，提供从文档解析到PPT文件生成的完整流程。
        包括记录管理、目录创建、内容生成、文件创建等完整的业务逻辑。
        
        Args:
            document_content: 解析后的文档文本内容，作为PPT生成的数据源
            user_id: 用户ID，用于关联生成记录和权限控制
            filename: 原始文档文件名，用于生成PPT文件名和标题
            ppt_type: PPT模板类型，决定使用哪种模板和样式，默认为"business"
            upload_file_record: 可选的上传文件记录，用于关联原始文件
            original_file_path: 可选的原始文件路径，用于文件备份和管理
            
        Returns:
            PPTGenerateResponse: PPT生成响应对象，包含：
                - id: 生成记录ID
                - file_path: 生成的PPT文件相对路径
                - filename: PPT文件名
                - size: 文件大小
                - message: 生成结果消息
                
        Process:
            1. 验证用户和模型配置
            2. 生成安全的文件名和目录结构
            3. 创建PPT生成记录（初始状态为processing）
            4. 处理原始文件备份
            5. 调用分阶段内容生成方法
            6. 使用模板创建PPT文件
            7. 更新生成记录状态和文件信息
            8. 返回成功响应
            
        Error Handling:
            - 用户不存在：抛出ValueError异常
            - 模型配置缺失：抛出ValueError异常
            - 内容生成失败：更新记录状态为failed
            - 文件创建失败：更新记录状态为failed
            - 任何异常都会被捕获并记录到数据库
            
        Note:
            - 自动创建专用的目录结构（llm_file/ppt/{record_id}/）
            - 支持原始文件备份和关联
            - 有完整的错误处理和状态跟踪
            - 生成的文件路径为相对路径，便于部署
        """
        import time
        start_time = time.time()  # 记录开始时间
        ppt_record = None

        # saas用户预计费
        order_id = await wallet_service.pre_charge(current_user.id, AGENT.PPT.value)
        business_id = None

        try:
            # 1. 获取用户对象和模型配置
            user = await User.filter(id=user_id).first()
            if not user:
                raise ValueError(f"用户不存在: {user_id}")
            
            model_config = await get_user_model(
                current_user=current_user,
                use_case=UseCase.AI_PPT_GENERATE.value
            )
            logger.info(f"model_config = {model_config}")
            
            # 根据model_config.id获取数据库ORM模型
            db_model_config = await ModelConfig.filter(id=model_config.id).first()
            if not db_model_config:
                raise ValueError(f"模型配置不存在: {model_config.id}")
            
            # 2. 生成文档标题（从文件名提取）
            import os
            document_title = os.path.splitext(filename)[0]  # 移除扩展名作为标题
            file_ext = os.path.splitext(filename)[1].lower()
            
            # 3. 生成PPT文件名（基于文档标题和时间戳，不包含模板类型）
            from datetime import datetime
            import time
            from app.utils.utils import sanitize_filename
            
            # 使用毫秒级时间戳确保唯一性
            timestamp = int(time.time() * 1000)
            # 使用sanitize_filename确保文件名安全
            safe_title = sanitize_filename(document_title[:15].replace(' ', '_')) if document_title else "document_ppt"
            ppt_filename = f"ppt_{safe_title}_{timestamp}.pptx"
            
            # 4. 创建PPT生成记录（初始状态）
            ppt_record = await CoAiPpt.create(
                user=user,
                upload_file=upload_file_record,
                model_config=db_model_config,  # 使用数据库ORM模型而不是Pydantic模型
                original_filename=filename,
                original_file_path="",  # 暂时为空，创建目录后更新
                file_type=file_ext,
                ppt_filename=ppt_filename,
                ppt_file_path="",  # 暂时为空，生成成功后更新
                model_type=ppt_type,
                status="processing"
            )
            
            logger.info(f"PPT生成记录已创建，ID: {ppt_record.id}")

            # 用于saas计费
            business_id = ppt_record.id

            # 5. 获取原始文件路径（直接使用attachments目录的路径）
            new_original_file_path = ""
            if upload_file_record and upload_file_record.file_path:
                new_original_file_path = upload_file_record.file_path
                logger.info(f"原始文件路径: {new_original_file_path}")
            else:
                logger.warning("没有找到上传文件记录或文件路径")
            
            # 6. 检查模板内容页容量
            max_content_slides = self.get_template_content_slide_count_by_type(ppt_type)
            
            # 7. 基于文档内容和模板容量分阶段并发生成PPT内容
            logger.info("🚀 调用并发模式生成PPT内容")
            content = await self.generate_ppt_content_from_document_staged_array(
                document_content, document_title, ppt_type, max_content_slides, current_user
            )
            logger.info(f"🎯 并发模式生成完成，内容页数量: {len(content.get('content_slides', []))}")
            
            # 8. 创建PPT文件（保存到attachments目录）
            from datetime import datetime
            import os
            import time
            
            # 生成文件保存路径（与save_upload_file相同的逻辑）
            date_str = datetime.now().strftime("%Y%m%d")
            attachments_path = f"attachments/{date_str}"
            os.makedirs(attachments_path, exist_ok=True)
            
            # 生成带时间戳的文件名（使用与save_upload_file相同的时间戳生成方式）
            timestamp = int(time.time() * 1000)  # 使用毫秒级时间戳，与文件上传保持一致
            ppt_filename_with_timestamp = f"{timestamp}_{ppt_filename}"
            
            relative_ppt_path = f"{attachments_path}/{ppt_filename_with_timestamp}"
            absolute_ppt_path = os.path.join(os.getcwd(), relative_ppt_path)
            
            # 创建PPT文件
            await self.create_ppt_file_direct(content, absolute_ppt_path, ppt_type)
            
            # 9. 获取文件信息
            if not os.path.exists(absolute_ppt_path):
                raise ValueError("PPT文件生成失败")

            file_size = os.path.getsize(absolute_ppt_path)

            if settings.FILE_MODE != "local":
                with open(absolute_ppt_path, "rb") as f:
                    ppt_bytes = f.read()
                upload_file.write(relative_ppt_path, ppt_bytes)
                remove_local_file(relative_ppt_path)

            output_filename = os.path.basename(relative_ppt_path)
            
            # 10. 计算生成耗时并更新PPT生成记录状态为成功
            end_time = time.time()
            generation_duration = int(end_time - start_time)  # 计算耗时（秒）
            
            await ppt_record.update_from_dict({
                "original_file_path": new_original_file_path,
                "ppt_file_path": relative_ppt_path,
                "ppt_file_size": file_size,
                "generation_duration": generation_duration,
                "status": "completed"
            })
            await ppt_record.save()
            
            logger.info(f"PPT生成成功，记录ID: {ppt_record.id}, 文件路径: {relative_ppt_path}, 耗时: {generation_duration}秒")

            # 异步 计费确认
            asyncio.create_task(wallet_service.charge_confirm(current_user.id, order_id, business_id))

            # 11. 构建响应对象
            response = PPTGenerateResponse(
                id=str(ppt_record.id),
                file_path=relative_ppt_path,
                filename=output_filename,
                size=file_size,
                generation_time=generation_duration,
                message=f"基于文档 '{filename}' 生成PPT成功"
            )
            
            return response
            
        except Exception as e:
            # 异步 计费取消
            asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, business_id))
            # 计算生成耗时并更新PPT生成记录状态为失败
            if ppt_record:
                try:
                    end_time = time.time()
                    generation_duration = int(end_time - start_time)  # 计算耗时（秒）
                    
                    await ppt_record.update_from_dict({
                        "status": "failed",
                        "generation_duration": generation_duration,
                        "error_message": str(e)
                    })
                    await ppt_record.save()
                    logger.error(f"PPT生成失败，记录ID: {ppt_record.id}, 耗时: {generation_duration}秒, 错误: {str(e)}")
                except Exception as update_error:
                    logger.error(f"更新PPT记录状态失败: {str(update_error)}")
            
            logger.error(f"基于文档生成PPT失败: {str(e)}")
            raise


    

    

    

    
    async def create_ppt_file_direct(self, content: Dict[str, Any], file_path: str, ppt_type: str = "graduation") -> None:
        """
        直接创建PPT文件到指定路径
        
        Args:
            content: PPT内容数据
            file_path: 完整的文件路径（绝对路径）
            ppt_type: PPT模板类型
        """
        logger.info(f"开始创建PPT文件到: {file_path}, 模板类型: {ppt_type}")
        
        # 根据模板类型选择模板文件
        template_filename = self._get_template_filename(ppt_type)
        template_path = os.path.join(self.template_dir, template_filename)
        
        if not os.path.exists(template_path):
            logger.warning(f"模板文件不存在: {template_path}，将创建基础PPT")
            # 如果模板不存在，创建一个基础的PPT
            prs = Presentation()
        else:
            logger.info(f"加载模板文件: {template_path}")
            prs = Presentation(template_path)
        
        # 替换模板中的占位符
        self._replace_placeholders_in_presentation(prs, content)
        
        # 处理动态内容页面
        self._handle_dynamic_content_slides(prs, content)
        
        # 保存文件到指定路径
        prs.save(file_path)
        
        logger.info(f"PPT文件创建成功: {file_path}")
    
    def _get_template_filename(self, ppt_type: str) -> str:
        """
        根据模板类型返回对应的模板文件名
        
        Args:
            ppt_type: PPT模板类型
            
        Returns:
            模板文件名
        """
        template_mapping = {
            PPTTemplateType.GRADUATION.value: "graduation_template.pptx",
            PPTTemplateType.CREATIVE.value: "creative_template.pptx",
            PPTTemplateType.SIMPLE.value: "simple_template.pptx"
        }
        
        template_filename = template_mapping.get(ppt_type, "graduation_template.pptx")
        logger.info(f"模板类型 '{ppt_type}' 对应的模板文件: {template_filename}")
        return template_filename
    
    def get_template_content_slide_count_by_type(self, ppt_type: str) -> int:
        """
        根据模板类型获取内容页的容量
        
        Args:
            ppt_type: PPT模板类型
            
        Returns:
            模板支持的内容页数量
        """
        max_slides = PPTConfig.TEMPLATE_SLIDE_COUNTS.get(ppt_type, PPTConfig.DEFAULT_CONTENT_SLIDE_COUNT * PPTConfig.DEFAULT_PAGES_PER_TITLE)
        logger.info(f"模板类型 '{ppt_type}' 支持的最大内容页数: {max_slides}")
        return max_slides
    
    def get_template_content_slide_count(self, template_path: str = None) -> int:
        """
        获取模板中内容页的数量
        
        Args:
            template_path: 模板文件路径，如果为None则使用默认模板
            
        Returns:
            模板中内容页的数量
        """
        if template_path is None:
            template_path = os.path.join(self.template_dir, "graduation_template.pptx")
        
        if not os.path.exists(template_path):
            logger.warning(f"模板文件不存在: {template_path}")
            return 3  # 返回默认值
        
        try:
            prs = Presentation(template_path)
            content_template_indices = self._find_content_slide_templates(prs)
            count = len(content_template_indices)
            logger.info(f"模板 {template_path} 包含 {count} 页内容页")
            return count
        except Exception as e:
            logger.error(f"读取模板文件失败: {str(e)}")
            return 3  # 返回默认值
    
    def _replace_placeholders_in_presentation(self, prs: Presentation, content: Dict[str, Any]):
        """替换演示文稿中的所有占位符"""
        logger.info("开始替换模板占位符")
        
        # 准备替换数据
        replacements = self._prepare_replacement_data(content)
        
        # 遍历所有幻灯片
        for slide_idx, slide in enumerate(prs.slides):
            logger.debug(f"处理第 {slide_idx + 1} 页幻灯片")
            
            # 遍历幻灯片中的所有形状
            for shape in slide.shapes:
                if hasattr(shape, "text_frame") and shape.text_frame:
                    self._replace_text_in_shape(shape, replacements)
                elif hasattr(shape, "text") and shape.text:
                    # 处理简单文本形状
                    for placeholder, value in replacements.items():
                        if placeholder in shape.text:
                            shape.text = shape.text.replace(placeholder, value)
        
        logger.info("占位符替换完成")
    
    def _prepare_replacement_data(self, content: Dict[str, Any]) -> Dict[str, str]:
        """准备替换数据"""
        replacements = {
            "{{main_title}}": content.get("main_title", "演示文稿"),
            "{{subtitle}}": content.get("subtitle", "自动生成"),
            "{{date}}": content.get("date", "2023年12月"),
            "{{author}}": content.get("author", "AI助手"),
            "{{school}}": content.get("school", ""),
            "{{guidance}}": content.get("guidance", ""),
            "{{contact_info}}": content.get("contact_info", "感谢您的关注"),
            "{{document_source}}": content.get("document_source", "")
        }
        
        # 处理目录项
        agenda = content.get("agenda", [])
        for i in range(1, 6):  # 支持5个目录项
            key = f"{{{{agenda_item_{i}}}}}"
            if i <= len(agenda):
                replacements[key] = agenda[i-1]
            else:
                replacements[key] = ""  # 空字符串，如果没有对应项目
        
        # agenda_item_en_
        agenda_en = content.get("agenda_en", [])
        for i in range(1, 6):
            key = f"{{{{agenda_item_en_{i}}}}}"
            if i <= len(agenda_en):
                replacements[key] = agenda_en[i-1]
            else:
                replacements[key] = ""
        # agenda_item_sub_
        agenda_sub = content.get("agenda_sub", [])
        for i in range(1, 6):
            key = f"{{{{agenda_item_sub_{i}}}}}"
            if i <= len(agenda_sub):
                replacements[key] = agenda_sub[i-1]
            else:
                replacements[key] = ""
        

        
        return replacements
    
    def _replace_text_in_shape(self, shape, replacements: Dict[str, str]):
        """替换形状中的文本 - 保留格式版本"""
        if not hasattr(shape, "text_frame") or not shape.text_frame:
            return
            
        # 处理文本框中的所有段落
        for paragraph in shape.text_frame.paragraphs:
            # 处理段落中的所有runs
            for run in paragraph.runs:
                original_text = run.text
                modified_text = original_text
                
                # 替换占位符
                for placeholder, value in replacements.items():
                    if placeholder in modified_text:
                        modified_text = modified_text.replace(placeholder, value)
                
                # 如果文本发生了变化，更新run文本（保留格式）
                if modified_text != original_text:
                    run.text = modified_text
    
    def _find_content_slide_templates(self, prs: Presentation) -> List[int]:
        """查找所有内容页模板页面索引"""
        content_slide_indices = []
        
        for idx, slide in enumerate(prs.slides):
            # 检查是否包含内容页占位符
            has_content_placeholder = False
            
            for shape in slide.shapes:
                if hasattr(shape, "text_frame") and shape.text_frame:
                    text = shape.text_frame.text
                    # 检查是否包含内容页特有的占位符
                    if ("{{slide_title}}" in text or 
                        "{{slide_title_2}}" in text or
                        "{{point_1_title}}" in text or 
                        "{{point_1_content}}" in text or
                        "{{left_column_content}}" in text or
                        "{{right_column_content}}" in text or
                        "{{point_" in text):
                        has_content_placeholder = True
                        break
            
            if has_content_placeholder:
                content_slide_indices.append(idx)
                logger.info(f"✓ 发现内容页模板：第 {idx + 1} 页")
            else:
                logger.debug(f"✗ 第 {idx + 1} 页不是内容页模板")
        
        logger.info(f"共发现 {len(content_slide_indices)} 个内容页模板: {[i+1 for i in content_slide_indices]}")
        return content_slide_indices
    
    def _handle_dynamic_content_slides(self, prs: Presentation, content: Dict[str, Any]):
        """处理动态内容页面 - 只使用模板中现有的内容页，不创建新页面"""
        logger.info("开始处理动态内容页面")
        
        content_slides = content.get("content_slides", [])
        if not content_slides:
            logger.info("没有内容页面需要处理")
            return
        
        # 查找所有内容页模板
        content_template_indices = self._find_content_slide_templates(prs)
        
        if not content_template_indices:
            logger.warning("模板中没有找到内容页模板，跳过动态内容处理")
            return
        
        # 只处理模板允许的数量，超出的内容丢弃
        max_content_slides = len(content_template_indices)
        actual_content_slides = content_slides[:max_content_slides]
        
        logger.info(f"模板提供 {max_content_slides} 页内容页，生成了 {len(content_slides)} 页内容")
        if len(content_slides) > max_content_slides:
            logger.info(f"将使用前 {max_content_slides} 页内容，多余的 {len(content_slides) - max_content_slides} 页内容将被忽略")
        
        # 填充现有的内容页模板
        for idx, slide_content in enumerate(actual_content_slides):
            template_slide_idx = content_template_indices[idx]
            template_slide = prs.slides[template_slide_idx]
            
            # 特别关注第13页的调试信息
            if template_slide_idx + 1 == 13:
                logger.info(f"🔍 开始调试第13页内容填充")
                logger.info(f"🔍 第13页内容: {slide_content}")
                
                # 检查第13页的占位符
                for shape in template_slide.shapes:
                    if hasattr(shape, "text_frame") and shape.text_frame:
                        text = shape.text_frame.text
                        if "{{" in text:
                            logger.info(f"🔍 第13页发现占位符: {repr(text)}")
            
            self._fill_content_slide(template_slide, slide_content, idx)
            logger.info(f"填充第 {template_slide_idx + 1} 页内容页模板")
        
        logger.info(f"动态内容页面处理完成，共使用 {len(actual_content_slides)} 个模板页面")
    
    def _duplicate_slide(self, prs: Presentation, template_slide):
        """复制幻灯片 - 改进版本，更完整地复制模板页面"""
        try:
            # 获取模板布局
            slide_layout = template_slide.slide_layout
            
            # 创建新幻灯片
            new_slide = prs.slides.add_slide(slide_layout)
            
            # 复制所有形状和内容
            for shape in template_slide.shapes:
                try:
                    if hasattr(shape, "text_frame") and shape.text_frame:
                        # 创建文本框
                        new_shape = new_slide.shapes.add_textbox(
                            shape.left, shape.top, shape.width, shape.height
                        )
                        # 复制文本内容
                        new_shape.text_frame.text = shape.text_frame.text
                        
                        # 复制段落格式
                        for i, paragraph in enumerate(shape.text_frame.paragraphs):
                            if i < len(new_shape.text_frame.paragraphs):
                                new_para = new_shape.text_frame.paragraphs[i]
                            else:
                                new_para = new_shape.text_frame.add_paragraph()
                            
                            # 复制段落文本和格式
                            new_para.text = paragraph.text
                            new_para.alignment = paragraph.alignment
                            
                            # 复制字体格式
                            if paragraph.font:
                                new_para.font.name = paragraph.font.name
                                new_para.font.size = paragraph.font.size
                                new_para.font.bold = paragraph.font.bold
                                new_para.font.italic = paragraph.font.italic
                    
                    elif hasattr(shape, "shape_type"):
                        # 对于其他类型的形状，尝试基本复制
                        logger.debug(f"跳过复制形状类型: {shape.shape_type}")
                        
                except Exception as shape_error:
                    logger.warning(f"复制形状时出错: {str(shape_error)}")
                    continue
            
            logger.info("成功复制模板页面")
            return new_slide
            
        except Exception as e:
            logger.error(f"复制幻灯片失败: {str(e)}")
            # 如果复制失败，创建一个基本的内容页
            slide_layout = prs.slide_layouts[1]  # 通常是内容页布局
            new_slide = prs.slides.add_slide(slide_layout)
            
            # 添加基本的占位符文本
            if new_slide.shapes.title:
                new_slide.shapes.title.text = "{{slide_title}}"
            
            # 添加内容占位符
            content_placeholder = None
            for shape in new_slide.placeholders:
                if hasattr(shape, "text_frame"):
                    content_placeholder = shape
                    break
                    
            if content_placeholder and hasattr(content_placeholder, "text_frame"):
                content_placeholder.text_frame.text = """{{point_1_title}}
{{point_1_content}}

{{point_2_title}}
{{point_2_content}}

{{point_3_title}}
{{point_3_content}}

{{point_4_title}}
{{point_4_content}}"""
            
            logger.info("创建了基本的内容页作为备选")
            return new_slide
    
    def _fill_content_slide(self, slide, slide_content: Dict[str, Any], slide_idx: int):
        """填充内容页 - 支持三种格式：新格式、旧格式、两栏格式"""
        title = slide_content.get("title", f"内容页 {slide_idx + 1}")
        points = slide_content.get("points", [])
        
        # 检测页面格式类型
        format_type, all_text_content = self._detect_slide_format(slide)
        
        logger.info(f"🔍 处理页面 {slide_idx + 1}，格式类型: {format_type}")
        logger.debug(f"开始填充内容页 {slide_idx + 1}，标题: '{title}', 要点数量: {len(points)}")
        
        # 构建替换字典
        replacements = self._build_replacements(title, points, format_type)
        
        # 执行占位符替换
        self._replace_placeholders_in_slide(slide, replacements)
        
        logger.debug(f"内容页 {slide_idx + 1} 填充完成，总形状: {len(slide.shapes)}")
    
    def _detect_slide_format(self, slide) -> tuple:
        """检测幻灯片格式类型"""
        all_text_content = ""
        
        # 收集所有文本内容
        for shape in slide.shapes:
            if hasattr(shape, "text_frame") and shape.text_frame:
                for paragraph in shape.text_frame.paragraphs:
                    for run in paragraph.runs:
                        if run.text:
                            all_text_content += run.text + " "
        
        # 定义占位符模式
        two_column_patterns = ["{{slide_title_2}}", "{{left_column_content}}", "{{right_column_content}}"]
        new_format_patterns = ["{{point_1_title}}", "{{point_2_title}}", "{{point_3_title}}", "{{point_4_title}}"]
        old_format_patterns = ["{{slide_title}}"]
        
        # 检测格式类型
        has_two_column = any(pattern in all_text_content for pattern in two_column_patterns)
        has_new_format = any(pattern in all_text_content for pattern in new_format_patterns)
        has_old_format = any(pattern in all_text_content for pattern in old_format_patterns) and not has_two_column
        
        # 确定格式类型
        if has_two_column:
            return "两栏格式", all_text_content
        elif has_new_format and has_old_format:
            return "混合格式", all_text_content
        elif has_new_format:
            return "新格式", all_text_content
        elif has_old_format:
            return "旧格式", all_text_content
        else:
            return "未知格式", all_text_content
    
    def _build_replacements(self, title: str, points: list, format_type: str) -> Dict[str, str]:
        """构建占位符替换字典"""
        replacements = {
            # 通用占位符
            "{{slide_title}}": title,
            "{{slide_title_2}}": title,
        }
        
        # 添加新格式和旧格式占位符
        point_keys = [
            ("{{point_1_title}}", "{{point_1_content}}", "{{content_point_1}}"),
            ("{{point_2_title}}", "{{point_2_content}}", "{{content_point_2}}"),
            ("{{point_3_title}}", "{{point_3_content}}", "{{content_point_3}}"),
            ("{{point_4_title}}", "{{point_4_content}}", "{{content_point_4}}")
        ]
        
        for i, (title_key, content_key, old_key) in enumerate(point_keys):
            point = points[i] if i < len(points) else {}
            # 新格式占位符（详细格式）
            replacements[title_key] = point.get("title", "")
            replacements[content_key] = point.get("content", "")
            # 旧格式占位符（向后兼容）
            replacements[old_key] = point.get("content", "")
        
        # 根据格式类型添加特殊处理
        if format_type == "两栏格式":
            self._add_two_column_content(replacements, points)
        elif format_type == "旧格式":
            self._add_old_format_content(replacements, points)
        
        return replacements
    
    def _add_two_column_content(self, replacements: Dict[str, str], points: list):
        """添加两栏格式的内容"""
        # 将points分为左右两列
        left_points = points[:2] if len(points) >= 2 else points
        right_points = points[2:4] if len(points) > 2 else []
        
        # 构建左栏内容
        left_content = self._build_column_content(left_points)
        right_content = self._build_column_content(right_points)
        
        replacements["{{left_column_content}}"] = left_content
        replacements["{{right_column_content}}"] = right_content
        
        logger.info(f"🔍 两栏格式处理完成，左栏: {len(left_content)} 字符，右栏: {len(right_content)} 字符")
    
    def _add_old_format_content(self, replacements: Dict[str, str], points: list):
        """添加旧格式的组合内容"""
        combined_content = ""
        for point in points[:4]:
            if point.get("title") and point.get("content"):
                combined_content += f"{point['title']}\n{point['content']}\n\n"
        
        replacements["{{main_content}}"] = combined_content.strip()
        replacements["{{content}}"] = combined_content.strip()
        
        logger.info(f"🔍 旧格式处理完成，组合内容长度: {len(combined_content)}")
    
    def _build_column_content(self, points: list) -> str:
        """构建列内容"""
        content = ""
        for point in points:
            if point.get("title") and point.get("content"):
                content += f"• {point['title']}\n{point['content']}\n\n"
        return content.strip()
    
    def _replace_placeholders_in_slide(self, slide, replacements: Dict[str, str]):
        """在幻灯片中替换占位符"""
        replacements_made = 0
        
        for shape in slide.shapes:
            if hasattr(shape, "text_frame") and shape.text_frame:
                shape_replacements = self._replace_text_in_shape_preserve_format(shape, replacements)
                replacements_made += shape_replacements
        
        if replacements_made > 0:
            logger.debug(f"共进行了 {replacements_made} 次占位符替换")
    
    def _replace_text_in_shape_preserve_format(self, shape, replacements: Dict[str, str]) -> int:
        """保留格式的文本替换 - 专用于内容页"""
        if not hasattr(shape, "text_frame") or not shape.text_frame:
            return 0
        
        replacements_made = 0
        
        for paragraph in shape.text_frame.paragraphs:
            for run in paragraph.runs:
                original_text = run.text
                modified_text = original_text
                
                # 替换所有占位符
                for placeholder, value in replacements.items():
                    if placeholder in modified_text:
                        modified_text = modified_text.replace(placeholder, value)
                        replacements_made += 1
                
                # 更新文本，保留原有格式
                if modified_text != original_text:
                    run.text = modified_text
        
        return replacements_made
    

    

    

    



# 创建全局PPT服务实例
ppt_service = PPTService() 
