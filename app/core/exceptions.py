from typing import Any, Dict, Optional
from fastapi import HTTPException


class SystemConfigError(Exception):
    """系统配置异常基类"""
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "SYSTEM_CONFIG_ERROR"
        self.details = details or {}


class ConfigValidationError(SystemConfigError):
    """配置验证错误"""
    def __init__(self, message: str, field: str = None, value: Any = None):
        super().__init__(message, "CONFIG_VALIDATION_ERROR")
        self.field = field
        self.value = value


class ConfigPermissionError(SystemConfigError):
    """配置权限错误"""
    def __init__(self, message: str = "权限不足"):
        super().__init__(message, "CONFIG_PERMISSION_ERROR")


class ConfigNotFoundError(SystemConfigError):
    """配置不存在错误"""
    def __init__(self, key: str):
        super().__init__(f"配置项 {key} 不存在", "CONFIG_NOT_FOUND_ERROR")
        self.key = key


class ConfigSyncError(SystemConfigError):
    """配置同步错误"""
    def __init__(self, message: str, sync_details: Dict[str, Any] = None):
        super().__init__(message, "CONFIG_SYNC_ERROR", sync_details)


class CacheError(SystemConfigError):
    """缓存操作错误"""
    def __init__(self, message: str, operation: str = None):
        super().__init__(message, "CACHE_ERROR")
        self.operation = operation

class PreChargeException(Exception):
    """预计费失败"""

    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "PRE_CHARGE_EXCEPTION"
        self.details = details or {}


def create_error_response(error: SystemConfigError) -> Dict[str, Any]:
    """创建统一的错误响应格式"""
    response = {
        "success": False,
        "error": {
            "code": error.error_code,
            "message": error.message,
            "type": error.__class__.__name__
        }
    }
    
    if error.details:
        response["error"]["details"] = error.details
    
    if hasattr(error, 'field'):
        response["error"]["field"] = error.field
    
    if hasattr(error, 'key'):
        response["error"]["key"] = error.key
    
    return response


def create_success_response(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
    """创建统一的成功响应格式"""
    response = {
        "success": True,
        "message": message
    }
    
    if data is not None:
        response["data"] = data
    
    return response 