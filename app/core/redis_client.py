# import aioredis
import asyncio
from redis.asyncio import Redis
from app.core.config import settings
from app.core.logging import get_logger
from app.utils.llm_interaction_manager import InteractionStatusManager

logger = get_logger(__name__)

# 单例 Redis 客户端
redis_client = None
llm_interaction_instance = None

def get_redis_client():
    """
    获取全局 Redis 客户端（异步连接池）
    """
    global redis_client
    global llm_interaction_instance
    if redis_client is None:
        redis_client = Redis.from_url(
          url=settings.REDIS_URL,
          decode_responses=True
      )
    logger.info(f"RedisClient - Redis连接已创建 URL:{settings.REDIS_URL}")
    return redis_client

def get_llm_interaction_instance():
    global llm_interaction_instance
    if llm_interaction_instance is None:
        client = get_redis_client()
        llm_interaction_instance = InteractionStatusManager(redis=client)
        logger.info("RedisClient - LLM交互管理器初始化成功")
    return llm_interaction_instance
# 用于 FastAPI 生命周期事件的关闭钩子
async def close_redis_client():
    global redis_client
    if redis_client is not None:
        await redis_client.close()
        redis_client = None 