import os
from typing import Optional, List
from dotenv import load_dotenv

# 临时解决方案：直接使用BaseModel代替BaseSettings
from pydantic import BaseModel

class BaseSettings(BaseModel):
    """临时的BaseSettings替代方案"""
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 显式加载.env文件
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), ".env")
if os.path.exists(dotenv_path):
    print(f"config.py: 加载.env文件: {dotenv_path}")
    load_dotenv(dotenv_path, override=True)

# 不要在顶层导入db_crypto，避免循环导入
# 将导入移到需要使用的方法内部

class Settings(BaseSettings):
    PROJECT_NAME: str = "Hi-IdeaGen"
    PROJECT_VERSION: str = "0.1.0"
    PROJECT_DESCRIPTION: str = "智能研究报告生成系统"
    
    # 服务配置
    PORT: int = int(os.environ.get("PORT", "8001"))
    HOST: str = os.environ.get("HOST", "0.0.0.0")
    
    # 数据库配置 - 存储加密后的连接字符串
    _DATABASE_URL: str = os.environ.get("DATABASE_URL", "postgres://postgres:postgres@localhost:5432/hi_ideagen")
    
    @property
    def DATABASE_URL(self) -> str:
        """获取解密后的数据库连接URL"""
        # 在方法内部导入，避免循环导入
        from app.utils.db_crypto import decrypt_db_url, is_encrypted_db_url
        if is_encrypted_db_url(self._DATABASE_URL):
            return decrypt_db_url(self._DATABASE_URL)
        return self._DATABASE_URL
    # JWT配置
    SECRET_KEY: str = os.environ.get("SECRET_KEY", "your-secret-key-to-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_SECONDS: int = int(os.environ.get("ACCESS_TOKEN_EXPIRE_SECONDS", str(60 * 60 * 24 * 14)))  # 14天有效期
    
    # 默认管理员配置
    DEFAULT_ADMIN_USERNAME: str = os.environ.get("DEFAULT_ADMIN_USERNAME", "idea_admin")
    DEFAULT_ADMIN_PASSWORD: str = os.environ.get("DEFAULT_ADMIN_PASSWORD", "VD9liPmHg$4G0mNv")
    DEFAULT_SUPER_ADMIN_MOBILE: str = os.environ.get("DEFAULT_SUPER_ADMIN_MOBILE", "13666666666")
    DEFAULT_ORG_ADMIN_MOBILE: str = os.environ.get("DEFAULT_ORG_ADMIN_MOBILE", "13999999999")
    # API密钥前缀
    API_KEY_PREFIX: str = os.environ.get("API_KEY_PREFIX", "sk-")
    
    # 加密配置
    CRYPTO_BASE_KEY: str = os.environ.get("CRYPTO_BASE_KEY", "5UYxQwbK3tJnMrVfL8zSgHe5R2NdAa7Z")
    
    # 研究配置
    # OPENROUTER_API_KEY: str = os.environ.get("OPENROUTER_API_KEY", "")
    SERPAPI_API_KEY: str = os.environ.get("SERPAPI_API_KEY", "")
    JINA_API_KEY: str = os.environ.get("JINA_API_KEY", "")
    
     # PubMed配置
    PUBMED_TOOL_NAME: str = os.environ.get("PUBMED_TOOL_NAME", "co-mpanion")
    PUBMED_EMAIL: str = os.environ.get("PUBMED_EMAIL", "")
    
    # API端点
    OPENROUTER_URL: str = "https://openrouter.ai/api/v1/chat/completions"
    SERPAPI_URL: str = "https://serpapi.com/search"
    SERPAPI_GOOGLE_SCHOLAR_URL: str = "https://serpapi.com/search?engine=google_scholar"
    JINA_BASE_URL: str = "https://r.jina.ai/"
    JINA_SEARCH_URL: str = "https://s.jina.ai/"
    OPENROUTER_API_URL: str = "https://openrouter.ai/api/v1"
    # 搜索配置
    SEARCH_RESULTS_LIMIT: int = int(os.environ.get("SEARCH_RESULTS_LIMIT", "30"))
    # DEFAULT_MODEL: str = os.environ.get("DEFAULT_MODEL", "anthropic/claude-3.7-sonnet")
    RESEARCH_ITERATION_LIMIT: int = int(os.environ.get("RESEARCH_ITERATION_LIMIT", "5"))
    MAX_SEARCH_QUERIES: int = int(os.environ.get("MAX_SEARCH_QUERIES", "20")) #最多处理的搜索查询
    MAX_CONTEXTS: int = int(os.environ.get("MAX_CONTEXTS", "5"))  # 使用配置的最大上下文数量
    MAX_LITERATURE_AND_CONTEXTS: int = int(os.environ.get("MAX_LITERATURE_AND_CONTEXTS", "35"))
    MAX_LITERATURES_COUNT: int = int(os.environ.get("MAX_LITERATURE_AND_CONTEXTS", "35"))  # 使用配置的最大文献数量
    MAX_HALLUCINATION_DETECTION_SEARCH: int = int(os.environ.get("MAX_HALLUCINATION_DETECTION_SEARCH", "5"))  # # 幻觉审查的google搜索条目数
    SEARCH_ENGINE  : str = os.environ.get("SEARCH_ENGINE", "google,jina") #搜索引擎
    PICTURE_DATA_SEARCH_ENGINE  : str = os.environ.get("PICTURE_DATA_SEARCH_ENGINE", "google,jina") #搜索引擎
    LITERATURE_LIBRARY_SEARCH_ENGINE  : str = os.environ.get("LITERATURE_LIBRARY_SEARCH_ENGINE", "google_scholar") #参考文献谷歌搜索引擎
    # Jina直接内容配置 - 是否直接使用Jina搜索返回的内容，而不是通过URL重新获取
    JINA_DIRECT_CONTENT: bool = os.environ.get("JINA_DIRECT_CONTENT", "OFF").upper() == "ON"

    # LLM参数
    LLM_DEFAULT_TEMPERATURE: float = float(os.environ.get("LLM_DEFAULT_TEMPERATURE", "1"))
    LLM_DEFAULT_TOP_K: int = int(os.environ.get("LLM_DEFAULT_TOP_K", "80"))
    LLM_DEFAULT_TOP_P: float = float(os.environ.get("LLM_DEFAULT_TOP_P", "0.8"))
    
    # JSON Schema支持的模型列表
    JSON_SCHEMA_SUPPORTED_MODELS: List[str] = os.environ.get("JSON_SCHEMA_SUPPORTED_MODELS", "").split(",") if os.environ.get("JSON_SCHEMA_SUPPORTED_MODELS") else []
    
    # JSON Schema模型校验开关
    # 开启时使用JSON_SCHEMA_SUPPORTED_MODELS列表进行校验，关闭时所有模型都使用文本解析
    ENABLE_JSON_SCHEMA_MODEL_CHECK: bool = os.environ.get("ENABLE_JSON_SCHEMA_MODEL_CHECK", "ON").upper() == "ON"
    
    # 日志配置
    LOG_LEVEL: str = os.environ.get("LOG_LEVEL", "INFO")
    LOG_ROOT_DIR: str = os.environ.get("LOG_ROOT_DIR", "./logs")  # Docker宿主机挂载目录
    LOG_DIR: str = os.environ.get("LOG_DIR", "./logs")  # 应用程序内部日志目录
    LOG_RETENTION_DAYS: int = int(os.environ.get("LOG_RETENTION_DAYS", "30"))
    TZ: str = os.environ.get("TZ", "Asia/Shanghai")  # 时区配置
    
    # Redis配置
    REDIS_URL: str = os.environ.get("REDIS_URL", "redis://localhost:6379/10")
    IS_OPEN_SSO: str = os.environ.get("IS_OPEN_SSO", "OFF")
    DIFY_API_URL: str = os.environ.get("DIFY_API_URL", "")
    DIFY_API_KEY: str = os.environ.get("DIFY_API_KEY", "")
    
    # 短信服务配置
    ALIBABA_CLOUD_ACCESS_KEY_ID: str = os.environ.get("ALIBABA_CLOUD_ACCESS_KEY_ID", "")
    ALIBABA_CLOUD_ACCESS_KEY_SECRET: str = os.environ.get("ALIBABA_CLOUD_ACCESS_KEY_SECRET", "")
    SMS_SIGN_NAME: str = os.environ.get("SMS_SIGN_NAME", "")
    SMS_TEMPLATE_CODE: str = os.environ.get("SMS_TEMPLATE_CODE", "")
    SMS_CODE_EXPIRE_TIME: int = int(os.environ.get("SMS_CODE_EXPIRE_TIME", "300"))
    SMS_SEND_INTERVAL: int = int(os.environ.get("SMS_SEND_INTERVAL", "60"))

    # 测试环境固定验证码配置
    SMS_TEST_MODE: str = os.environ.get("SMS_TEST_MODE", "OFF")  # ON/OFF 是否启用测试模式
    SMS_TEST_CODE: str = os.environ.get("SMS_TEST_CODE", "654321")  # 测试用固定验证码
    DIFY_AI_CHAT_API_KEY: str = os.environ.get("DIFY_AI_CHAT_API_KEY", "")
    DIFY_AI_HOMEWORK_API_KEY: str = os.environ.get("DIFY_AI_HOMEWORK_API_KEY", "")

    # 论文要求
    PAPER_WORD_COUNT: int= os.environ.get("PAPER_WORD_COUNT", "15000")
    
    # PPT生成配置
    PPT_MAX_CONTENT_LENGTH: int = int(os.environ.get("PPT_MAX_CONTENT_LENGTH", "20000"))  # 默认15,000字符

    # 图片生成的搜索关键词数量
    FIGURE_SEARCH_KEYWORD_COUNT: int = int(os.environ.get("FIGURE_SEARCH_KEYWORD_COUNT", "1"))
    # 图片生成所需数据的谷歌搜索条目数
    FIGURE_GOOGLE_SEARCH_LIMIT: int = int(os.environ.get("FIGURE_GOOGLE_SEARCH_LIMIT", "1"))
    # 前端访问的基础路径
    WEB_BASE_URL: str= os.environ.get("WEB_BASE_URL", "http://localhost:8000")

    # 下载token配置，默认有效期24小时
    DOWNLOAD_TOKEN_EXPIRE_HOURS: int = int(os.environ.get("DOWNLOAD_TOKEN_EXPIRE_HOURS", "24"))
    
    # OnlyOffice配置
    ONLYOFFICE_CALLBACK_BASE_URL: str = os.environ.get("ONLYOFFICE_CALLBACK_BASE_URL", "")

    # Aliyun OSS
    OSS_ACCESS_KEY_ID: str = os.environ.get("OSS_ACCESS_KEY_ID", "")
    OSS_ACCESS_KEY_SECRET: str = os.environ.get("OSS_ACCESS_KEY_SECRET", "")
    OSS_BUCKET: str = os.environ.get("OSS_BUCKET", "")
    OSS_ENDPOINT: str = os.environ.get("OSS_ENDPOINT", "")
    OSS_REGION: str = os.environ.get("OSS_REGION", "")
    FILE_MODE: str = os.environ.get("FILE_MODE", "local")

    # SaaS登录配置
    SAAS_LOGIN_AS_DEFAULT_ORG: str = os.environ.get("SAAS_LOGIN_AS_DEFAULT_ORG", "ON")
    SAAS_LOGIN_MAX_ALLOWED_COUNT: int = int(os.environ.get("SAAS_LOGIN_MAX_ALLOWED_COUNT", "10"))
    AES_ENCRYPT_KEY: str = os.environ.get("AES_ENCRYPT_KEY", "XTLSZDIqgDNe1Bw2DSFB2HS79VSSAukQ")
    HMAC_KEY: str = os.environ.get("HMAC_KEY", "TPXU8MuL5Ic5cvVaswlcJnipgV7qs8PD")
    AES_SALT: str = os.environ.get("AES_SALT", "salt")
    HMAC_SALT: str = os.environ.get("HMAC_SALT", "hmac-salt")

    # 本地数据存储
    APP_INSTANCE_ID: str = os.environ.get("APP_INSTANCE_ID", "INSTANCE_1")
    # 大模型交互场景数据的有效时长（单位秒）默认12小时
    LLM_INTERACTION_DATA_EXPIRE_TIME: int = int(os.environ.get("LLM_INTERACTION_DATA_EXPIRE_TIME", "43200"))
    AI_TRACES_SAME_MOMENT_COUNT: int = int(os.environ.get("AI_TRACES_SAME_MOMENT_COUNT", "3"))
    class Config:
        env_file = dotenv_path


settings = Settings()
