import asyncio
import time
from typing import Optional, Callable, Any
from app.core.logging import get_logger

logger = get_logger(__name__)


class DistributedCacheLock:
    """分布式缓存锁机制"""
    
    def __init__(self, redis_client):
        self._redis_client = redis_client
        self._lock_prefix = "cache_lock:"
        self._default_timeout = 30  # 增加默认锁超时时间到30秒
        self._retry_attempts = 3    # 重试次数
        self._retry_delay = 1       # 重试延迟（秒）
    
    def _get_lock_key(self, resource_key: str) -> str:
        """生成锁键"""
        return f"{self._lock_prefix}{resource_key}"
    
    async def acquire_lock(self, resource_key: str, timeout: int = None, retry_attempts: int = None) -> bool:
        """
        获取分布式锁
        
        Args:
            resource_key: 资源键
            timeout: 锁超时时间（秒）
            retry_attempts: 重试次数
        
        Returns:
            bool: 是否成功获取锁
        """
        lock_key = self._get_lock_key(resource_key)
        lock_timeout = timeout or self._default_timeout
        retry_count = retry_attempts or self._retry_attempts
        
        for attempt in range(retry_count):
            try:
                # 检查Redis连接
                await self._redis_client.ping()
                
                lock_value = str(time.time())
                
                # 使用SET NX EX命令原子性地设置锁
                result = await self._redis_client.set(
                    lock_key, 
                    lock_value, 
                    ex=lock_timeout, 
                    nx=True
                )
                
                if result:
                    logger.debug(f"成功获取锁: {resource_key} (尝试 {attempt + 1}/{retry_count})")
                    return True
                else:
                    logger.warning(f"获取锁失败，资源被占用: {resource_key} (尝试 {attempt + 1}/{retry_count})")
                    
                    # 如果不是最后一次尝试，等待后重试
                    if attempt < retry_count - 1:
                        await asyncio.sleep(self._retry_delay)
                    
            except Exception as e:
                logger.error(f"获取锁时出错 (尝试 {attempt + 1}/{retry_count}): {e}")
                if attempt < retry_count - 1:
                    await asyncio.sleep(self._retry_delay)
        
        logger.error(f"获取锁失败，已达到最大重试次数: {resource_key}")
        return False
    
    async def release_lock(self, resource_key: str) -> bool:
        """
        释放分布式锁
        
        Args:
            resource_key: 资源键
        
        Returns:
            bool: 是否成功释放锁
        """
        lock_key = self._get_lock_key(resource_key)
        
        try:
            # 检查Redis连接
            await self._redis_client.ping()
            
            # 直接删除锁键，简化释放逻辑
            result = await self._redis_client.delete(lock_key)
            
            if result:
                logger.debug(f"成功释放锁: {resource_key}")
                return True
            else:
                logger.debug(f"释放锁失败，锁不存在: {resource_key}")
                return False
                
        except Exception as e:
            logger.error(f"释放锁时出错: {e}")
            return False
    
    async def force_release_lock(self, resource_key: str) -> bool:
        """
        强制释放锁（用于清理死锁）
        
        Args:
            resource_key: 资源键
        
        Returns:
            bool: 是否成功释放锁
        """
        lock_key = self._get_lock_key(resource_key)
        
        try:
            # 检查Redis连接
            await self._redis_client.ping()
            
            # 直接删除锁键
            result = await self._redis_client.delete(lock_key)
            
            if result:
                logger.warning(f"强制释放锁: {resource_key}")
                return True
            else:
                logger.debug(f"强制释放锁失败，锁不存在: {resource_key}")
                return False
                
        except Exception as e:
            logger.error(f"强制释放锁时出错: {e}")
            return False
    
    async def with_lock(self, resource_key: str, func: Callable, *args, **kwargs) -> Any:
        """
        使用锁执行函数
        
        Args:
            resource_key: 资源键
            func: 要执行的函数
            *args, **kwargs: 函数参数
        
        Returns:
            函数执行结果
        """
        lock_acquired = False
        try:
            # 尝试获取锁
            lock_acquired = await self.acquire_lock(resource_key)
            if not lock_acquired:
                raise Exception(f"无法获取锁: {resource_key}")
            
            # 执行函数
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            return result
            
        finally:
            # 确保释放锁
            if lock_acquired:
                await self.release_lock(resource_key)


class CacheConsistencyManager:
    """缓存一致性管理器"""
    
    def __init__(self, redis_client):
        self._redis_client = redis_client
        self._lock_manager = DistributedCacheLock(redis_client)
        self._cache_invalidation_channel = "cache_invalidation"
    
    async def invalidate_cache_pattern(self, pattern: str):
        """使指定模式的缓存失效"""
        try:
            keys = await self._redis_client.keys(pattern)
            if keys:
                await self._redis_client.delete(*keys)
                logger.info(f"已清除缓存模式: {pattern}, 清除 {len(keys)} 个键")
            
            # 发布缓存失效消息
            await self._redis_client.publish(
                self._cache_invalidation_channel, 
                f"pattern:{pattern}"
            )
            
        except Exception as e:
            logger.error(f"清除缓存模式失败: {e}")
    
    async def subscribe_cache_invalidation(self):
        """订阅缓存失效消息"""
        pubsub = self._redis_client.pubsub()
        await pubsub.subscribe(self._cache_invalidation_channel)
        
        async for message in pubsub.listen():
            if message['type'] == 'message':
                pattern = message['data'].decode('utf-8').split(':', 1)[1]
                logger.info(f"收到缓存失效消息: {pattern}")
                # 可以在这里添加额外的缓存清理逻辑 