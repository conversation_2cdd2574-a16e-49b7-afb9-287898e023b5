from datetime import datetime, timezone, timedelta
from typing import Optional
import uuid
import secrets
import string

from jose import jwt
from passlib.context import Crypt<PERSON>ontext
from app.core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def get_password_hash(password: str) -> str:
    """获取密码的哈希值"""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def create_access_token(subject: str, expires_delta: Optional[timedelta] = None) -> str:
    """创建JWT访问令牌"""
    if expires_delta:
        expire = datetime.now(timezone(timedelta(hours=8))) + expires_delta
    else:
        expire = datetime.now(timezone(timedelta(hours=8))) + timedelta(
            seconds=settings.ACCESS_TOKEN_EXPIRE_SECONDS
        )
    
    to_encode = {"exp": expire, "sub": subject}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def generate_api_key() -> str:
    """生成API密钥"""
    # 生成24位随机字符
    alphabet = string.ascii_letters + string.digits
    random_part = ''.join(secrets.choice(alphabet) for _ in range(24))
    # 拼接前缀
    return f"{settings.API_KEY_PREFIX}{random_part}"


def generate_complex_password(length: int = None) -> str:
    """
    生成8-15位复杂随机密码
    
    密码包含：
    - 至少1个大写字母
    - 至少1个小写字母  
    - 至少1个数字
    - 至少1个特殊字符
    
    Args:
        length: 密码长度，如果不指定则随机生成8-15位
        
    Returns:
        str: 生成的复杂密码
    """
    if length is None:
        # 随机生成8-15位长度
        length = secrets.randbelow(8) + 8  # 8-15位随机长度
    
    # 确保长度在合理范围内
    length = max(8, min(15, length))
    
    # 定义字符集合
    uppercase = string.ascii_uppercase
    lowercase = string.ascii_lowercase
    digits = string.digits
    special_chars = "!@#$%^&*"
    
    # 确保至少包含每种类型的字符
    password = [
        secrets.choice(uppercase),
        secrets.choice(lowercase),
        secrets.choice(digits),
        secrets.choice(special_chars)
    ]
    
    # 填充剩余长度
    all_chars = uppercase + lowercase + digits + special_chars
    for _ in range(length - 4):
        password.append(secrets.choice(all_chars))
    
    # 随机打乱顺序
    secrets.SystemRandom().shuffle(password)
    
    return ''.join(password)


def create_download_token(ppt_id: str, user_id: str, expires_delta: Optional[timedelta] = None) -> str:
    """
    创建PPT文件下载令牌

    Args:
        ppt_id: PPT记录ID
        user_id: 用户ID
        expires_delta: 过期时间，默认1小时

    Returns:
        str: 下载令牌
    """
    if expires_delta:
        expire = datetime.now(timezone(timedelta(hours=8))) + expires_delta
    else:
        expire = datetime.now(timezone(timedelta(hours=8))) + timedelta(hours=1)  # 默认1小时过期

    to_encode = {
        "exp": expire,
        "ppt_id": ppt_id,
        "user_id": user_id,
        "type": "download"
    }
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_download_token(token: str) -> Optional[dict]:
    """
    验证下载令牌

    Args:
        token: 下载令牌

    Returns:
        dict: 包含ppt_id和user_id的字典，验证失败返回None
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        if payload.get("type") != "download":
            return None
        return {
            "ppt_id": payload.get("ppt_id"),
            "user_id": payload.get("user_id")
        }
    except jwt.JWTError:
        return None