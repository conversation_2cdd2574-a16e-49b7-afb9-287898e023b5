import asyncio
from app.utils.utils import RequestIDMiddleware
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import Response
from tortoise.contrib.fastapi import register_tortoise
from fastapi.staticfiles import StaticFiles
from app.core.config import settings
from app.db.config import TORTOISE_ORM
from app.api.routes import api_router
from app.models.user import User
from app.models.role import Role
from app.models.user_report_usage import UserReportUsage
from app.core.security import get_password_hash
from fastapi.exceptions import RequestValidationError, HTTPException
from app.utils.validate_error import (
    custom_validation_exception_handler,
    custom_no_auth_handler
)
from app.core.exceptions import SystemConfigError, create_error_response
from pydantic import BaseModel
from fastapi.openapi.docs import (
    get_swagger_ui_html,
)
import os
import json
from app.core.redis_client import get_redis_client, close_redis_client, get_llm_interaction_instance
from app.api.repository.organizations import create_org_and_admin
from app.api.schemas.organizations import OrganizationCreate
from app.api.schemas.role import InsetRole
# from app.api.routes.statics import oss_router

llm_interaction_instance = get_llm_interaction_instance()
class SecureStaticFiles(StaticFiles):
    """安全的静态文件服务，需要token验证"""

    async def __call__(self, scope, receive, send):
        """重写调用方法，添加token验证"""
        if scope["type"] == "http":
            request = Request(scope, receive)

            # 检查是否是PPT文件请求
            if request.url.path.startswith("/static/attachments/") and request.url.path.endswith(".pptx"):
                # 验证token
                token = request.query_params.get("token")
                if not token:
                    response = Response("需要下载令牌", status_code=401)
                    await response(scope, receive, send)
                    return

                # 验证token有效性
                from app.core.security import verify_download_token
                token_data = verify_download_token(token)
                if not token_data:
                    response = Response("下载令牌无效或已过期", status_code=403)
                    await response(scope, receive, send)
                    return

                # 验证文件权限（可选：进一步验证用户是否有权限下载该文件）
                # 这里可以添加更多的权限检查逻辑

        # 调用父类方法处理请求
        await super().__call__(scope, receive, send)
class CustomValidationResponse(BaseModel):
    success: bool = False
    code: int = 422
    data: None
    error: str


app = FastAPI(
    docs_url=None, redoc_url=None,
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    version=settings.PROJECT_VERSION,
    # swagger_js_url= "https://unpkg.com/swagger-ui-dist@5/swagger-ui-bundle.js",
    # swagger_css_url= "https://unpkg.com/swagger-ui-dist@5/swagger-ui.css"
)

app.add_exception_handler(RequestValidationError, custom_validation_exception_handler)
app.add_exception_handler(HTTPException, custom_no_auth_handler)

# 添加系统配置异常处理器
async def system_config_exception_handler(request: Request, exc: SystemConfigError):
    """系统配置异常处理器"""
    from fastapi.responses import JSONResponse
    return JSONResponse(
        status_code=400,
        content=create_error_response(exc)
    )

app.add_exception_handler(SystemConfigError, system_config_exception_handler)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# 增加请求唯一id
app.add_middleware(
    RequestIDMiddleware
)

# 注册路由
app.include_router(api_router)
# app.mount("/pictures", StaticFiles(directory="pictures"), name="pictures")
# 配置安全的静态文件服务（仅用于PPT文件下载）
# 使用自定义的SecureStaticFiles类，需要token验证
if os.path.exists("attachments"):
    app.mount("/static/attachments", SecureStaticFiles(directory="attachments"), name="attachments")

# 👇 OpenAPI patch：修改文档中 422 的默认结构
original_openapi = app.openapi


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = original_openapi()

    # 替换所有 path 下的 422 响应结构
    for path in openapi_schema["paths"].values():
        for method in path.values():
            responses = method.get("responses", {})
            if "422" in responses:
                responses["422"] = {
                    "description": "Custom Validation Error",
                    "content": {
                        "application/json": {
                            "schema": CustomValidationResponse.schema()
                        }
                    }
                }

    app.openapi_schema = openapi_schema
    return app.openapi_schema


# 应用覆盖
app.openapi = custom_openapi

# 注册Tortoise ORM
register_tortoise(
    app,
    config=TORTOISE_ORM,
    generate_schemas=True,
    add_exception_handlers=True,
)


@app.on_event("startup")
async def startup_event():
    """应用启动时执行的事件"""
    if settings.IS_OPEN_SSO == 'ON':
        # 初始化 Redis 连接
        get_redis_client()
    
    # 同步系统配置
    from app.services.system_config_service import system_config_service
    try:
        sync_result = await system_config_service.sync_default_configs()
        print(f"系统配置同步完成: {sync_result}")
    except Exception as e:
        print(f"系统配置同步失败: {e}")
    
    # 初始化默认管理员用户
    admin_user = await User.filter(username=settings.DEFAULT_ADMIN_USERNAME).first()

    if not admin_user:
        super_role = await Role.create(
            name="超级管理员",
            identifier=InsetRole.SUPER_ADMIN.value
        )
        admin_user = User(
            username=settings.DEFAULT_ADMIN_USERNAME,
            mobile=settings.DEFAULT_SUPER_ADMIN_MOBILE,
            hashed_password=get_password_hash(settings.DEFAULT_ADMIN_PASSWORD),
            role=super_role
        )
        await admin_user.save()
        # 创建管理员用户的报告使用记录
        await UserReportUsage.create(
            user_id=admin_user,
            used_count=0,  # 初始使用次数为0
            max_allowed_count=None  # 管理员不限制使用次数
        )
        # 创建默认机构和机构管理员
        await create_org_and_admin(OrganizationCreate.model_validate(
            {
                "name": "默认机构管理员",
                "type": "事业单位",
                "contact_person": "默认机构管理员",
                "contact_phone": settings.DEFAULT_ORG_ADMIN_MOBILE,             
                "code": "default_org",
                "is_active": True,
                "contact_email": "<EMAIL>",  # 电子邮箱（必填）
                "limit_count": 10000,  # 机构总可用次数（必填）
            }
        , from_attributes=True))
        print(f"已创建默认管理员用户: {settings.DEFAULT_ADMIN_USERNAME}")
    # 生成swagger.json
    swagger_file_path = "scripts/swagger.json"

    # 检查文件是否存在
    if os.path.exists(swagger_file_path):
        print(f"Swagger文件已存在: {swagger_file_path}，跳过生成")
    else:
        print(f"正在生成Swagger文件: {swagger_file_path}")
        with open(swagger_file_path, "w") as f:
            json.dump(app.openapi(), f, indent=2)
        print("Swagger文件生成完成")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行的事件"""
    if settings.IS_OPEN_SSO == 'ON':
        await close_redis_client()
    # 应用关闭前把本应用实例的大模型交互的标识符数据全部移除
    await llm_interaction_instance.batch_end_interaction()

@app.get("/")
async def root():
    """根路径响应"""
    return {
        "message": f"欢迎使用 {settings.PROJECT_NAME} API",
        "version": settings.PROJECT_VERSION,
        "docs": "/docs",
    }
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://unpkg.com/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://unpkg.com/swagger-ui-dist@5/swagger-ui.css",
    )