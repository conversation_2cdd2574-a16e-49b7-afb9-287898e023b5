import uuid
from tortoise import fields
from tortoise.models import Model
from app.utils.enum import CoVerifyStatus


class CoVerify(Model):
    """co验证模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    ai_report = fields.TextField(null=True, description="AI报告内容")
    status = fields.CharEnumField(CoVerifyStatus, default=CoVerifyStatus.NO_START, description="验证状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    # 外键关联
    file = fields.ForeignKeyField(
        "models.UploadFile", 
        related_name="co_verify_files", 
        null=True, 
        description="关联的上传文件"
    )
    user = fields.ForeignKeyField(
        "models.User", 
        related_name="co_verify_users", 
        null=True, 
        description="关联的用户"
    )

    class Meta:
        table = "co_verify"
        description = "co验证表"
    
    def __str__(self):
        return f"CoVerify({self.id})" 