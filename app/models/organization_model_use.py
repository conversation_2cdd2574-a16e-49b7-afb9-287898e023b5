from tortoise import fields
from tortoise.models import Model
from enum import Enum
from app.utils.enum import UseCase

class OrganizationModelUses(Model):
    id = fields.UUIDField(pk=True)
    organization = fields.ForeignKeyField('models.Organizations', related_name='use_related_model', description="机构")
    model = fields.ForeignKeyField('models.ModelConfig', related_name='use_related_organization', description="模型")
    default_way = fields.CharEnumField(
        UseCase,
        description="模型的默认用途"
    )
    is_thinking = fields.BooleanField(default=False, null=True, description="是否开启思考模式")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "organization_model_uses"
        table_description = "机构模型的用途表"