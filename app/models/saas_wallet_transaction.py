import uuid
from tortoise import fields
from tortoise.models import Model

from app.services.wallet.interface import TransactionStatus


class WalletTransaction(Model):
    """SaaS交易订单表"""
    
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    
    # 业务信息
    user_id = fields.CharField(max_length=100, description="用户ID")
    count = fields.IntField(description="使用次数")
    agent_id = fields.CharField(max_length=100, description="智能体")

    # 平台信息
    platform = fields.CharField(max_length=50, description="第三方平台")
    
    # 交易信息
    pre_charge_id = fields.CharField(max_length=200, null=True, description="预扣费ID")
    business_id = fields.CharField(max_length=200, null=True, description="关联的业务主键")
    order_state = fields.CharField(max_length=50, null=True, description="订单状态,成功/失败")
    
    # 交易状态
    transaction_state = fields.CharEnumField(TransactionStatus, default=TransactionStatus.PRE_CHARGING, description="交易状态")

    # 时间信息
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "saas_wallet_transaction"
        table_description = "SaaS交易订单表"
        
    def __str__(self):
        return f"{self.platform_name}-{self.agent_id}-{self.pre_charge_id}"
