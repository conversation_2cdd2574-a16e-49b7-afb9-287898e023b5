import uuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime, timezone


class SystemConfig(Model):
    """系统配置表，用于存储系统级别的配置信息"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    key = fields.CharField(max_length=255, unique=True, description="配置键")
    category = fields.CharField(max_length=100, null=True, description="配置分类")
    description = fields.TextField(null=True, description="配置描述")
    value = fields.TextField(null=True, description="配置值")
    default_value = fields.TextField(null=True, description="默认值")
    order_no = fields.IntField(default=0, description="排序号")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="修改时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    
    class Meta:
        table = "system_configs"
        description = "系统配置表"
    
    def __str__(self):
        return f"{self.key}: {self.value}" 