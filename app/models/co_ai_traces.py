import uuid
from tortoise import fields
from tortoise.models import Model


class CoAiTraces(Model):
    """AI去痕记录模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    
    # 关联用户
    user = fields.ForeignKeyField(
        "models.User",
        related_name="co_ai_traces",
        description="创建用户"
    )
    
    # 关联上传的文件
    upload_file = fields.ForeignKeyField(
        "models.UploadFile",
        related_name="ai_traces",
        description="关联的上传文件"
    )
    
    # 关联使用的模型配置
    model_config = fields.ForeignKeyField(
        "models.ModelConfig", 
        related_name="ai_trace_configs", 
        description="使用的模型配置",
        null=True
    )
    
    # 原始文件信息
    original_filename = fields.CharField(max_length=255, description="原始文件名")
    file_size = fields.IntField(description="文件大小(字节)")
    file_type = fields.CharField(max_length=50, description="文件类型")
    char_count = fields.IntField(description="字符数量")
    
    # 处理结果
    processed_sections = fields.IntField(description="处理的段落数量")
    original_content = fields.TextField(description="原始内容")
    processed_content = fields.TextField(null=True, description="处理后的内容")
    processed_path = fields.TextField(null=True, description="处理后内容URL，md文件存储路径")
    detail_report = fields.TextField(null=True, description="详细的段落数据的文件地址")
    # 处理状态
    status = fields.CharField(
        max_length=20, 
        default="processing", 
        description="处理状态 (processing: 处理中, completed: 完成, failed: 失败)"
    )
    
    # 错误信息
    error_message = fields.TextField(null=True, description="错误信息")
    
    # 处理时间统计
    processing_start_time = fields.DatetimeField(null=True, description="处理开始时间")
    processing_end_time = fields.DatetimeField(null=True, description="处理结束时间")
    
    # 消耗的tokens
    tokens_consumed = fields.IntField(null=True, default=0, description="消耗的tokens数量")
    
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "co_ai_traces"
        description = "AI去痕记录"
    
    def __str__(self):
        return f"{self.original_filename} - {self.status}" 