import uuid
from tortoise import fields
from tortoise.models import Model


class CoAiPpt(Model):
    """PPT生成记录模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    
    # 关联用户
    user = fields.ForeignKeyField(
        "models.User",
        related_name="co_ai_ppts",
        description="创建用户"
    )
    
    # 关联上传的源文件
    upload_file = fields.ForeignKeyField(
        "models.UploadFile",
        related_name="ppt_generations",
        description="关联的上传文件"
    )
    
    # 关联使用的模型配置
    model_config = fields.ForeignKeyField(
        "models.ModelConfig", 
        related_name="ppt_generations", 
        description="使用的模型配置",
        null=True
    )
    
    # 原始文档信息
    original_filename = fields.CharField(max_length=255, description="原始文档文件名")
    original_file_path = fields.CharField(max_length=500, description="原始文档文件路径")
    file_type = fields.CharField(max_length=50, description="原始文件类型")
    
    # 生成的PPT信息
    ppt_file_path = fields.CharField(max_length=500, description="生成的PPT文件路径")
    ppt_filename = fields.CharField(max_length=255, description="PPT文件名")
    ppt_file_size = fields.IntField(null=True, description="PPT文件大小(字节)")
    
    # 生成配置
    model_type = fields.CharField(max_length=50, description="使用的PPT模板类型")
    
    # 处理状态和结果
    status = fields.CharField(
        max_length=20, 
        default="processing", 
        description="生成状态 (processing: 生成中, completed: 完成, failed: 失败)"
    )
    
    # 生成耗时
    generation_duration = fields.IntField(null=True, description="生成耗时（秒）")
    
    # 错误信息
    error_message = fields.TextField(null=True, description="错误信息")
    
    # 标准字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "co_ai_ppts"
        description = "PPT生成记录"
    
    def __str__(self):
        return f"{self.original_filename} -> {self.ppt_filename} ({self.status})" 