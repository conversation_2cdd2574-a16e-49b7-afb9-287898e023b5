from pydantic import BaseModel, Field
from datetime import datetime
from typing import List, Optional


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(True, description="请求是否成功")
    message: str = Field("", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")

class HomeworkResponse(BaseModel):
    """作业响应模型"""
    id: str = Field(..., description="会话id")
    name: str = Field(..., description="会话名称")
    created_at: datetime = Field(..., description="创建时间")


class HomeworkListResponse(BaseResponse):
    """作业记录列表响应模型"""
    has_more: bool = Field(..., description="是否还有更多")
    page_size: int = Field(..., description="每页大小")
    items: List[HomeworkResponse] = Field(..., description="作业记录列表")

class DifyFile(BaseModel):
    id: str
    filename: str
    type: str
    mime_type: str
    size: int
    upload_file_id: str
    url: str

class HomeworkDetailResponse(BaseModel):
    """作业详情响应模型"""
    file:  Optional[DifyFile] = None
    answer: str = Field("", description="作业的完整内容字符串")
    conversation_id: str = Field("", description="会话ID")

