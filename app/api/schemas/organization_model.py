from pydantic import BaseModel, UUID4, Field
from typing import Optional, List
from datetime import datetime
from app.api.schemas.model_config import ModelConfigBase
from app.utils.enum import UseCase


class OrganizationModelCreate(BaseModel):
  organization_id: UUID4 = Field(..., description="机构的ID")
  list_model: List[UUID4] = Field(..., description="模型的ID列表")

class OrganizationDefaultUseResponse(BaseModel):
   default_way: Optional[UseCase] = Field(default=None, description="使用场景")
   is_thinking: Optional[bool] = False
   model: ModelConfigBase
   class Config:
      from_attributes=True
class UseItem(BaseModel):
   model_id: UUID4
   is_thinking: Optional[bool] = False
   default_way: UseCase
class OrganizationDefaultUse(BaseModel):
   list: List[UseItem]
   organization_id: Optional[UUID4] = Field(default=None, description="机构的ID（超级管理员需要传）")
class OrganizationModelWithUse(BaseModel):
    id: UUID4
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime]
    is_deleted: Optional[bool] = Field(default=False, description="是否删除")
    model: Optional[ModelConfigBase]
    organization_id: UUID4
    is_thinking: Optional[bool] = False
    default_way: Optional[List[str]] = Field(default=None, description="默认用途")

    class Config:
      from_attributes = True 