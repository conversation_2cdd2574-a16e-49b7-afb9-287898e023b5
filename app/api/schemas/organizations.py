from pydantic import BaseModel, Field, UUID4, validator
from typing import Optional
from datetime import datetime
import re

# 基础模型
class OrganizationBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="机构名称")
    type: str = Field(..., min_length=1, max_length=50, description="机构类型")
    contact_person: str = Field(..., min_length=1, max_length=50, description="联系人")
    contact_phone: str = Field(..., description="联系电话(加密传输)")
    description: Optional[str] = Field(None, description="机构描述")
    remarks: Optional[str] = Field(None, description="备注")
    is_trial: Optional[bool] = Field(False, description="是否为体验机构")

# 创建请求
class OrganizationCreate(OrganizationBase):
    code: str = Field(..., min_length=1, max_length=50, description="机构代码(唯一)")
    is_active: bool = Field(True, description="是否启用")
    contact_email: str = Field(..., description="电子邮箱")
    limit_count: int = Field(..., description="机构的总可用次数")
    contact_phone: Optional[str] = Field(None, description="联系电话(加密传输)")
    @validator('contact_email')
    def validate_email(cls, v):
        email_pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
        if v and not re.match(email_pattern, v):
            raise ValueError('无效的电子邮箱格式')
        return v

# 更新请求
class OrganizationUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="机构名称")
    type: Optional[str] = Field(None, min_length=1, max_length=50, description="机构类型")
    contact_person: Optional[str] = Field(None, min_length=1, max_length=50, description="联系人")
    contact_phone: Optional[str] = Field(None, description="联系电话(加密传输)")
    contact_email: Optional[str] = Field(None, description="电子邮箱")
    is_active: Optional[bool] = Field(None, description="是否启用")
    description: Optional[str] = Field(None, description="机构描述")
    remarks: Optional[str] = Field(None, description="备注")
    limit_count: int = Field(..., description="机构的总可用次数")
    is_trial: Optional[bool] = Field(None, description="是否体验机构")
    @validator('contact_email')
    def validate_email(cls, v):
        email_pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
        if v and not re.match(email_pattern, v):
            raise ValueError('无效的电子邮箱格式')
        return v

# 响应模型
class OrganizationResponse(OrganizationBase):
    id: UUID4
    code: str = Field(..., description="机构代码")
    is_active: bool = Field(..., description="是否启用")
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_deleted: bool = False
    contact_email: str = Field(..., description="电子邮箱")
    use_count: Optional[int] = Field(default=None, description="机构的使用次数")
    limit_count: Optional[int] = Field(default=None, description="机构的总可用次数")

    class Config:
        from_attributes = True
class OrganizationWithUserResponse(OrganizationBase):
    id: UUID4
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="角色")
    
    class Config:
        from_attributes = True

# UseCase响应模型
class UseCaseResponse(BaseModel):
    value: str = Field(..., description="UseCase的值")
    label: str = Field(..., description="UseCase的名称")
    description: str = Field(..., description="UseCase的描述")
    is_thinking: Optional[bool] = False
    model_id: Optional[UUID4] = Field(default=None, description="模型的ID") 
    class Config:
        from_attributes = True
