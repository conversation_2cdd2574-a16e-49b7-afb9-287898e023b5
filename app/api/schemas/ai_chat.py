from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from app.services.dify_service import FileItem

class ChatMessageRequest(BaseModel):
    """AI对话消息请求"""
    query: str  # 用户输入的问题
    inputs: Optional[Dict[str, Any]] = None  # 应用变量值
    conversation_id: Optional[str] = None  # 会话ID，用于继续对话
    files: Optional[List[FileItem]] = None  # 文件列表
    response_mode: str = "streaming"  # 响应模式，默认流式
    auto_generate_name: bool = True  # 自动生成会话标题

class ChatMessageResponse(BaseModel):
    """AI对话消息响应"""
    message_id: str
    conversation_id: str
    answer: str
    created_at: int

class ConversationListRequest(BaseModel):
    """获取会话列表请求"""
    last_id: Optional[str] = None  # 上一页最后一条记录的 ID（用于分页）
    limit: int = 20  # 返回数量限制
    pinned: Optional[bool] = None  # 是否只返回置顶会话

class ConversationItem(BaseModel):
    """会话条目"""
    id: str
    name: str
    inputs: Dict[str, Any]
    status: str
    introduction: str
    created_at: int
    updated_at: int

class ConversationListResponse(BaseModel):
    """会话列表响应"""
    data: List[Dict[str, Any]]
    has_more: bool
    limit: int

class FileUploadRequest(BaseModel):
    """文件上传请求"""
    file_path: str  # 本地文件路径
    file_type: Optional[str] = None  # 文件类型（可选）
    file_name: Optional[str] = None  # 文件名（可选）

class FileUploadResponse(BaseModel):
    """文件上传响应"""
    id: str
    name: str
    size: int
    extension: str
    mime_type: str
    created_by: str
    created_at: int

class StopChatMessageRequest(BaseModel):
    """停止对话消息请求"""
    user: str  # 用户标识，必须和发送消息接口传入user保持一致

class StopChatMessageResponse(BaseModel):
    """停止对话消息响应"""
    result: str  # 停止操作结果，成功时为"success"

class AIHomeworkRequest(BaseModel):
    """AI作业解答请求"""
    query: Optional[str] = ""  # 用户输入的问题，可选
    conversation_id: Optional[str] = None  # 会话ID，用于继续对话
    files: Optional[List[FileItem]] = None  # 文件列表
    
    def to_chat_message_request(self) -> ChatMessageRequest:
        """转换为ChatMessageRequest对象"""
        # 确保文件类型正确设置为document
        files = self.files
        if files:
            for file in files:
                if file.type == "custom" and file.transfer_method == "local_file":
                    file.type = "document"
                    
        return ChatMessageRequest(
            query=self.query or "",
            inputs={"additionalProp1": {}},
            conversation_id=self.conversation_id,
            files=files,
            response_mode="streaming",
            auto_generate_name=False
        ) 

class MessageItem(BaseModel):
    """消息条目"""
    id: str
    conversation_id: str
    inputs: Dict[str, Any]
    query: str
    answer: str
    feedback: Optional[str] = None
    retriever_resources: Optional[List[Dict[str, Any]]] = None
    created_at: int
    agent_thoughts: Optional[List[Dict[str, Any]]] = None
    message_files: Optional[List[Dict[str, Any]]] = None
    parent_message_id: Optional[str] = None

class MessagesListRequest(BaseModel):
    """获取会话历史消息请求"""
    conversation_id: str  # 会话ID
    first_id: Optional[str] = None  # 第一条消息ID（用于分页加载）
    limit: int = 20  # 返回消息数量限制

class MessagesListResponse(BaseModel):
    """消息列表响应"""
    data: List[Dict[str, Any]]
    has_more: bool
    limit: int
    first_id: Optional[str] = None 

class SessionInfoResponse(BaseModel):
    """会话信息响应"""
    current_round: int  # 当前对话轮次
    message_count: int  # 当前轮次内的消息数量
    total_messages: int  # 总消息数量
    remaining_messages: int  # 剩余消息数量
    is_round_complete: bool  # 当前轮次是否完成
    conversation_id: Optional[str] = None  # 会话ID
    session_start_time: Optional[str] = None  # 会话开始时间
    last_activity: Optional[str] = None  # 最后活动时间 

class DeleteConversationRequest(BaseModel):
    """删除会话请求"""
    # conversation_id 现在是路径参数，不需要在请求体中
    pass

class DeleteConversationResponse(BaseModel):
    """删除会话响应"""
    result: str  # 删除操作结果，成功时为"success"
    conversation_id: str  # 被删除的会话ID

class RenameConversationRequest(BaseModel):
    """重命名会话请求"""
    name: str = ""  # 新的会话名称，如果为空且auto_generate为True，则自动生成
    auto_generate: bool = True  # 是否自动生成会话名称

class RenameConversationResponse(BaseModel):
    """重命名会话响应"""
    result: str  # 重命名操作结果，成功时为"success"
    conversation_id: str  # 会话ID
    name: str  # 新的会话名称 