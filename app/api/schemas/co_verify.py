from pydantic import BaseModel, Field
from uuid import UUID
from typing import Optional
from datetime import datetime
from app.api.schemas.upload_file import UploadFileResponse
from app.utils.enum import CoVerifyStatus
from app.api.schemas.user import UserResponse


class CoVerifyCreate(BaseModel):
    """
    创建co验证请求体
    """
    file_id: Optional[UUID] = Field(None, description="关联的文件ID")
    status: Optional[CoVerifyStatus] = Field(None, description="验证状态")


class CoVerifyUpdate(BaseModel):
    """
    更新co验证请求体
    """
    file_id: Optional[UUID] = Field(None, description="关联的文件ID")
    status: Optional[CoVerifyStatus] = Field(None, description="验证状态")
    ai_report: Optional[str] = Field(None, description="AI报告内容")


class CoVerifyResponse(BaseModel):
    """
    co验证响应体
    """
    id: UUID = Field(..., description="验证ID")
    ai_report: Optional[str] = Field(None, description="AI报告内容")
    status: str = Field(..., description="验证状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    is_deleted: bool = Field(..., description="是否删除")
    deleted_at: Optional[datetime] = Field(None, description="删除时间")
    file: Optional[UploadFileResponse] = Field(None, description="关联的文件")
