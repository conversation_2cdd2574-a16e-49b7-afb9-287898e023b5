from typing import Optional, List
from pydantic import BaseModel, UUID4, Field
from datetime import datetime


class SystemConfigBase(BaseModel):
    key: str = Field(..., min_length=1, description="配置键，不能为空")
    category: Optional[str] = None
    description: Optional[str] = None
    value: Optional[str] = None
    default_value: Optional[str] = None
    order_no: Optional[int] = 0


class SystemConfigCreate(SystemConfigBase):
    pass


class SystemConfigUpdate(BaseModel):
    category: Optional[str] = None
    description: Optional[str] = None
    value: Optional[str] = None
    default_value: Optional[str] = None
    order_no: Optional[int] = None


class SystemConfigResponse(SystemConfigBase):
    id: UUID4
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class SystemConfigSyncResponse(BaseModel):
    changed: bool
    added: int
    updated: int
    deleted: int 