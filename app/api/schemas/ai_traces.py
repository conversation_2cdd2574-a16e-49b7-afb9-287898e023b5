from pydantic import BaseModel, Field
from uuid import UUID
from typing import Optional, List
from datetime import datetime
from app.api.schemas.upload_file import UploadFileResponse


class CoAiTracesCreate(BaseModel):
    """
    创建co验证请求体
    """
    file_id: Optional[UUID] = Field(None, description="关联的文件ID")


class ProjectConfigAITraces(BaseModel):
  original: str = Field(..., description="原始内容")
  modified: str = Field(..., description="修改后的内容")
  series_num: int = Field(..., description="文本原始的位置")

class CoAiTracesResponse(BaseModel):
    """
    AI去痕响应体
    """
    detail_list: Optional[List[ProjectConfigAITraces]] = None
    id: UUID = Field(..., description="记录ID")
    ai_report: Optional[str] = Field(None, description="AI报告内容")
    
    # 原始文件信息
    original_filename: Optional[str] = Field(None, description="原始文件名")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    file_type: Optional[str] = Field(None, description="文件类型")
    char_count: Optional[int] = Field(None, description="字符数量")
    
    # 处理结果
    processed_sections: Optional[int] = Field(None, description="处理的段落数量")
    original_content: Optional[str] = Field(None, description="原始内容")
    processed_content: Optional[str] = Field(None, description="处理后的内容")
    processed_path: Optional[str] = Field(None, description="处理后内容URL，md文件存储路径")
    detail_report: Optional[str] = Field(None, description="详细的段落数据的文件地址")
    
    # 处理状态和时间
    status: str = Field(..., description="处理状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    processing_start_time: Optional[datetime] = Field(None, description="处理开始时间")
    processing_end_time: Optional[datetime] = Field(None, description="处理结束时间")
    tokens_consumed: Optional[int] = Field(None, description="消耗的tokens数量")
    
    # 时间戳
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    is_deleted: Optional[bool] = Field(None, description="是否已删除")
    deleted_at: Optional[datetime] = Field(None, description="删除时间")
    
    # 关联对象
    upload_file: Optional[UploadFileResponse] = Field(None, description="关联的文件")
    
    class Config:
        from_attributes = True

