from pydantic import BaseModel, <PERSON>
from datetime import datetime
from typing import Optional
from uuid import UUID
from enum import Enum
from app.api.schemas.organizations import OrganizationResponse

class InsetRole(str, Enum):
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"
    USER = "user"

PERMISSION_LEVELS = {
  "super_admin": 3,
  "admin": 2,
  # 其他角色都是0
}
class RoleBase(BaseModel):
    name: str = Field(..., description="角色名称")
    identifier: str = Field(..., description="角色标识符")


class RoleCreate(RoleBase):
    organization: Optional[UUID] = None

class RoleUpdate(BaseModel):
    name: Optional[str] = Field(None, description="角色名称")
    identifier: Optional[str] = Field(None, description="角色标识符")
    organization: Optional[UUID] = None
    class Config:
        extra = 'forbid'

class RoleResponse(RoleBase):
    id: UUID
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True
class RoleWithOrganizationResponse(RoleBase):
    id: UUID
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    organization: Optional[OrganizationResponse] = Field(default=None, description="机构信息")

    class Config:
        from_attributes = True
