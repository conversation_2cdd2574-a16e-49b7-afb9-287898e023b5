from pydantic import BaseModel, Field

class InstanceInfoItem(BaseModel):
    scene: str = Field(..., description="场景值，ManagerScene枚举值")
    business_id: str = Field(..., description="业务系统的ID")
    instance_id: str = Field(..., description="应用实例的唯一ID标识符，在.env文件里面配置")
    created_at: str = Field(..., description="异步任务的开始时间")
class StopTaskData(BaseModel):
    scene: str = Field(..., description="场景值，ManagerScene枚举值")
    business_id: str = Field(..., description="业务系统的ID")
    instance_id: str = Field(..., description="应用实例的唯一ID标识符，在.env文件里面配置")
