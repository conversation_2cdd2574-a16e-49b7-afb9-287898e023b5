from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from enum import Enum
from datetime import datetime


class PPTTemplateType(str, Enum):
    """PPT模板类型枚举"""
    GRADUATION = "graduation"  # 毕业答辩模板
    CREATIVE = "creative"  # 创意模板
    SIMPLE = "simple"      # 简约模板


class PPTGenerationStatus(str, Enum):
    """PPT生成状态枚举"""
    PENDING = "pending"      # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(True, description="请求是否成功")
    message: str = Field("", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")


class PPTGenerateRequest(BaseModel):
    """PPT生成请求模型（基于文档上传）"""
    ppt_type: PPTTemplateType = Field(..., description="PPT模板类型，可选值：graduation(毕业答辩模板)、creative(创意模板)、simple(简约模板)")
    custom_title: Optional[str] = Field(None, max_length=100, description="自定义PPT标题")
    custom_author: Optional[str] = Field(None, max_length=50, description="自定义作者")
    additional_instructions: Optional[str] = Field(None, max_length=500, description="额外的生成指令")
    
    @validator('custom_title')
    def validate_title(cls, v):
        if v and len(v.strip()) == 0:
            raise ValueError('标题不能为空字符串')
        return v.strip() if v else v
    
    @validator('custom_author') 
    def validate_author(cls, v):
        if v and len(v.strip()) == 0:
            raise ValueError('作者不能为空字符串')
        return v.strip() if v else v


class PPTGenerateResponse(BaseResponse):
    """PPT生成响应模型"""
    id: str = Field(..., description="记录ID")
    file_path: str = Field(..., description="生成的PPT文件路径")
    filename: str = Field(..., description="文件名")
    size: Optional[int] = Field(None, description="文件大小（字节）")
    download_url: Optional[str] = Field(None, description="下载链接")
    preview_url: Optional[str] = Field(None, description="预览链接")
    document_content: Optional[str] = Field(None, description="解析的文档内容（前500字符预览）")
    generation_time: Optional[int] = Field(None, description="生成耗时（秒）")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PPTHistoryResponse(BaseModel):
    """PPT生成历史记录响应模型"""
    id: str = Field(..., description="记录ID")
    original_filename: str = Field(..., description="原始文档文件名")
    original_file_path: str = Field(..., description="原始文档文件路径")
    ppt_filename: str = Field(..., description="生成的PPT文件名")
    ppt_file_path: str = Field(..., description="生成的PPT文件路径")
    file_type: str = Field(..., description="原始文件类型")
    ppt_type: str = Field(..., description="使用的PPT模板类型")
    status: PPTGenerationStatus = Field(..., description="生成状态")
    file_size: Optional[int] = Field(None, description="PPT文件大小")
    generation_duration: Optional[int] = Field(None, description="生成耗时（秒）")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PPTListResponse(BaseModel):
    """PPT历史列表响应模型"""
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")
    items: List[PPTHistoryResponse] = Field(..., description="PPT历史记录列表")


class PPTDetailResponse(BaseModel):
    """PPT详细信息响应模型"""
    id: str = Field(..., description="PPT记录的唯一标识符")
    original_filename: str = Field(..., description="原始上传文档的文件名")
    original_file_path: str = Field(..., description="原始文档存储路径")
    ppt_filename: str = Field(..., description="生成的PPT文件名")
    ppt_file_path: str = Field(..., description="PPT文件存储路径")
    file_type: str = Field(..., description="原始文件类型")
    ppt_type: str = Field(..., description="使用的PPT模板类型")
    status: PPTGenerationStatus = Field(..., description="生成状态")
    file_size: Optional[int] = Field(None, description="PPT文件大小（字节）")
    generation_duration: Optional[int] = Field(None, description="生成耗时（秒）")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    download_url: Optional[str] = Field(None, description="PPT文件的下载地址")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PPTTemplateInfo(BaseModel):
    """PPT模板信息模型"""
    template_type: PPTTemplateType = Field(..., description="模板类型，可选值：graduation(毕业答辩模板)、creative(创意模板)、simple(简约模板)")
    display_name: str = Field(..., description="显示名称")
    description: str = Field(..., description="模板描述")
    content_slide_count: int = Field(..., description="支持的内容页数量")
    features: List[str] = Field(default_factory=list, description="模板特性")
    preview_image: Optional[str] = Field(None, description="预览图片URL")


class PPTTemplateListResponse(BaseResponse):
    """PPT模板列表响应模型"""
    templates: List[PPTTemplateInfo] = Field(..., description="可用模板列表")


# 错误响应模型
class PPTErrorResponse(BaseModel):
    """PPT错误响应模型"""
    success: bool = Field(False, description="请求是否成功")
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间戳")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        } 