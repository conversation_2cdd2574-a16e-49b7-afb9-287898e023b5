from pydantic import BaseModel, Field, validator
from typing import Optional
import re


class SMSCodeRequest(BaseModel):
    """发送短信验证码请求"""
    mobile: str = Field(..., description="手机号码(加密传输)")
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if not v:
            raise ValueError("手机号码不能为空")
        
        # 不再验证手机号格式，因为现在是加密传输
        # 加密后的字符串不符合手机号格式
        return v


class SMSCodeResponse(BaseModel):
    """发送短信验证码响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    request_id: Optional[str] = Field(None, description="请求ID")


class MobileLoginRequest(BaseModel):
    """手机号验证码登录请求"""
    mobile: str = Field(..., description="手机号码(加密传输)")
    code: str = Field(..., description="验证码")
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if not v:
            raise ValueError("手机号码不能为空")
        
        # 不再验证手机号格式，因为现在是加密传输
        # 加密后的字符串不符合手机号格式
        return v
    
    @validator('code')
    def validate_code(cls, v):
        if not v:
            raise ValueError("验证码不能为空")
        
        # 验证码应该是6位数字
        if not re.match(r'^\d{6}$', v):
            raise ValueError("验证码格式错误")
        
        return v

