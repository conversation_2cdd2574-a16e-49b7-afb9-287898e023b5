from fastapi import APIRouter, Depends
from app.api.routes import (
  auth,
  co_ai_homework,
  users,
  model_configs,
  user_report_usages,
  dictionary,
  area,
  organizations,
  menu,
  role,
  organization_role_menu,
  organization_menu,
  organization_model,
  user_default_model,
  project_configs,
  project_report,
  project_downloads,
  sms,
  co_ai_chat,
  co_ai_traces,
  upload_file,
  co_verify,
  ppt,
  onlyoffice,
  statics,
  system_config,
  app_instance, health
)
from app.api.deps import (
  get_current_user,
  auth_super_admin_depend,
  auth_not_trial_depend
)


api_router = APIRouter(prefix="/api")
auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user)])
super_admin_auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user), Depends(auth_super_admin_depend)])
# 非体验用户才有的权限路由
no_trial_auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user), Depends(auth_not_trial_depend)])

# 添加无需认证的路由（登录相关接口）
api_router.include_router(statics.oss_router, prefix="/statics", tags=["静态资源服务"])
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(sms.router, prefix="/sms", tags=["短信服务"])
api_router.include_router(health.router, prefix="/health", tags=["健康检查"])
auth_router.include_router(role.router, prefix="/role", tags=["角色管理"])
auth_router.include_router(menu.router, prefix="/menu", tags=["菜单管理"])
auth_router.include_router(organization_menu.router, prefix="/organization-menu", tags=["机构菜单管理"])
auth_router.include_router(organization_role_menu.router, prefix="/role-menu", tags=["角色菜单管理"]) 
auth_router.include_router(users.router, prefix="/users", tags=["用户管理"])
auth_router.include_router(user_report_usages.router, tags=["用户报告使用次数"])
super_admin_auth_router.include_router(model_configs.router, prefix="/model-configs", tags=["模型配置管理"]) 
auth_router.include_router(dictionary.router, prefix="/dictionary", tags=["字典"])
auth_router.include_router(area.router, prefix="/area", tags=["省市区"])
super_admin_auth_router.include_router(organizations.router, prefix="/organizations", tags=["机构管理"])
auth_router.include_router(organization_model.router, prefix="/organization-model", tags=["机构的模型信息"]) 
auth_router.include_router(user_default_model.router, prefix="/user-model", tags=["用户的模型"]) 
auth_router.include_router(project_configs.router, prefix="/project-configs", tags=["项目配置"])
auth_router.include_router(project_report.router, prefix="/project-reports", tags=["项目大纲报告生成"])
no_trial_auth_router.include_router(project_downloads.router, prefix="/project-downloads", tags=["项目文件下载"])
auth_router.include_router(co_ai_chat.router, prefix="/ai-chat", tags=["AI对话聊天"])
auth_router.include_router(co_ai_homework.router, prefix="/ai-homework", tags=["AI作业助手"])
auth_router.include_router(co_ai_traces.router, prefix="/ai-traces", tags=["AI去痕处理"])
auth_router.include_router(upload_file.router, prefix="/upload-file", tags=["上传文件"])
auth_router.include_router(co_verify.router, prefix="/verify", tags=["幻觉审查"])
auth_router.include_router(ppt.router, prefix="/ppt", tags=["PPT生成"])
# 系统配置路由 ：超级管理员可以访问所有接口
super_admin_auth_router.include_router(system_config.router, prefix="/system-config", tags=["系统配置管理"])
# 系统配置公共接口：普通用户可访问的只读接口 
api_router.include_router(system_config.public_router, prefix="/system-config", tags=["系统配置公共接口"])
# OnlyOffice路由分离：需要认证的接口注册到auth_router，不需要认证的回调接口注册到api_router
auth_router.include_router(onlyoffice.router, prefix="/onlyoffice", tags=["OnlyOffice"])
super_admin_auth_router.include_router(app_instance.router, prefix="/app-instance", tags=["应用实例信息"])
api_router.include_router(onlyoffice.callback_router, prefix="/onlyoffice", tags=["OnlyOffice回调"])
api_router.include_router(auth_router)
api_router.include_router(super_admin_auth_router)
api_router.include_router(no_trial_auth_router)