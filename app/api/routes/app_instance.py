from fastapi import APIRouter, Query
from typing import List, Optional
from app.models.area import Area
from app.api.schemas.area import AreaResponse
from app.utils.utils import send_data, ResponseModel
from app.core.redis_client import get_llm_interaction_instance
from app.core.config import settings
from app.core.logging import get_logger
from app.api.schemas.app_instance import InstanceInfoItem, StopTaskData
from app.utils.enum import ManagerScene

llm_interaction_instance = get_llm_interaction_instance()
logger = get_logger(__name__)

router = APIRouter()

@router.get("/info", response_model=ResponseModel[List[InstanceInfoItem]])
async def get_instance_info(
  app_instance_id: Optional[str] = Query(default=None, description="应用实例的唯一标识符号")
):
  result = await llm_interaction_instance.get_instance_interactions(
    app_instance_id=app_instance_id
  )
  return send_data(True, [InstanceInfoItem.model_validate(item, from_attributes=True) for item in result])
@router.delete("/stop-task", response_model=ResponseModel[bool])
async def stop_task(
  data: StopTaskData
):
  try:
    result = await llm_interaction_instance.end_interaction(
      app_instance_id=data.instance_id,
      scene=ManagerScene(data.scene),
      id=data.business_id
    )
    if not result:
      raise Exception("关闭异步任务失败")
    return send_data(True, True)
  except Exception as e:
    logger.error(f"关闭异步任务失败：{str(e)}")
    return send_data(False, None, "关闭异步任务失败")