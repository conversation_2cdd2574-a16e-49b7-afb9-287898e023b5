from fastapi import (
  APIRouter,
  HTTPException,
  Query,
  Request,
  UploadFile,
  File,
  Depends,
  status
)
import os
from typing import Optional
from uuid import UUID
from app.api.schemas.ai_traces import (
    CoAiTracesCreate,
    CoAiTracesResponse
)
from app.utils.utils import (
    PageQuery,
    ResponsePageModel,
    send_page_data,
    send_data,
    ResponseModel,
    validate_file_security,
    validate_text_length,
    ALLOW_EXTENSION,
    read_file_content
)
from app.api.repository import co_ai_traces as co_ai_traces_repo, upload_file
from app.api.deps import get_current_user_from_state
from app.core.logging import get_logger
from app.utils.utils import convert_markdown_to_docx, convert_markdown_to_pdf
from app.utils.enum import CoVerifyStatus, ProjectConfigError
from app.api.repository.upload_file import save_upload_file, delete_upload_file, get_file_content_by_id
from app.api.schemas.upload_file import UploadFileResponse
from app.utils.enum import CoAiTracesStatus
from app.services.text_moderation_service import text_moderation_service
from app.services.content_moderation_service import content_moderation_service

logger = get_logger(__name__)
router = APIRouter()


@router.post("", response_model=ResponseModel[CoAiTracesResponse])
async def create_co_ai_traces(data: CoAiTracesCreate, request: Request):
    """
    创建AI去痕记录
    """
    try:
        current_user = get_current_user_from_state(request)

        # 添加内容审查 - 检查文件内容是否安全
        try:
            # 获取文件记录
            file_record = await get_file_content_by_id(data.file_id)
            if not file_record:
                return send_data(False, None, "文件不存在")
            
            # 使用统一的内容审核服务
            is_safe, error_msg = await content_moderation_service.verify_file_content(
                file_path=file_record.file_path,
                description=f"AI去痕记录文件 {data.file_id}"
            )
            
            if not is_safe:
                return send_data(False, None, error_msg)
                
            logger.info(f"文件 {data.file_id} 的文档内容审核通过，可以继续创建AI去痕记录")
                
        except Exception as content_error:
            error_msg = f"文档内容审核失败: {str(content_error)}"
            logger.error(error_msg)
            return send_data(False, None, error_msg)

        result = await co_ai_traces_repo.create_ai_traces(current_user=current_user, file_id=data.file_id)
        return send_data(True, result)
    except Exception as e:
        error_msg = f"创建AI去痕记录失败: {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)


@router.get("/{verify_id}", response_model=ResponseModel[CoAiTracesResponse])
async def get_co_verify(verify_id: UUID, request: Request):
    """
    根据ID获取AI去痕记录
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await co_ai_traces_repo.get_one_traces_record(verify_id, current_user)
        if result.status == CoAiTracesStatus.SUCCESS.value and result.processed_path:
            try:
                upload_file.download_file(result.processed_path)
                result.processed_content = read_file_content(result.processed_path)
            finally:
                upload_file.remove_local_file(result.processed_path)
        return send_data(True, result)
    except Exception as e:
        error_msg = str(e)
        logger.error(error_msg)
        return send_data(False, None, error_msg)


@router.get("/list/paginated", response_model=ResponsePageModel[CoAiTracesResponse])
async def get_co_ai_traces_list(
    request: Request,
    page_query: PageQuery = Depends(),
    keyword: Optional[str] = Query(default=None, description="搜索关键词"),
):
    """
    分页获取AI去痕列表（只显示近配置天数的记录）
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await co_ai_traces_repo.get_ai_traces_list(page_query, current_user, keyword)
        return send_page_data(True, result)
    except Exception as e:
        error_msg = f"获取AI去痕列表失败: {str(e)}"
        logger.error(error_msg)
        return send_page_data(False, None, error_msg)


@router.delete("/{verify_id}", response_model=ResponseModel[bool])
async def delete_co_verify(verify_id: UUID, request: Request):
    """
    删除AI去痕记录
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await co_ai_traces_repo.delete_ai_traces(verify_id, current_user)
        return send_data(True, result)
    except Exception as e:
        error_msg = f"删除AI去痕记录失败: {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)


@router.get("/{id}/download", summary="下载AI去痕报告")
async def download_report(
    id: str,
    request: Request,
    flag: str = Query(..., description="pdf/doc")
):
    current_user = get_current_user_from_state(request)
    try:
        result = await co_ai_traces_repo.get_one_traces_record(id, current_user)
        
        # 添加详细的日志
        logger.info(f"下载AI去痕报告: 记录ID={id}, 状态={result.status}, processed_path={result.processed_path}")
        
        # 检查状态和文件路径
        if result.status != CoAiTracesStatus.SUCCESS.value:
            logger.error(f"AI去痕记录状态不正确: {result.status}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"AI去痕记录状态不正确: {result.status}"
            )
        
        if not result.processed_path:
            logger.error(f"processed_path为空: {result.processed_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="处理后的内容文件不存在"
            )
        
        # 检查文件是否存在
        upload_file.download_file(result.processed_path)
        
        file_path = result.processed_path
        if not os.path.isabs(file_path):
            file_path = os.path.join(os.getcwd(), file_path)
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文件不存在: {file_path}"
            )
        
        logger.info(f"开始转换文件: {file_path}, 格式: {flag}")
        try:
            if flag == 'pdf':
                return convert_markdown_to_pdf(result.processed_path)
            else:
                return convert_markdown_to_docx(result.processed_path)
        finally:
            upload_file.remove_local_file(result.processed_path)
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"下载文章失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载文章失败: {str(e)}"
        )


@router.post("/upload-paper", response_model=ResponseModel[UploadFileResponse], summary="上传文件（仅支持word、md、txt）")
async def upload_user_file(
    request: Request,
    file: UploadFile = File(..., description="用户上传的文件")
):
    """
    上传文件接口，只允许上传word、md、txt文件。
    - 文件名用时间戳覆盖。
    - 文件存储到本地并写入upload_files表。
    - 返回文件信息。
    """
    # 校验文件类型
    current_user = get_current_user_from_state(request)
    ext = os.path.splitext(file.filename)[1].lower()
    if ext not in ALLOW_EXTENSION:
        return send_data(False, None, ProjectConfigError.UPLOAD_FILE_ERROR.value)
    # 保存文件
    try:
        await validate_file_security(file)
        await validate_text_length(file)
        result = await save_upload_file(file, current_user)
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, str(e))


@router.post("/project/{project_id}", response_model=ResponseModel[CoAiTracesResponse], summary="为论文创建AI去痕")
async def create_co_ai_traces_from_project(
    project_id: str,
    request: Request,
):
    """
    根据项目ID创建AI去痕记录并开始处理
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await co_ai_traces_repo.create_ai_traces_from_project(
            project_id=project_id,
            current_user=current_user
        )
        return send_data(True, result)
    except Exception as e:
        error_msg = str(e)
        logger.error(error_msg)
        return send_data(False, None, error_msg)
