from asyncio.log import logger

from app.api.repository import upload_file
from app.core.logging import get_logger
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import FileResponse
from starlette.background import BackgroundTask
from datetime import datetime
import os
import tempfile
from docx import Document
import docx
from docx.shared import RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.models.project_configs import ProjectConfig
from app.api.schemas.project_configs import ProjectConfigStatus
from app.api.deps import get_current_user

router = APIRouter()
logger = get_logger(__name__)


# 清理临时文件
def cleanup_files(file_paths):
    """
    清理临时文件
    
    Args:
        file_paths: 文件路径列表
    """
    for path in file_paths:
        try:
            if path and os.path.exists(path):
                os.unlink(path)
        except:
            pass
def ensure_heading_styles(doc):
    logger.info(f"doc.styles: {doc.styles}")
    if 'Heading 2' not in [s.name for s in doc.styles]:
        style = doc.styles.add_style('Heading 2', WD_STYLE_TYPE.PARAGRAPH) # type: ignore
        style.base_style = doc.styles['Normal']
        style.font.name = '宋体'
        style.font.size = docx.shared.Pt(14)
        style.font.bold = True
        logger.info("已创建缺失的样式：Heading 2")
# 处理Word文档格式化的函数
def format_docx_file(doc):
    """
    统一处理Word文档格式化
    
    Args:
        doc: python-docx的Document对象
        
    Returns:
        doc: 格式化后的Document对象
    """
    # 导入re模块，用于正则表达式匹配
    import re

    # 如果没有Heading 2样式，则创建
    #ensure_heading_styles(doc)
        

    # 检测是否进入参考文献部分
    in_references_section = False
    # 连续参考文献格式计数
    consecutive_ref_format_count = 0
    
    # 处理所有段落
    prev_is_heading = False
    for i, paragraph in enumerate(doc.paragraphs):
        # 判断是否为标题段落
        is_heading = paragraph.style.name.startswith('Heading')
        
        # 特别处理：使用正则匹配查找参考文献标题段落，支持各种格式
        # 匹配各种参考文献标题格式：
        # - # 参考文献、## 参考文献、### 参考文献等markdown标题
        # - 参考文献（纯文本）
        # - references、bibliography等英文标题
        references_pattern = r'^#+\s*.*?参考文献|^参考文献|^#+\s*references|^references|^#+\s*bibliography|^bibliography'
        if re.match(references_pattern, paragraph.text.strip(), re.IGNORECASE):
            logger.info(f"检测到参考文献标题: {paragraph.text}")
            in_references_section = True
            consecutive_ref_format_count = 0
            
            # 确保正确设置为标题样式
            if not is_heading:
                paragraph.style = doc.styles['Heading 2']
                logger.info(f"将参考文献段落设置为标题样式: {paragraph.text}")
        
        # 只通过格式特征识别参考文献 - 检测[数字]开头的格式
        if not in_references_section and paragraph.text.strip() and re.match(r'^\[\d+\]', paragraph.text.strip()):
            consecutive_ref_format_count += 1
            # 如果连续3个以上段落都是[数字]开头，认为进入了参考文献部分
            if consecutive_ref_format_count >= 3:
                in_references_section = True
                logger.info(f"通过格式特征识别到参考文献部分，起始于段落 {i-2}")
        elif not re.match(r'^\[\d+\]', paragraph.text.strip()):
            consecutive_ref_format_count = 0
        
        # 如果到了一个新的主要标题，离开参考文献部分
        if is_heading and paragraph.style.name == 'Heading 1':
            if in_references_section:
                logger.info(f"离开参考文献部分，遇到新标题: {paragraph.text}")
            in_references_section = False
            consecutive_ref_format_count = 0
        
        prev_is_heading = is_heading
        
        if is_heading:
            # 标题段落保持原样式，但更改字体为宋体
            for run in paragraph.runs:
                # 安全地设置字体
                try:
                    # 直接设置字体名称（中英文）
                    run.font.name = '宋体'
                    # 尝试使用更安全的方式设置东亚字体
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    # 设置西文字体为Times New Roman
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                    run.font.bold = True  # 确保标题是粗体
                    # 设置字体颜色为黑色
                    run.font.color.rgb = RGBColor(0, 0, 0)
                    
                    # 所有标题段落都移除斜体属性
                    run.font.italic = False  # 移除斜体属性
                    # 确保XML级别也移除斜体
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        for i in run._element.rPr.findall(docx.oxml.ns.qn('w:i')):
                            run._element.rPr.remove(i)
                        for i in run._element.rPr.findall(docx.oxml.ns.qn('w:iCs')):
                            run._element.rPr.remove(i)
                except Exception as e:
                    logger.warning(f"设置标题字体时出错: {str(e)}")
        else:
            # 非标题段落设置为正文样式
            paragraph.style = doc.styles['Normal']
            
            # 检查每个run，保留原始的加粗样式
            for run in paragraph.runs:
                # 保存原始加粗状态
                original_bold = run.bold
                
                # 直接设置字体名称（中英文）
                run.font.name = '宋体'
                # 同时设置东亚语言字体（确保中文显示为宋体）
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                # 设置西文字体为Times New Roman
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                
                # 恢复原始加粗状态，而不是强制设为False
                run.font.bold = original_bold
            
            # 设置段落格式：段落间距、行距等
            try:
                if is_heading:
                    # 标题段落设置
                    paragraph.paragraph_format.space_before = docx.shared.Pt(12)  # 标题前增加间距
                    paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 标题后统一间距
                    
                    # 一级标题(Heading 1)居中显示
                    if paragraph.style.name == 'Heading 1':
                        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                else:
                    # 正文段落设置 - 统一所有正文段落的间距
                    paragraph.paragraph_format.space_before = docx.shared.Pt(0)  # 移除段前间距
                    paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 统一设置段后间距为12磅
                    # 使用数值设置行距为1.5倍
                    paragraph.paragraph_format.line_spacing = 1.5
                    paragraph.paragraph_format.first_line_indent = docx.shared.Pt(21)  # 首行缩进两个中文字符
            except Exception as e:
                logger.warning(f"设置段落格式时出错: {str(e)}")
        
        # 强制段落之间有换行
        if paragraph.text.strip() and not is_heading:
            try:
                # 确保段落之间有分隔，防止段落粘连
                paragraph.paragraph_format.keep_with_next = False
                # 如果段落包含参考文献标记 [数字]，可能需要特殊处理
                if any(f"[{i}]" in paragraph.text for i in range(1, 100)):
                    paragraph.paragraph_format.space_after = docx.shared.Pt(6)  
            except Exception as e:
                logger.warning(f"设置段落换行属性时出错: {str(e)}")
        
                    # 处理引用标记为上标格式（如[1], [2]等）
        try:
            # 如果是标题段落，跳过引用标记处理，保持原有设置
            if is_heading:
                continue
                
            import re
            
            # 判断是否为参考文献条目
            # 条件更宽松：只要在参考文献部分，或者段落符合引用格式开头都视为参考文献
            # 支持多种格式：
            # - 纯数字引用: [1], [ 2], [3-5], [ 1, 2], [ 11 , 12 ] (包含任意空格)
            # - 含文本标识: [信息 1], [来源 2-3]
            # 注意：参考文献部分支持所有格式，但角标化只处理纯数字引用
            is_reference_item = in_references_section or bool(re.match(r'^\s*\[\s*(?:[^\[\]]+?\s+)?(?:\d+\s*(?:-\s*\d+)?(?:\s*,\s*(?:[^\[\]]+?\s+)?(?:\d+\s*(?:-\s*\d+)?))*)\s*\]', paragraph.text.strip()))
            
            # 调试日志
            if is_reference_item:
                logger.info(f"检测到参考文献项: {paragraph.text[:30]}...")
            
            # 如果是参考文献条目，使用特殊处理
            if is_reference_item:
                # 为参考文献设置特殊格式
                paragraph.paragraph_format.first_line_indent = docx.shared.Pt(-21)  # 悬挂缩进
                paragraph.paragraph_format.left_indent = docx.shared.Pt(21)  # 左缩进
                paragraph.paragraph_format.space_after = docx.shared.Pt(6)  # 减小段后间距
                paragraph.paragraph_format.line_spacing = 1.15  # 设置行间距
                
                # 处理参考文献条目文本，确保引用标记不是上标
                text = paragraph.text
                # 增强的正则表达式，匹配各种格式的引用标记
                # 支持[1], [ 2], [3-5], [ 1 , 2 ], [信息 1], [信息 1, 2]等多种格式
                if text and re.match(r'^\s*\[\s*(?:[^\[\]]+?\s+)?(?:\d+\s*(?:-\s*\d+)?(?:\s*,\s*(?:[^\[\]]+?\s+)?(?:\d+\s*(?:-\s*\d+)?))*)\s*\]', text):
                    try:
                        # 分离引用标记和内容，支持多种引用格式
                        match = re.match(r'^(\s*\[\s*(?:[^\[\]]+?\s+)?(?:\d+\s*(?:-\s*\d+)?(?:\s*,\s*(?:[^\[\]]+?\s+)?(?:\d+\s*(?:-\s*\d+)?))*)\s*\])(.*)$', text)
                        if match:
                            # 清除原有内容
                            paragraph.clear()
                            
                            # 重新添加引用标记（不作为上标）和内容
                            mark = match.group(1)
                            content = match.group(2)
                            
                            mark_run = paragraph.add_run(mark)
                            mark_run.bold = False
                            mark_run.font.name = '宋体'
                            mark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                            
                            if content:
                                content_run = paragraph.add_run(content)
                                content_run.font.name = '宋体'
                                content_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    except Exception as e:
                        logger.warning(f"处理参考文献条目时出错: {str(e)}")
                
                # 跳过上标处理，因为参考文献中的序号不应该是上标
                continue
            
            # 优化的引用标记正则表达式，仅匹配纯数字引用（不包含文本前缀）
            # 支持格式: 
            # - 纯数字引用: [1], [11, 18], [15,18], [ 11-18], [ 11 , 12 ]
            # - 不支持文本引用: [信息 1] 不会被处理为角标
            # 使用非贪婪匹配和明确的边界条件，确保捕获完整的引用
            citation_pattern = re.compile(r'\[(\s*\d+\s*(?:-\s*\d+)?(?:\s*,\s*\d+\s*(?:-\s*\d+)?)*)\s*\]')
            
            # 添加调试日志
            logger.info(f"处理段落引用: {paragraph.text[:50]}...")
            
            # 对于复杂的引用处理，直接修改文本内容并重建段落
            if citation_pattern.search(paragraph.text):
                # 获取原始文本
                original_text = paragraph.text
                # 创建包含所有样式设置的新段落
                new_paragraph = docx.Document().add_paragraph()
                new_paragraph.paragraph_format.alignment = paragraph.paragraph_format.alignment
                new_paragraph.paragraph_format.line_spacing = paragraph.paragraph_format.line_spacing
                new_paragraph.paragraph_format.space_before = paragraph.paragraph_format.space_before
                new_paragraph.paragraph_format.space_after = paragraph.paragraph_format.space_after
                new_paragraph.paragraph_format.first_line_indent = paragraph.paragraph_format.first_line_indent
                new_paragraph.style = paragraph.style
                
                # 遍历所有匹配
                last_end = 0
                for match in citation_pattern.finditer(original_text):
                    # 添加引用前的文本
                    if match.start() > last_end:
                        before_text = original_text[last_end:match.start()]
                        run_before = new_paragraph.add_run(before_text)
                        run_before.font.name = '宋体'
                        run_before._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                        run_before._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                        run_before._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                        
                    # 添加引用标记为上标
                    citation_text = match.group(0)  # 完整引用，如 [1] 或 [1, 2]
                    citation_run = new_paragraph.add_run(citation_text)
                    citation_run.font.name = '宋体'
                    
                    # 记录日志
                    logger.info(f"设置引用为上标: {citation_text}")
                    
                    # 直接设置字体上标属性
                    citation_run.font.superscript = True
                    
                    # 在XML级别确保上标正确设置
                    from docx.oxml import OxmlElement
                    if hasattr(citation_run._element, 'rPr'):
                        # 检查是否已有垂直对齐元素
                        vert_align = citation_run._element.rPr.find(docx.oxml.ns.qn('w:vertAlign'))
                        if vert_align is not None:
                            # 更新已有元素
                            vert_align.set(docx.oxml.ns.qn('w:val'), 'superscript')
                        else:
                            # 创建并添加新元素
                            superscript = OxmlElement('w:vertAlign')
                            superscript.set(docx.oxml.ns.qn('w:val'), 'superscript')
                            citation_run._element.rPr.append(superscript)
                    
                    # 设置字体
                    citation_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    citation_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                    citation_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                    citation_run.font.color.rgb = RGBColor(0, 0, 0)
                    
                    last_end = match.end()
                
                # 添加最后一段文本
                if last_end < len(original_text):
                    after_text = original_text[last_end:]
                    run_after = new_paragraph.add_run(after_text)
                    run_after.font.name = '宋体'
                    run_after._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    run_after._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                    run_after._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                
                # 清除原段落并复制新内容
                paragraph.clear()
                for run in new_paragraph.runs:
                    new_run = paragraph.add_run(run.text)
                    # 复制上标属性
                    new_run.font.superscript = run.font.superscript
                    # 复制字体设置
                    new_run.font.name = run.font.name
                    new_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    new_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                    new_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                    # 设置字体颜色为黑色
                    new_run.font.color.rgb = RGBColor(0, 0, 0)
                    
                    # 在XML级别再次确保上标设置
                    if run.font.superscript:
                        from docx.oxml import OxmlElement
                        if hasattr(new_run._element, 'rPr'):
                            vert_align = new_run._element.rPr.find(docx.oxml.ns.qn('w:vertAlign'))
                            if vert_align is not None:
                                # 更新已有元素
                                vert_align.set(docx.oxml.ns.qn('w:val'), 'superscript')
                            else:
                                # 创建并添加新元素
                                superscript = OxmlElement('w:vertAlign')
                                superscript.set(docx.oxml.ns.qn('w:val'), 'superscript')
                                new_run._element.rPr.append(superscript)
        except Exception as e:
            logger.warning(f"处理引用标记为上标时出错: {str(e)}")
    
    # 处理文档中的样式
    try:
        # 处理所有标题样式，确保全局样式定义中没有斜体属性并设置正确的间距
        for style in doc.styles:
            if style.name.startswith('Heading'):
                if hasattr(style.font, 'italic'):
                    style.font.italic = False
                
                # 调整标题样式的段落格式，确保标题和正文之间有足够间距
                if hasattr(style, 'paragraph_format'):
                    style.paragraph_format.space_before = docx.shared.Pt(12)
                    style.paragraph_format.space_after = docx.shared.Pt(12)  # 统一标题段后间距
                    
                    # 标题1居中显示
                    if style.name == 'Heading 1':
                        style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                    
                    # 重置可能干扰的XML级别设置
                    if hasattr(style._element, 'pPr') and style._element.pPr.spacing:
                        style._element.pPr.spacing.before = docx.shared.Pt(12)
                        style._element.pPr.spacing.after = docx.shared.Pt(12)
    except Exception as e:
        logger.warning(f"处理标题样式时出错: {str(e)}")
    
    return doc

# 通用的Markdown转Word处理函数
def convert_markdown_to_docx(markdown_file_path, output_filename):
    """
    将Markdown文件转换为格式化的Word文档
    
    Args:
        markdown_file_path: Markdown文件的路径
        output_filename: 输出的Word文件名
        
    Returns:
        tuple: (临时文件路径, 下载文件名)
    """
    import pypandoc
    
    # 创建临时输出文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as tmp:
        temp_docx_file = tmp.name
    
    # 预处理Markdown文件，处理参考文献部分的换行
    with open(markdown_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # 在原文中直接修改参考文献格式
    import re
    
    # 首先确保"## 参考文献"标题前后有足够的换行符，保证它能被正确识别为标题
    references_title_pattern = r'(^|\n)(#+\s*参考文献\s*)(\n|$)'
    
    def ensure_newlines_around_references_title(match):
        prefix = match.group(1)  # 前导部分（换行或文件开头）
        title = match.group(2)   # ## 参考文献
        suffix = match.group(3)  # 后面的换行符或文件结尾
        
        # 确保标题前有空行（至少两个换行符）
        if prefix == '\n':
            prefix = '\n\n'
        elif prefix == '':
            prefix = '\n\n'
        else:
            # 如果已经有多个换行符，保持原样
            pass
        
        # 确保标题后有空行（至少两个换行符）
        if suffix == '\n':
            suffix = '\n\n'
        elif suffix == '':
            suffix = '\n\n'
        else:
            # 如果已经有多个换行符，保持原样
            pass
        
        result = f"{prefix}{title}{suffix}"
        logger.info(f"已处理参考文献标题，确保其前后有足够空行: '{title}'")
        return result
    
    # 应用参考文献标题格式调整
    adjusted_content = re.sub(references_title_pattern, ensure_newlines_around_references_title, content)
    
    logger.info("开始识别参考文献部分")
    
    # 替换函数：确保每个参考文献前面有双换行
    def ensure_blank_line_before_ref(match):
        # 获取完整的匹配文本，包括可能的前导换行
        full_match = match.group(0)
        # 获取参考文献内容（[数字]开头的部分）
        ref_text = match.group(2)
        
        # 如果匹配到的前缀是一个换行符，添加一个额外的换行以形成空行
        if match.group(1) == '\n':
            return f"\n\n{ref_text}"
        # 如果匹配到的是文件开头（无前缀），或已经有空行，保持原样
        else:
            return full_match
    
    # 正则表达式：匹配(换行或文件开头)后跟随着[数字]开头的行
    # 分组1：前导部分（换行或文件开头）
    # 分组2：参考文献文本
    refs_pattern = r'(^|\n)(\[\d+\][^\n]+)'
    
    # 应用替换
    modified_content = re.sub(refs_pattern, ensure_blank_line_before_ref, adjusted_content)
    
    # 计算修改了多少处参考文献格式
    original_refs = re.findall(refs_pattern, adjusted_content)
    modified_refs = re.findall(refs_pattern, modified_content)
    
    if original_refs:
        logger.info(f"已优化 {len(original_refs)} 条参考文献的格式")
        # 使用修改后的内容
        new_content = modified_content
    else:
        logger.info("未识别到需要优化格式的参考文献")
        # 使用已经调整过参考文献标题格式的内容
        new_content = adjusted_content
    
    # 使用Pandoc转换Markdown到Word
    logger.info(f"使用Pandoc转换含格式化参考文献的内容到Word文档")
    
    # 创建临时处理后的Markdown文件
    with tempfile.NamedTemporaryFile(delete=False, mode='w', encoding='utf-8', suffix='.md') as tmp_md:
        tmp_md.write(new_content)
        processed_md_path = tmp_md.name
        
    logger.info(f"已创建处理后的临时文件: {processed_md_path}")
    
    # 转换文件
    pypandoc.convert_file(
        processed_md_path,
        'docx',
        outputfile=temp_docx_file,
        format='markdown',
        extra_args=[
            '--standalone',
            '--shift-heading-level-by=0'  # 保持原始标题级别: # -> 标题1, ## -> 标题2, ### -> 标题3
        ]
    )
    
    # 清理临时文件
    try:
        os.unlink(processed_md_path)
    except Exception:
        pass
    
    # 使用python-docx处理文档格式
    doc = Document(temp_docx_file)
    doc = format_docx_file(doc)
    doc.save(temp_docx_file)
    
    return temp_docx_file, output_filename

# 下载项目大纲的Word文档格式的接口（基于Pandoc实现）
@router.get("/{project_id}/download-outline-docx")
async def download_project_outline_as_docx(project_id: str, current_user: UserResponse = Depends(get_current_user)):
    """
    使用Pandoc下载项目大纲文件为Word文档格式
    
    Args:
        project_id: 项目ID
        current_user: 当前用户
        
    Returns:
        FileResponse: 可下载的Word格式大纲文件
    """
    try:
        import pypandoc
        
        # 检查用户是否为试用用户
        if current_user.is_trial:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="下载功能仅对付费用户开放，升级账户即可使用此功能"
            )
            
        # 检查项目是否存在并验证权限
        config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
        if not config_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目配置不存在"
            )
        
        # if config_db.user.id != current_user.id:
        #     raise HTTPException(
        #         status_code=status.HTTP_403_FORBIDDEN,
        #         detail="无权访问此项目"
        #     )
        
        # 检查大纲状态和文件是否存在
        if config_db.status in [ProjectConfigStatus.OUTLINE_GENERATING.value, 
                                  ProjectConfigStatus.OUTLINE_FAILED.value,
                                  ProjectConfigStatus.OUTLINE_CANCELED.value
                                  ]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="大纲尚未生成完成或生成失败"
            )
        
        # 获取大纲文件路径（优先使用人工修改的大纲）
        outline_path = config_db.manual_modified_outline or config_db.ai_generated_outline
        if not outline_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="大纲文件路径不存在"
            )
        
        # 处理文件路径
        if os.path.isabs(outline_path):
            abs_file_path = outline_path
        else:
            abs_file_path = os.path.join(os.getcwd(), outline_path)
        
        # 检查文件是否存在
        if not os.path.exists(abs_file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"大纲文件不存在: {outline_path}"
            )
        
        # 生成下载文件名
        if config_db.name:
            date_str = datetime.now().strftime("%Y-%m-%d")
            download_filename = f"outline_{config_db.name.replace(' ', '_')}_{date_str}.docx"
        else:
            download_filename = f"outline_{datetime.now().strftime('%Y-%m-%d')}.docx"
        
        # 使用通用函数处理转换
        try:
            upload_file.download_file(outline_path)
            temp_docx_file, _ = convert_markdown_to_docx(abs_file_path, download_filename)
        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文档处理失败: {str(e)}"
            )
        finally:
            upload_file.remove_local_file(outline_path)
        
        # 返回文件下载响应
        return FileResponse(
            path=temp_docx_file,
            filename=download_filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            background=BackgroundTask(lambda: os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None)
        )
    except ImportError as e:
        logger.error(f"导入pypandoc失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="需要安装pypandoc库才能使用此功能。请运行 'pip install pypandoc'"
        )
    except Exception as e:
        logger.error(f"下载Word文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载Word文档失败: {str(e)}"
        )

# 下载项目报告的Word文档格式的接口
@router.get("/{project_id}/download-report-docx")
async def download_project_report_as_docx(project_id: str, current_user: UserResponse = Depends(get_current_user)):
    """
    下载项目报告文件为Word文档格式
    
    Args:
        project_id: 项目ID
        current_user: 当前用户
        
    Returns:
        FileResponse: 可下载的Word格式报告文件
    """
    try:
        import pypandoc
        
        # 检查用户是否为试用用户
        if current_user.is_trial:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="下载功能仅对付费用户开放，升级账户即可使用此功能"
            )
            
        # 检查项目是否存在并验证权限
        config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
        if not config_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目配置不存在"
            )
        
        # if config_db.user.id != current_user.id:
        #     raise HTTPException(
        #         status_code=status.HTTP_403_FORBIDDEN,
        #         detail="无权访问此项目"
        #     )
        
        # 检查报告状态和文件是否存在
        if config_db.status not in [
            ProjectConfigStatus.REPORT_GENERATED.value,
            ProjectConfigStatus.REMOVE_HALLUCINATING.value,
            ProjectConfigStatus.REMOVE_HALLUCINATED.value,
            ProjectConfigStatus.REMOVE_AI_TRACING.value,
            ProjectConfigStatus.REMOVE_AI_TRACED.value
        ]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="报告尚未生成完成或生成失败"
            )
        
        # 获取报告文件路径
        report_path = config_db.manual_modified_report or config_db.ai_generated_report
        if not report_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报告文件路径不存在"
            )
        
        # 处理文件路径
        if os.path.isabs(report_path):
            abs_file_path = report_path
        else:
            abs_file_path = os.path.join(os.getcwd(), report_path)
        
        # 检查文件是否存在
        if not os.path.exists(abs_file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"报告文件不存在: {report_path}"
            )
        
        # 生成下载文件名
        if config_db.name:
            date_str = datetime.now().strftime("%Y-%m-%d")
            download_filename = f"{config_db.name.replace(' ', '_')}_{date_str}.docx"
        else:
            download_filename = f"report_{datetime.now().strftime('%Y-%m-%d')}.docx"
        
        # 使用通用函数处理转换
        try:
            upload_file.download_file(report_path)
            temp_docx_file, _ = convert_markdown_to_docx(abs_file_path, download_filename)
        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文档处理失败: {str(e)}"
            )
        finally:
            upload_file.remove_local_file(report_path)
        
        # 返回文件下载响应
        return FileResponse(
            path=temp_docx_file,
            filename=download_filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            background=BackgroundTask(lambda: os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None)
        )
    except ImportError as e:
        logger.error(f"导入pypandoc失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="需要安装pypandoc库才能使用此功能。请运行 'pip install pypandoc'"
        )
    except Exception as e:
        logger.error(f"下载Word文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载Word文档失败: {str(e)}"
        ) 