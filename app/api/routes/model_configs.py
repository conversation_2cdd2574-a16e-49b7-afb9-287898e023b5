from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from tortoise.expressions import Q
from app.utils.enum import ModelConfigError
from app.api.deps import get_current_user_from_state
from app.api.schemas.role import InsetRole
from app.models.model_config import ModelConfig
from app.api.schemas.model_config import (
    ModelConfigCreate,
    ModelConfigUpdate,
    ModelConfigResponse,
    ModelConfigResponseWithoutKey
)
from app.utils.utils import send_data, ResponseModel
from app.services.llm_token_service import test_model_connectivity
from app.core.logging import get_logger
from app.utils.utils import (
    send_page_data,
    ResponsePageModel,
    PageQuery
)
from app.utils.enum import ModelConfigError
from app.api.repository.organization_model import validate_model_distributed
from pydantic import UUID4
from app.utils.mobile_crypto import encrypt_data, decrypt_data

logger = get_logger(__name__)
router = APIRouter()


@router.post("", response_model=ResponseModel[ModelConfigResponseWithoutKey], status_code=status.HTTP_201_CREATED)
async def create_model_config(
    model_config_in: ModelConfigCreate,
    request: Request
):
    """创建新的模型配置"""
    current_user = get_current_user_from_state(request)
    try:
        # 检查模型名称是否已存在
        existing_config = await ModelConfig.filter(
            model_name=model_config_in.model_name,
            user_id=current_user.id,
            is_deleted=False
        ).first()
        if existing_config:
            return send_data(False, None, ModelConfigError.MODEL_EXISTED.value)
        
        # 检查是否是第一个模型
        # existing_models = await ModelConfig.filter(
        #     user_id=current_user.id,
        #     is_deleted=False
        # ).count()
        
        # # 如果是第一个模型，强制设置为默认
        # if existing_models == 0:
        #     model_config_in.is_active = True
        # else:
        #     # 如果设置为非激活状态，检查是否已有激活的模型配置
        #     if not model_config_in.is_active:
        #         active_config = await ModelConfig.filter(
        #             user_id=current_user.id,
        #             is_active=True,
        #             is_deleted=False
        #         ).first()
        #         if not active_config:
        #             raise HTTPException(
        #                 status_code=status.HTTP_400_BAD_REQUEST,
        #                 detail="必须选择一个做为默认模型！"
        #             )
        model_config_in.api_key = decrypt_data(model_config_in.api_key, False)
        # 检查模型API连通性
        is_connected, error_msg = await test_model_connectivity(
            api_key=model_config_in.api_key,
            api_url=model_config_in.api_url,
            model=model_config_in.model_name
        )
        
        if not is_connected:
            return send_data(False, None, error_msg)
        
        # 如果设置为激活状态，将用户其他模型配置设为非激活
        # if model_config_in.is_active:
        #     await ModelConfig.filter(
        #         user_id=current_user.id, 
        #         is_deleted=False
        #     ).update(is_active=False)
        
        # 创建新的模型配置
        model_config = await ModelConfig.create(
            **model_config_in.model_dump(),
            user_id=current_user.id
        )
        response = ModelConfigResponseWithoutKey.model_validate(model_config, from_attributes=True)
        # response.api_key = encrypt_data(response.api_key)
        return send_data(True, response) 
    except HTTPException as e:
        return send_data(False, None, e.detail)
    except Exception as e:
        return send_data(False, None, f"创建模型配置失败: {str(e)}")

@router.put("/{model_config_id}", response_model=ResponseModel[ModelConfigResponseWithoutKey])
async def update_model_config(
    model_config_id: UUID4,
    model_config_in: ModelConfigUpdate
):
    """更新模型配置"""
    try:    
        model_config = await ModelConfig.filter(
            id=model_config_id,
            is_deleted=False
        ).first()
        
        if not model_config:
            return send_data(False, None, ModelConfigError.NOT_RECORD)
        
        
        # 获取更新数据
        update_data = model_config_in.model_dump(exclude_unset=True)
          # 如果设置讲模型设置为禁用
        if "is_active" in update_data and not update_data["is_active"]:
            is_distributed = await validate_model_distributed(model_config_id)
            if is_distributed:
                return send_data(False, None, ModelConfigError.MODEL_IS_DISTRIBUTED.value)
        # 如果更新了模型名称，检查名称是否已存在
        if "model_name" in update_data and update_data["model_name"] != model_config.model_name:
            existing_config = await ModelConfig.filter(
                model_name=update_data["model_name"],
                is_deleted=False
            ).exclude(id=model_config_id).first()
            if existing_config:
                return send_data(False, None, ModelConfigError.MODEL_EXISTED.value)
        update_data["api_key"] = decrypt_data(update_data["api_key"], False)
        # 检查模型API连通性
        if "api_key" in update_data or "api_url" in update_data or "model_name" in update_data:
            api_key = update_data.get("api_key", model_config.api_key)
            api_url = update_data.get("api_url", model_config.api_url)
            model_name = update_data.get("model_name", model_config.model_name)
            is_connected, error_msg = await test_model_connectivity(
                api_key=api_key,
                api_url=api_url,
                model=model_name
            )
            
            if not is_connected:
                return send_data(False, None, error_msg)
      
        # 如果设置为激活状态，将用户其他模型配置设为非激活
        # if update_data.get("is_active", False):
        #     logger.info(f"update_data: {update_data}")
        #     await ModelConfig.filter(
        #         user_id=current_user.id, 
        #         is_deleted=False
        #     ).exclude(id=model_config_id).update(is_active=False)
        
        # 更新模型配置
        await model_config.update_from_dict(update_data)
        await model_config.save()
        response = ModelConfigResponseWithoutKey.model_validate(model_config, from_attributes=True)
        # response.api_key = encrypt_data(response.api_key)
        return send_data(True, response) 
    except HTTPException as e:
        return send_data(False, None, e.detail)
    except Exception as e:
        return send_data(False, None, f"更新模型配置失败: {str(e)}")


@router.get("/list", response_model=ResponseModel[List[ModelConfigResponseWithoutKey]], description="查询所有的模型(不分页)")
async def list_model_configs(
    request: Request,
    keyword: str = Query(default=None, description="搜索关键词")
):
    """查询所有的模型"""
    current_user = get_current_user_from_state(request)
    
    # 只有超级管理员才能访问
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        return send_data(False, None, "权限不足，只有超级管理员可以查看所有模型配置")
    
    try:
        # 构建基础查询
        base_query = None
        base_query = ModelConfig.filter(is_deleted=False, is_active=True)
        if keyword:
            queries = Q()
            for field in ["model_name", "name"]:
                queries |= Q(**{f"{field}__icontains": keyword})
            base_query = base_query.filter(queries)
        result = await base_query.order_by("-updated_at").all()
        return send_data(True, [ModelConfigResponseWithoutKey.model_validate(item, from_attributes=True) for item in result])
    except Exception as e:
        error_msg = f"{ModelConfigError.GET_LIST_FAIL.value} {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)

@router.get("/list/paginated", response_model=ResponsePageModel[ModelConfigResponseWithoutKey], description="查询所有模型（分页）")
async def list_model_configs_paginated(
    request: Request,
    query: PageQuery = Depends(),
    keyword: str = Query(default=None, description="搜索关键词")
):
    """分页查询模型配置"""
    current_user = get_current_user_from_state(request)
    
    # 只有超级管理员才能访问
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        return send_page_data(False, None, "权限不足，只有超级管理员可以查看所有模型配置")
    
    try:
        # 构建基础查询
        base_query = None
        base_query = ModelConfig.filter(is_deleted=False)
        if keyword:
            queries = Q()
            for field in ["model_name", "name"]:
                queries |= Q(**{f"{field}__icontains": keyword})
            base_query = base_query.filter(queries)
        total = await base_query.count()
        data = await base_query.order_by("-updated_at").offset(query.page-1).limit(query.size)
        
        # 使用统一的响应格式
        result = {
            "total": total,
            "page": query.page,
            "size": query.size,
            "items": [ModelConfigResponseWithoutKey.model_validate(item, from_attributes=True) for item in data]
        }
        
        return send_page_data(True, result)
    except Exception as e:
        error_msg = f"{ModelConfigError.GET_LIST_FAIL.value} {str(e)}"
        logger.error(error_msg)
        return send_page_data(False, None, error_msg)


@router.get("/{model_config_id}", response_model=ResponseModel[ModelConfigResponse])
async def get_model_config(
    model_config_id: str,
    request: Request
):
    """获取单个模型配置"""
    current_user = get_current_user_from_state(request)
    try:
        model_config = await ModelConfig.filter(
            id=model_config_id,
            user_id=current_user.id,
            is_deleted=False
        ).first()
        
        if not model_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型配置不存在"
            )
        model_config.api_key = encrypt_data(model_config.api_key)
        return send_data(True, ModelConfigResponse.model_validate(model_config, from_attributes=True))
    except HTTPException as e:
        return send_data(False, None, e.detail)
    except Exception as e:
        return send_data(False, None, f"获取模型配置失败: {str(e)}")


@router.delete("/{model_config_id}", response_model=ResponseModel[ModelConfigResponseWithoutKey])
async def delete_model_config(
    model_config_id: str
):
    """逻辑删除模型配置"""
    try:
        # 检查是否是最后一个模型
        # total_models = await ModelConfig.filter(
        #     user_id=current_user.id,
        #     is_deleted=False
        # ).count()
        
        # if total_models <= 1:
        #     raise HTTPException(
        #         status_code=status.HTTP_400_BAD_REQUEST,
        #         detail="不能删除最后一个模型配置"
        #     )
        # 判断模型是否已经被分配给机构使用
        is_distributed = await validate_model_distributed(
            model_id=model_config_id
        )
        if is_distributed:
            return send_data(False, None, ModelConfigError.MODEL_IS_DISTRIBUTED.value)
        model_config = await ModelConfig.filter(
            id=model_config_id,
            is_deleted=False
        ).first()
    
        if not model_config:
            return send_data(False, None, ModelConfigError.NOT_RECORD)
        
        # # 如果删除的是默认模型，将第一个非删除的模型设置为默认
        # if model_config.is_active:
        #     first_model = await ModelConfig.filter(
        #         user_id=current_user.id,
        #         is_deleted=False
        #     ).exclude(id=model_config_id).order_by("created_at").first()
            
        #     if first_model:
        #         first_model.is_active = True
        #         await first_model.save()
        
        # 执行逻辑删除
        model_config.is_deleted = True
        await model_config.save()
        # model_config.api_key = encrypt_data(model_config.api_key)
        return send_data(True, ModelConfigResponseWithoutKey.model_validate(model_config, from_attributes=True)) 
    except HTTPException as e:
        return send_data(False, None, e.detail)
    except Exception as e:
        return send_data(False, None, f"删除模型配置失败: {str(e)}")

