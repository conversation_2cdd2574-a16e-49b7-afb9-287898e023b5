import os
import tempfile
import time
import uuid
from datetime import datetime, timedelta
from urllib.parse import quote
from fastapi import APIRouter, Request, HTTPException, status, File, UploadFile, Form, Depends, Query
from fastapi.responses import FileResponse
import subprocess
import shutil


from app.core.logging import get_logger
from app.core.config import settings
from app.api.deps import get_current_user_from_state
from app.api.schemas.ppt import PPTGenerateRequest, PPTGenerateResponse, PPTTemplateType, PPTHistoryResponse, PPTListResponse, PPTDetailResponse
from app.services.ppt_service import ppt_service
from app.utils.utils import ResponseModel, send_data, PageQuery
from app.utils.document_parser import extract_text_from_file, get_supported_extensions, validate_file_extension, convert_doc_to_docx_with_libreoffice, check_libreoffice_available
from app.models.upload_file import UploadFile as UploadFileModel
from app.models.co_ai_ppt import CoAiPpt
from app.api.schemas.upload_file import UploadFileResponse
from typing import Optional
from app.api.schemas.role import InsetRole
from app.services.system_config_service import system_config_service
from app.services.content_moderation_service import content_moderation_service
# 从document_parser导入，不再从utils导入
# from app.utils.utils import convert_doc_to_docx_with_libreoffice, check_libreoffice_available, validate_converted_docx

# 添加新的导入
from docx import Document
from docx.shared import Inches

# 获取logger实例
logger = get_logger(__name__)

router = APIRouter()

@router.post("/generate", response_model=ResponseModel[PPTGenerateResponse], summary="基于文档生成PPT文件")
async def generate_ppt(
    request: Request,
    file: UploadFile = File(..., description="上传的文档文件"),
    ppt_type: PPTTemplateType = Form(PPTTemplateType.GRADUATION, description="使用的PPT模板类型")
):
    """
    基于上传的文档生成PPT文件
    
    Args:
        file: 上传的文档文件 (支持 docx, txt, pdf, md, doc)
        ppt_type: 使用的PPT模板类型，可选值：graduation(毕业答辩模板)、creative(创意模板)、simple(简约模板)
        
    Returns:
        生成的PPT文件信息
    """
    current_user = None
    temp_file_path = None
    converted_docx_path = None
    
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求生成PPT，文件: {file.filename}, 模板: {ppt_type}")
        
        # 验证文件
        if not file or not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须上传文件"
            )
        
        # 使用共用文档解析模块验证文件类型
        try:
            validate_file_extension(file.filename, raise_exception=True)
            file_ext = os.path.splitext(file.filename)[1].lower()
        except Exception as ext_error:
            supported_formats = ', '.join(get_supported_extensions())
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件类型，支持的格式: {supported_formats}"
            )
        
        # 读取文件内容
        content = await file.read()
        if not content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件内容为空"
            )
        
        # 创建临时文件
        temp_dir = tempfile.gettempdir()
        safe_filename = "".join(c for c in file.filename if c.isalnum() or c in '._-')
        temp_file_path = os.path.join(temp_dir, f"ppt_upload_{current_user.id}_{safe_filename}")
        
        # 保存临时文件
        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(content)
        
        logger.info(f"临时文件已保存: {temp_file_path}")
        
        # 处理 .doc 文件转换
        if file_ext == '.doc':
            try:
                logger.info(f"检测到 .doc 文件，开始使用 LibreOffice 转换为 .docx 格式")
                converted_docx_path =  convert_doc_to_docx_with_libreoffice(temp_file_path)
                logger.info(f".doc 文件转换成功: {converted_docx_path}")
                
                # 使用转换后的 .docx 文件进行后续处理
                temp_file_path = converted_docx_path
                file_ext = '.docx'
                
            except Exception as convert_error:
                logger.error(f".doc 文件转换失败: {str(convert_error)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f".doc 文件转换失败，请确保文件格式正确: {str(convert_error)}"
                )
        
        # 使用共用文档解析模块提取文档文本内容
        try:
            document_text = extract_text_from_file(temp_file_path, file_ext)
            logger.info(f"成功从 {file_ext} 文件提取文本，字符数: {len(document_text)}")
        except FileNotFoundError:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="临时文件丢失，请重新上传文件"
            )
        except PermissionError:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="文件访问权限不足，请检查文件权限"
            )
        except ValueError as ve:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文档格式错误: {str(ve)}"
            )
        except Exception as extract_error:
            logger.error(f"文档解析失败: {str(extract_error)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文档解析失败: {str(extract_error)}"
            )
        
        # 检查提取的文本是否为空
        if not document_text.strip():
            logger.error("文档中没有提取到文本内容", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文档中没有提取到文本内容"
            )
        
        # 内容长度检查（使用配置化限制）
        max_content_length = settings.PPT_MAX_CONTENT_LENGTH
        if len(document_text) > max_content_length:
            error_msg = f"文档内容过长，最大允许{max_content_length}字符，当前: {len(document_text)}字符"
            logger.error(error_msg)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        
        logger.info(f"文档内容预处理完成，字符数: {len(document_text)}")
        
        # 文本内容审核 - 检查是否包含敏感词或违规内容
        # 使用统一的审核方法，只需要获取是否通过的结果
        is_safe, error_msg = await content_moderation_service.verify_text_content(
            content=document_text,
            description="PPT文档内容"
        )
        if not is_safe:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        
        # 如果审核通过，is_content_safe 为 True，继续执行
        # 如果审核不通过，check_ppt_document 方法会抛出异常，不会执行到这里
        logger.info(f"文档内容审核通过，可以继续生成PPT")
        # 重置文件指针，然后直接使用原始file对象
        file.file.seek(0)  # 重置文件指针到开头
        
        # 使用标准的文件保存流程创建上传文件记录
        from app.api.repository.upload_file import save_upload_file
        upload_file_response = await save_upload_file(file, current_user)
        logger.info(f"上传文件记录已创建，ID: {upload_file_response.id}, 路径: {upload_file_response.file_path}")
        
        # 获取UploadFileModel对象
        upload_file_record = await UploadFileModel.filter(id=upload_file_response.id).first()
        
        # 获取永久文件路径（绝对路径）
        permanent_file_path = os.path.join(os.getcwd(), upload_file_response.file_path)
        
        # 调用PPT服务生成PPT
        try:
            result = await ppt_service.generate_ppt_from_document(
                document_content=document_text,
                user_id=str(current_user.id),
                filename=file.filename,
                ppt_type=ppt_type,
                upload_file_record=upload_file_record,
                original_file_path=permanent_file_path,
                current_user=current_user
            )
            
            # 添加文档内容预览到响应中
            content_preview = document_text[:500] + "..." if len(document_text) > 500 else document_text
            result.document_content = content_preview
            
            logger.info(f"PPT生成成功，文件路径: {result.file_path}")
            
            return send_data(True, result, "PPT生成成功")
            
        except ValueError as ve:
            logger.error(f"PPT生成参数错误: {str(ve)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"PPT生成参数错误: {str(ve)}"
            )
        except ConnectionError as ce:
            logger.error(f"AI服务连接失败: {str(ce)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="AI服务暂时不可用，请稍后重试"
            )
        except TimeoutError as te:
            logger.error(f"PPT生成超时: {str(te)}")
            raise HTTPException(
                status_code=status.HTTP_408_REQUEST_TIMEOUT,
                detail="PPT生成超时，请尝试减少文档内容或稍后重试"
            )
        except Exception as ppt_error:
            logger.error(f"PPT生成失败: {str(ppt_error)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"PPT生成失败: {str(ppt_error)}"
            )
    
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        error_msg = f"生成PPT时发生未知错误: {str(e)}"
        username = current_user.username if current_user else "Unknown"
        logger.error(f"用户 {username} 生成PPT失败: {error_msg}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.debug(f"已清理临时文件: {temp_file_path}")
            except Exception as cleanup_error:
                logger.warning(f"清理临时文件失败: {cleanup_error}")
        
        # 清理转换后的 .docx 文件和其临时目录
        if converted_docx_path and os.path.exists(converted_docx_path):
            try:
                # 获取转换文件的临时目录
                temp_dir = os.path.dirname(converted_docx_path)
                
                # 删除转换后的文件
                os.remove(converted_docx_path)
                logger.debug(f"已清理转换后的 .docx 文件: {converted_docx_path}")
                
                # 如果是临时目录且为空，则删除目录
                if (temp_dir and 
                    temp_dir != tempfile.gettempdir() and 
                    'libreoffice_convert_' in os.path.basename(temp_dir)):
                    try:
                        os.rmdir(temp_dir)  # 只删除空目录
                        logger.debug(f"已清理转换临时目录: {temp_dir}")
                    except OSError:
                        # 目录不为空或其他错误，忽略
                        pass
                        
            except Exception as cleanup_error:
                logger.warning(f"清理转换后的 .docx 文件失败: {cleanup_error}")


@router.get("/download/by-id/{ppt_id}", summary="根据PPT记录ID下载PPT文件")
async def download_ppt_by_id(
    request: Request,
    ppt_id: str
):
    """
    根据PPT生成记录ID下载对应的PPT文件
    
    Args:
        ppt_id: PPT生成记录的UUID
        
    Returns:
        PPT文件下载响应
    """
    current_user = None
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求根据ID下载PPT: {ppt_id}")
        
        # 验证UUID格式
        try:
            uuid.UUID(ppt_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的PPT记录ID格式"
            )
        
        # 查询PPT生成记录
        ppt_record = await CoAiPpt.filter(
            id=ppt_id,
            is_deleted=False
        ).select_related("user", "user__organization").first()
        
        if not ppt_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PPT记录不存在或已被删除"
            )
        
        # 检查权限：根据角色确定下载权限
        has_permission = False
        
        if current_user.role and current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
            # 超级管理员可以下载所有PPT文件
            has_permission = True
            logger.info(f"超级管理员 {current_user.username} 下载PPT文件，PPT ID: {ppt_id}")
        elif current_user.role and current_user.role.identifier == InsetRole.ADMIN.value:
            # 管理员可以下载同组织的PPT文件
            if (current_user.organization and 
                current_user.organization.id and 
                ppt_record.user.organization and 
                str(ppt_record.user.organization.id) == str(current_user.organization.id)):
                has_permission = True
                logger.info(f"管理员 {current_user.username} 下载组织内PPT文件，PPT ID: {ppt_id}")
            elif str(ppt_record.user.id) == str(current_user.id):
                # 管理员也可以下载自己的PPT
                has_permission = True
                logger.info(f"管理员 {current_user.username} 下载自己的PPT文件，PPT ID: {ppt_id}")
        else:
            # 普通用户只能下载自己的PPT文件
            if str(ppt_record.user.id) == str(current_user.id):
                has_permission = True
                logger.info(f"普通用户 {current_user.username} 下载自己的PPT文件，PPT ID: {ppt_id}")
        
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限下载此PPT文件"
            )
        
        # 检查PPT生成状态
        if ppt_record.status != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"PPT生成未完成，当前状态: {ppt_record.status}"
            )
        
        # 获取文件路径
        ppt_file_path = ppt_record.ppt_file_path
        if not ppt_file_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PPT文件路径不存在"
            )
        
        # 使用upload_file模块的download_file函数来获取文件
        # 这样可以正确处理OSS和本地文件
        from app.api.repository.upload_file import download_file
        
        try:
            # download_file函数会根据FILE_MODE自动处理OSS或本地文件
            download_file(ppt_file_path)
            logger.info(f"文件已准备就绪: {ppt_file_path}")
        except Exception as download_error:
            logger.error(f"获取PPT文件失败: {str(download_error)}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PPT文件不存在或无法访问"
            )
        
        # 构建本地文件路径
        if os.path.isabs(ppt_file_path):
            local_file_path = ppt_file_path
        else:
            local_file_path = os.path.join(os.getcwd(), ppt_file_path)
        
        # 检查文件是否存在
        if not os.path.exists(local_file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PPT文件不存在"
            )
        
        # 设置文件名（使用原始文件名或PPT文件名）
        # 优先使用PPT文件名，如果不存在则使用路径中的文件名
        # 使用"ppt_时间戳"的形式作为文件名
        filename = f"ppt_{int(time.time() * 1000)}.pptx"
        
        logger.info(f"用户 {current_user.username} 直接下载PPT文件: {filename}")
        
        # 使用FileResponse直接返回文件下载
        return FileResponse(
            path=local_file_path,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"根据ID下载PPT失败: {str(e)}"
        username = current_user.username if current_user else 'Unknown'
        logger.error(f"用户 {username} 根据ID下载PPT失败: {error_msg}")
        logger.exception("详细异常信息:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


@router.get("/history", response_model=ResponseModel[PPTListResponse], summary="获取PPT生成历史记录")
async def get_ppt_history(
    request: Request,
    page_query: PageQuery = Depends(),
    keyword: Optional[str] = Query(default=None, description="搜索关键词（根据原始文件名模糊搜索）")
):
    """
    获取当前用户的PPT生成历史记录（只显示近期的记录，天数可配置）
    
    Args:
        page_query: 分页查询参数
        keyword: 搜索关键词（可选，根据原始文件名模糊搜索）
        
    Returns:
        分页后的PPT历史记录列表（近期，天数可配置）
    """
    current_user = None
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求PPT历史记录，页码: {page_query.page}")
        
        # 构建查询条件 - 默认只能查看自己的数据
        queryset = CoAiPpt.filter(is_deleted=False, user_id=current_user.id)
        
        # 只查询近配置天数的记录
        filter_days_str = await system_config_service.get_config("history_record_filter_days")
        filter_days = int(filter_days_str) if filter_days_str and filter_days_str.isdigit() else 30
        thirty_days_ago = datetime.now() - timedelta(days=filter_days)
        logger.info(f"{filter_days}天前的时间: {thirty_days_ago}")
        queryset = queryset.filter(updated_at__gte=thirty_days_ago)
        # 根据用户权限扩展查询范围
        if current_user.role and current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
            # 超级管理员可以查看所有数据，移除user_id过滤条件
            queryset = CoAiPpt.filter(is_deleted=False)
            # 重新应用配置天数过滤条件
            queryset = queryset.filter(updated_at__gte=thirty_days_ago)
            logger.info(f"超级管理员 {current_user.username} 查看所有PPT历史记录（近{filter_days}天）")
        elif current_user.role and current_user.role.identifier == InsetRole.ADMIN.value:
            # 管理员可以查看同组织的数据
            if current_user.organization and current_user.organization.id:
                queryset = CoAiPpt.filter(is_deleted=False, user__organization_id=current_user.organization.id)
                # 重新应用配置天数过滤条件
                queryset = queryset.filter(updated_at__gte=thirty_days_ago)
                logger.info(f"管理员 {current_user.username} 查看组织 {current_user.organization.id} 的PPT历史记录（近{filter_days}天）")
            else:
                # 如果管理员没有组织，只能查看自己的数据（保持默认查询条件，已包含配置天数过滤）
                logger.info(f"管理员 {current_user.username} 无组织信息，只能查看自己的PPT历史记录（近{filter_days}天）")
        else:
            # 普通用户或没有role的用户，只能查看自己的数据（保持默认查询条件，已包含配置天数过滤）
            role_info = current_user.role.identifier if current_user.role else "无角色"
            logger.info(f"用户 {current_user.username} (角色: {role_info}) 查看自己的PPT历史记录（近{filter_days}天）")
        
        # 关键词搜索（根据原始文件名模糊搜索）
        if keyword:
            queryset = queryset.filter(original_filename__icontains=keyword)
        
        # 获取总数
        total_count = await queryset.count()
        
        # 分页查询（按创建时间倒序）
        ppt_records = await queryset.select_related(
            "upload_file", 
            "model_config", 
            "user"
        ).offset(
            (page_query.page - 1) * page_query.size
        ).limit(page_query.size).order_by("-updated_at")
        
        # 构建响应数据
        history_items = []
        for record in ppt_records:
            # 将数据库中的字符串状态转换为枚举类型
            try:
                from app.api.schemas.ppt import PPTGenerationStatus
                if record.status == "processing":
                    status_enum = PPTGenerationStatus.PROCESSING
                elif record.status == "completed":
                    status_enum = PPTGenerationStatus.COMPLETED
                elif record.status == "failed":
                    status_enum = PPTGenerationStatus.FAILED
                else:
                    status_enum = PPTGenerationStatus.PENDING
            except Exception:
                # 如果状态转换失败，默认为PENDING
                status_enum = PPTGenerationStatus.PENDING
            
            history_item = PPTHistoryResponse(
                id=str(record.id),
                original_filename=record.original_filename,
                original_file_path=record.original_file_path,
                ppt_filename=record.ppt_filename,
                ppt_file_path=record.ppt_file_path,
                file_type=record.file_type,
                ppt_type=record.model_type,
                status=status_enum,
                file_size=record.ppt_file_size,
                generation_duration=record.generation_duration,
                created_at=record.created_at,
                updated_at=record.updated_at,
                error_message=record.error_message
            )
            history_items.append(history_item)
        
        # 保持原有的响应格式以确保向后兼容
        response_data = PPTListResponse(
            items=history_items,
            total=total_count,
            page=page_query.page,
            page_size=page_query.size,
            total_pages=(total_count + page_query.size - 1) // page_query.size
        )
        
        logger.info(f"用户 {current_user.username} 获取PPT历史记录成功，共 {total_count} 条记录")
        
        return send_data(True, response_data, "获取PPT历史记录成功")
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"获取PPT历史记录失败: {str(e)}"
        username = current_user.username if current_user else 'Unknown'
        logger.error(f"用户 {username} 获取PPT历史记录失败: {error_msg}")
        logger.exception("详细异常信息:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


@router.get("/{ppt_id}/detail", response_model=ResponseModel[PPTDetailResponse], summary="根据PPT ID获取PPT详细信息")
async def get_ppt_detail(
    request: Request,
    ppt_id: str
):
    """
    根据PPT ID获取PPT的完整详细信息

    Args:
        ppt_id: PPT记录的唯一标识符

    Returns:
        PPT的详细信息，包含下载地址

    Raises:
        HTTPException: 当PPT不存在、权限不足或其他错误时
    """
    current_user = None

    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求获取PPT详细信息，PPT ID: {ppt_id}")

        # 验证PPT ID格式
        try:
            uuid.UUID(ppt_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的PPT ID格式"
            )

        # 查询PPT记录
        ppt_record = await CoAiPpt.filter(
            id=ppt_id,
            is_deleted=False
        ).select_related("user", "user__organization", "upload_file", "model_config").first()

        if not ppt_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PPT记录不存在或已被删除"
            )

        # 检查权限：根据角色确定访问权限
        has_permission = False
        
        if current_user.role and current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
            # 超级管理员可以查看所有PPT记录
            has_permission = True
            logger.info(f"超级管理员 {current_user.username} 查看PPT详情，PPT ID: {ppt_id}")
        elif current_user.role and current_user.role.identifier == InsetRole.ADMIN.value:
            # 管理员可以查看同组织的PPT记录
            if (current_user.organization and 
                current_user.organization.id and 
                ppt_record.user.organization and 
                str(ppt_record.user.organization.id) == str(current_user.organization.id)):
                has_permission = True
                logger.info(f"管理员 {current_user.username} 查看组织内PPT详情，PPT ID: {ppt_id}")
            elif str(ppt_record.user.id) == str(current_user.id):
                # 管理员也可以查看自己的PPT
                has_permission = True
                logger.info(f"管理员 {current_user.username} 查看自己的PPT详情，PPT ID: {ppt_id}")
        else:
            # 普通用户只能查看自己的PPT记录
            if str(ppt_record.user.id) == str(current_user.id):
                has_permission = True
                logger.info(f"普通用户 {current_user.username} 查看自己的PPT详情，PPT ID: {ppt_id}")
        
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限查看此PPT记录"
            )

        # 生成下载地址
        download_url = None
        if ppt_record.status == "completed" and ppt_record.ppt_file_path:
            # 直接使用下载接口的URL
            from app.utils.download_url_generator import generate_file_download_url
            download_url = generate_file_download_url(
                file_path=ppt_record.ppt_file_path,
                resource_id=ppt_id,
                user_id=str(current_user.id),
                expires_hours=24
            )


        # 将数据库中的字符串状态转换为枚举类型
        try:
            from app.api.schemas.ppt import PPTGenerationStatus
            if ppt_record.status == "processing":
                status_enum = PPTGenerationStatus.PROCESSING
            elif ppt_record.status == "completed":
                status_enum = PPTGenerationStatus.COMPLETED
            elif ppt_record.status == "failed":
                status_enum = PPTGenerationStatus.FAILED
            else:
                status_enum = PPTGenerationStatus.PENDING
        except Exception:
            # 如果状态转换失败，默认为PENDING
            status_enum = PPTGenerationStatus.PENDING

        # 构建详细信息响应
        detail_response = PPTDetailResponse(
            id=str(ppt_record.id),
            original_filename=ppt_record.original_filename,
            original_file_path=ppt_record.original_file_path,
            ppt_filename=ppt_record.ppt_filename,
            ppt_file_path=ppt_record.ppt_file_path,
            file_type=ppt_record.file_type,
            ppt_type=ppt_record.model_type,
            status=status_enum,
            file_size=ppt_record.ppt_file_size,
            generation_duration=ppt_record.generation_duration,
            error_message=ppt_record.error_message,
            created_at=ppt_record.created_at,
            updated_at=ppt_record.updated_at,
            download_url=download_url
        )

        logger.info(f"用户 {current_user.username} 成功获取PPT详细信息，PPT ID: {ppt_id}")

        return send_data(True, detail_response, "获取PPT详细信息成功")

    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"获取PPT详细信息失败: {str(e)}"
        username = current_user.username if current_user else 'Unknown'
        logger.error(f"用户 {username} 获取PPT详细信息失败，PPT ID: {ppt_id}, 错误: {error_msg}")
        logger.exception("详细异常信息:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


@router.delete("/{ppt_id}", summary="根据PPT ID逻辑删除PPT记录")
async def delete_ppt_by_id(
    request: Request,
    ppt_id: str
):
    """
    根据PPT ID逻辑删除PPT记录
    
    Args:
        ppt_id: PPT记录的唯一标识符
        
    Returns:
        删除操作结果
        
    Raises:
        HTTPException: 当PPT不存在、权限不足或其他错误时
    """
    current_user = None
    
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求删除PPT记录，PPT ID: {ppt_id}")
        
        # 验证PPT ID格式
        try:
            uuid.UUID(ppt_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的PPT ID格式"
            )
        
        # 查询PPT记录
        ppt_record = await CoAiPpt.filter(
            id=ppt_id,
            is_deleted=False
        ).select_related("user", "user__organization").first()
        
        if not ppt_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PPT记录不存在或已被删除"
            )
        
        # 检查权限：根据角色确定删除权限
        has_permission = False
        
        if current_user.role and current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
            # 超级管理员可以删除所有PPT记录
            has_permission = True
            logger.info(f"超级管理员 {current_user.username} 删除PPT记录，PPT ID: {ppt_id}")
        elif current_user.role and current_user.role.identifier == InsetRole.ADMIN.value:
            # 管理员可以删除同组织的PPT记录
            if (current_user.organization and 
                current_user.organization.id and 
                ppt_record.user.organization and 
                str(ppt_record.user.organization.id) == str(current_user.organization.id)):
                has_permission = True
                logger.info(f"管理员 {current_user.username} 删除组织内PPT记录，PPT ID: {ppt_id}")
            elif str(ppt_record.user.id) == str(current_user.id):
                # 管理员也可以删除自己的PPT
                has_permission = True
                logger.info(f"管理员 {current_user.username} 删除自己的PPT记录，PPT ID: {ppt_id}")
        else:
            # 普通用户只能删除自己的PPT记录
            if str(ppt_record.user.id) == str(current_user.id):
                has_permission = True
                logger.info(f"普通用户 {current_user.username} 删除自己的PPT记录，PPT ID: {ppt_id}")
        
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限删除此PPT记录"
            )
        
        # 执行逻辑删除
        from datetime import datetime
        await CoAiPpt.filter(id=ppt_id).update(
            is_deleted=True,
            deleted_at=datetime.now()
        )
        
        logger.info(f"用户 {current_user.username} 成功删除PPT记录，PPT ID: {ppt_id}")
        
        return send_data(True, None, "PPT记录删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"删除PPT记录失败: {str(e)}"
        username = current_user.username if current_user else 'Unknown'
        logger.error(f"用户 {username} 删除PPT记录失败，PPT ID: {ppt_id}, 错误: {error_msg}")
        logger.exception("详细异常信息:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )