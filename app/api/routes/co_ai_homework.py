import asyncio
import urllib.parse
from zoneinfo import ZoneInfo

from fastapi import APIRouter, Request, HTTPException, status, Path
from fastapi.responses import StreamingResponse, FileResponse
from typing import Optional
import json
import tempfile
import os
from datetime import datetime, timedelta
from starlette.background import BackgroundTask

from app.api.schemas.homework import HomeworkResponse, HomeworkListResponse, HomeworkDetailResponse
from app.api.schemas.ai_chat import DeleteConversationResponse, RenameConversationResponse, RenameConversationRequest
from app.core.config import settings
from app.core.logging import get_logger
from app.api.deps import get_current_user_from_state
from app.services.dify_service import DifyService, create_dify_service
from app.services.wallet.interface import AGENT
from app.services.wallet.service import wallet_service
from app.utils.utils import ResponseModel, send_data
from app.api.schemas.ai_chat import AIHomeworkRequest
from app.services.system_config_service import system_config_service
from app.services.text_moderation_service import text_moderation_service
from app.services.content_moderation_service import content_moderation_service
from pydantic import BaseModel

# 获取logger实例
logger = get_logger(__name__)

router = APIRouter()

UTC_ZONE = ZoneInfo("UTC")
TARGET_ZONE = ZoneInfo("Asia/Shanghai")

class ExportRequest(BaseModel):
    """导出请求模型"""
    content: str
    filename: Optional[str] = None

async def get_dify_service() -> DifyService:
    """获取Dify服务实例"""
    if not settings.DIFY_AI_HOMEWORK_API_KEY or not settings.DIFY_API_URL:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Dify API配置未设置，请检查环境变量DIFY_AI_HOMEWORK_API_KEY和DIFY_API_URL"
        )
    return await create_dify_service(settings.DIFY_AI_HOMEWORK_API_KEY, settings.DIFY_API_URL)



async def download_homework_file(
    content: str,
    current_user,
    filename: Optional[str] = None
):
    """
    下载作业解答为Word文档格式
    
    Args:
        content: 作业解答内容
        current_user: 当前用户
        filename: 自定义文件名（可选）
        
    Returns:
        FileResponse: 可下载的Word格式文件
    """
    if not content or not content.strip():
        raise Exception("导出内容不能为空")
    
    # 生成文件名（如果没有传入filename）
    output_filename = filename
    if not output_filename:
        date_str = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        output_filename = f"homework_answer_{date_str}.docx"
    elif not output_filename.endswith('.docx'):
        output_filename += '.docx'
    
    # 修复数学公式格式
    from app.utils.utils import fix_math_formula_format
    processed_content = fix_math_formula_format(content)
    
    # 创建临时markdown文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.txt', mode='w', encoding='utf-8') as tmp_file:
        tmp_file.write(processed_content)
        temp_md_file = tmp_file.name
    
    try:
        # 使用convert_markdown_to_docx函数转换
        from app.utils.utils import convert_markdown_to_docx
        
        # 调用原始的convert_markdown_to_docx函数
        response = convert_markdown_to_docx(temp_md_file)
        
        # 获取临时文件路径
        temp_docx_file = response.path
        
        # 返回新的FileResponse，使用自定义文件名
        return FileResponse(
            path=temp_docx_file,
            filename=output_filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            background=BackgroundTask(lambda: os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None)
        )
    finally:
        # 清理临时markdown文件
        try:
            os.unlink(temp_md_file)
        except:
            pass

@router.post("/ai_homework", summary="AI作业解答接口（支持流式输出）")
async def ai_homework(
    request: Request,
    homework_request: AIHomeworkRequest
):
    """
    AI作业解答接口，支持流式输出
    
    Args:
        homework_request: 作业解答请求参数（只包含 conversation_id 和 files，其他参数使用默认值）
        
    Returns:
        StreamingResponse: 流式响应，SSE格式
    """
    # 用于saas计费
    order_id = None
    business_id = None
    current_user = None
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 发起AI作业解答: {(homework_request.query or '')[:50]}...")

        # 内容审核 - 检查用户输入的文本内容
        if homework_request.query and homework_request.query.strip():
            is_safe, error_msg = await content_moderation_service.verify_text_content(
                content=homework_request.query, 
                description=f"用户 {current_user.username} 的作业查询"
            )
            if not is_safe:
                logger.warning(f"用户 {current_user.username} 输入内容审核未通过: {homework_request.query[:100]}...")
                # 内容审核失败，返回400状态码
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=error_msg
                )
            logger.info(f"用户 {current_user.username} 输入内容审核通过")
                
          
        # saas用户预计费
        try:
            order_id = await wallet_service.pre_charge(current_user.id, AGENT.HOMEWORK.value)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )

        # 将作业请求转换为聊天请求
        chat_request = homework_request.to_chat_message_request()
        logger.info(f"chat_request================= {chat_request.files}")
        # 获取Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识（使用用户ID作为唯一标识）
        user_identifier = f"user_{current_user.id}"
        
        # 调用Dify服务发送消息
        if chat_request.response_mode == "streaming":
            # 流式模式
            try:
                stream_generator = await dify_service.send_chat_message(
                    query=chat_request.query,
                    user=user_identifier,
                    inputs=chat_request.inputs,
                    response_mode=chat_request.response_mode,
                    conversation_id=chat_request.conversation_id,
                    files=chat_request.files,
                    auto_generate_name=chat_request.auto_generate_name
                )
                
                logger.info(f"开始流式响应AI作业解答，用户: {current_user.username}")
                
                # 包装流式生成器以确保异常安全
                async def safe_stream_wrapper():
                    is_confirmed = False
                    try:
                        async for chunk in stream_generator:
                            if not is_confirmed:
                                # 异步 计费确认
                                business_id = wallet_service.extract_conversation_id_from_chunk(chunk)
                                asyncio.create_task(wallet_service.charge_confirm(current_user.id, order_id, business_id))
                                is_confirmed = True
                            yield chunk
                    except Exception as inner_error:
                        # 异步 计费取消
                        asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, business_id))
                        logger.error(f"流式响应过程中发生错误: {str(inner_error)}")
                        error_content = f"流式响应错误: {str(inner_error)}"
                        yield f"data: {json.dumps({'status': 'error', 'content': error_content})}\n\n"
                
                return StreamingResponse(
                    safe_stream_wrapper(),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "X-Accel-Buffering": "no"
                    }
                )
            except Exception as stream_error:
                # 流式生成器创建失败的错误处理
                # 异步 计费取消
                asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, business_id))
                error_msg = f"创建流式响应失败: {str(stream_error)}"
                logger.error(f"用户 {current_user.username} 创建流式响应失败: {error_msg}")
                
                async def error_stream():
                    try:
                        yield f"data: {json.dumps({'status': 'error', 'content': error_msg})}\n\n"
                    except Exception as json_error:
                        logger.error(f"JSON序列化错误: {json_error}")
                        yield f"data: {{'status': 'error', 'content': 'Internal server error'}}\n\n"
                
                return StreamingResponse(
                    error_stream(),
                    media_type="text/event-stream",
                    status_code=500
                )
        else:
            # 阻塞模式
            try:
                result = await dify_service.send_chat_message(
                    query=chat_request.query,
                    user=user_identifier,
                    inputs=chat_request.inputs,
                    response_mode=chat_request.response_mode,
                    conversation_id=chat_request.conversation_id,
                    files=chat_request.files,
                    auto_generate_name=chat_request.auto_generate_name
                )
                
                logger.info(f"AI作业解答完成，用户: {current_user.username}, 消息ID: {result.get('message_id')}")
                logger.info(f"AI作业解答完成，result: {result} ")
                logger.info(f"AI作业解答完成，result type: {isinstance(result, dict)}")
                # 确保result是可序列化的    
                if result is None:
                    logger.warning("Dify服务返回了None结果")
                    result = {"message": "响应为空"}
                # 异步 计费确认
                business_id = result.get("conversation_id")
                asyncio.create_task(wallet_service.charge_confirm(current_user.id, order_id, business_id))
                return send_data(True, result)
                
            except Exception as dify_error:
                # 异步 计费取消
                asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, business_id))
                error_msg = f"Dify服务错误: {str(dify_error)}"
                logger.error(f"用户 {current_user.username} Dify服务调用失败: {error_msg}")
                return send_data(False, None, error_msg)
            
    except Exception as e:
        # 异步 计费取消
        asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, business_id))

        error_msg = f"AI作业解答失败: {str(e)}"
        username = current_user.username if current_user else 'Unknown'
        logger.error(f"用户 {username} AI作业解答失败: {error_msg}")
        logger.exception("详细异常信息:")  # 记录完整的异常堆栈
        
        try:
            if hasattr(chat_request, 'response_mode') and chat_request.response_mode == "streaming":
                # 流式模式下返回错误事件
                async def error_stream():
                    try:
                        yield f"data: {json.dumps({'status': 'error', 'content': error_msg})}\n\n"
                    except Exception as json_error:
                        logger.error(f"错误流JSON序列化失败: {json_error}")
                        yield f"data: {{'status': 'error', 'content': 'Critical error'}}\n\n"
                
                return StreamingResponse(
                    error_stream(),
                    media_type="text/event-stream",
                    status_code=500
                )
            else:
                return send_data(False, None, error_msg)
        except Exception as final_error:
            # 最后的兜底处理
            logger.error(f"最终异常处理失败: {str(final_error)}")
            # 返回一个最简单的错误响应
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="服务器内部错误"
            )

@router.post("/export-homework", summary="导出AI作业解答为Word格式")
async def export_homework(
    request: Request,
    export_request: ExportRequest
):
    """
    导出AI作业解答为Word格式
    
    Args:
        export_request: 导出请求模型
        
    Returns:
        FileResponse: 可下载的Word文件
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求导出作业解答为Word格式")
        
        # 验证内容不为空
        if not export_request.content or not export_request.content.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="导出内容不能为空"
            )
        
        # 使用download_homework_file函数，参考project_report.py的写法
        return await download_homework_file(
            content=export_request.content,
            current_user=current_user,
            filename=export_request.filename
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        error_msg = f"导出作业解答失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 导出作业解答失败: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )

@router.post("/export-homework-pdf", summary="导出AI作业解答为PDF格式")
async def export_homework_pdf(
    request: Request,
    export_request: ExportRequest
):
    """
    导出AI作业解答为PDF格式
    
    Args:
        export_request: 导出请求模型
        
    Returns:
        FileResponse: 可下载的PDF文件
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求导出作业解答为PDF格式")
        
        # 验证内容不为空
        if not export_request.content or not export_request.content.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="导出内容不能为空"
            )
        
        # 生成文件名（如果没有传入filename）
        output_filename = export_request.filename
        if not output_filename:
            date_str = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            output_filename = f"homework_answer_{date_str}.pdf"
        elif not output_filename.endswith('.pdf'):
            output_filename += '.pdf'
        
        # 修复数学公式格式
        from app.utils.utils import fix_math_formula_format
        processed_content = fix_math_formula_format(export_request.content)
        
        # 创建临时markdown文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.txt', mode='w', encoding='utf-8') as tmp_file:
            tmp_file.write(processed_content)
            temp_md_file = tmp_file.name
        
        try:
            # 使用convert_markdown_to_pdf函数转换
            from app.utils.utils import convert_markdown_to_pdf
            
            # 调用convert_markdown_to_pdf函数
            response = convert_markdown_to_pdf(temp_md_file)
            
            # 获取临时文件路径
            temp_pdf_file = response.path
            
            # 返回新的FileResponse，使用自定义文件名
            return FileResponse(
                path=temp_pdf_file,
                filename=output_filename,
                media_type="application/pdf",
                background=BackgroundTask(lambda: os.unlink(temp_pdf_file) if os.path.exists(temp_pdf_file) else None)
            )
        finally:
            # 清理临时markdown文件
            try:
                os.unlink(temp_md_file)
            except:
                pass
                
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        error_msg = f"导出作业解答PDF失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 导出作业解答PDF失败: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )

@router.get("/history", response_model=ResponseModel[HomeworkListResponse], summary="获取作业助手会话列表")
async def get_homework_conversations(
    request: Request,
    last_id: str = None,
    page_size: int = 20
):
    """
    获取当前用户的作业助手会话列表
    
    Args:
        last_id: 上一页最大的id
        page_size: 每页数量，最大100
    Returns:
        用户的作业助手会话列表
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 获取作业助手会话列表")
        
        # 获取作业助手Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识
        user_identifier = f"user_{current_user.id}"
        
        # 获取会话列表
        filter_days_str = await system_config_service.get_config("history_record_filter_days")
        filter_days = int(filter_days_str) if filter_days_str and filter_days_str.isdigit() else 30
        conversations = await dify_service.get_conversations(
            user=user_identifier,
            last_id=last_id,
            limit=page_size,
            filter_days=filter_days  # 只获取配置天数内的会话
        )
        history_items = []

        if 'data' in conversations and isinstance(conversations['data'], list):
            for record in conversations.get('data',[]):
                # 设置时区（移除重复的时间过滤，信任dify_service的过滤结果）
                created_at_timestamp = record.get('created_at', 0)
                utc_time = datetime.fromtimestamp(created_at_timestamp, tz=UTC_ZONE)
                history_item = HomeworkResponse(
                    id=record.get('id'),
                    name=record.get('name'),
                    created_at=utc_time.astimezone(TARGET_ZONE)
                )
                # 将新创建的对象添加到结果列表中
                history_items.append(history_item)

        response_data = HomeworkListResponse(
            items=history_items,
            page_size=page_size,
            has_more=conversations['has_more']
        )

        logger.info(f"成功获取用户 {current_user.username} 的作业助手会话列表，数量: {len(history_items)}")
        return send_data(True, response_data, "获取作业助手会话列表成功")
        
    except Exception as e:
        error_msg = f"获取作业助手会话列表失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 获取作业助手会话列表失败: {error_msg}")
        return send_data(False, None, error_msg)

@router.get("/{homework_id}", response_model=ResponseModel[HomeworkDetailResponse], summary="获取作业助手会话的第一条消息答案")
async def get_homework_messages(
    request: Request,
    homework_id: str
):
    """
    获取作业助手会话的第一条消息的答案

    Args:
        homework_id: 会话ID，必填

    Returns:
        HomeworkDetailResponse: 包含成功状态、状态码、数据和错误信息的响应
        - file: 题目文件
        - answer: 解题详情
        - conversation_id: 会话ID
        - error: 错误信息，成功时为空字符串
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 获取作业助手会话历史消息，会话ID: {homework_id}")
        
        # 获取作业助手Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识
        user_identifier = f"user_{current_user.id}"
        
        # 获取会话历史消息
        messages = await dify_service.get_messages(
            conversation_id=homework_id,
            user=user_identifier
        )

        data = messages['data']
        # 获取第一条消息
        first_message = data[0]
        # 获取answer字段
        answer = first_message.get('answer', '')

        message_files = first_message.get('message_files', [])
        first_file = None

        if message_files:
            first_file= message_files[0]
            encoded_name = first_file.get('filename')
            parts = encoded_name.split("_", 3)

            if encoded_name:
                # 上传文件的地方的命名规则： temp_file_path = os.path.join(temp_dir, f"dify_upload_{current_user.id}_{safe_filename}")
                # 所以这里需要根据下划线阶段并获取真正的文件名（最多分成4部分，防止文件名中也包含下划线）
                parts = encoded_name.split("_", 3)
                if len(parts) == 4:
                    first_file['filename'] = urllib.parse.unquote(parts[3])  # 更新为解码后的文件名
        # 构建成功的响应
        response_data = HomeworkDetailResponse(
            answer=answer,
            file=first_file,
            conversation_id=homework_id
        )

        logger.info(f"成功获取用户 {current_user.username} 的作业助手会话历史消息，会话ID: {homework_id}, answer长度: {len(answer)}")
        return send_data(True, response_data, "获取作业助手会话历史消息")
    except Exception as e:
        error_msg = f"获取作业助手会话历史消息失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 获取作业助手会话历史消息失败: {error_msg}")
        return send_data(False, None, error_msg) 

@router.delete("/conversations/{conversation_id}", response_model=ResponseModel[DeleteConversationResponse], summary="删除作业会话")
async def delete_homework_conversation(
    request: Request,
    conversation_id: str = Path(..., description="会话ID")
):
    """
    删除指定作业会话
    
    Args:
        conversation_id: 会话ID
        
    Returns:
        删除操作的结果
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求删除作业会话，会话ID: {conversation_id}")
        
        # 获取Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识
        user_identifier = f"user_{current_user.id}"
        
        # 调用Dify服务删除会话
        result = await dify_service.delete_conversation(
            conversation_id=conversation_id,
            user=user_identifier
        )
        
        # 检查Dify服务返回的结果
        if result.get("result") == "success" or result.get("message") == "会话不存在或已被删除":
            logger.info(f"成功删除作业会话，用户: {current_user.username}, 会话ID: {conversation_id}")
            return send_data(True, {
                "result": "success",
                "conversation_id": conversation_id,
                "message": result.get("message", "会话已删除")
            })
        else:
            # 如果Dify服务返回了其他结果，直接返回
            logger.info(f"删除作业会话完成，用户: {current_user.username}, 会话ID: {conversation_id}, 结果: {result}")
            return send_data(True, result)
        
    except Exception as e:
        error_msg = f"删除作业会话失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 删除作业会话失败: {error_msg}")
        return send_data(False, None, error_msg)

@router.post("/conversations/{conversation_id}/name", response_model=ResponseModel[RenameConversationResponse], summary="重命名作业会话")
async def rename_homework_conversation(
    request: Request,
    rename_request: RenameConversationRequest,
    conversation_id: str = Path(..., description="会话ID")
):
    """
    重命名指定作业会话
    
    Args:
        conversation_id: 会话ID
        rename_request: 重命名请求参数
        
    Returns:
        重命名操作的结果
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求重命名作业会话，会话ID: {conversation_id}, 新名称: {rename_request.name}")
        
        # 获取Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识
        user_identifier = f"user_{current_user.id}"
        
        # 调用Dify服务重命名会话
        result = await dify_service.rename_conversation(
            conversation_id=conversation_id,
            user=user_identifier,
            name=rename_request.name,
            auto_generate=rename_request.auto_generate
        )
        
        logger.info(f"成功重命名作业会话，用户: {current_user.username}, 会话ID: {conversation_id}")
        return send_data(True, {
            "result": "success",
            "conversation_id": conversation_id,
            "name": result.get("name", rename_request.name)
        })
        
    except Exception as e:
        error_msg = f"重命名作业会话失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 重命名作业会话失败: {error_msg}")
        return send_data(False, None, error_msg) 