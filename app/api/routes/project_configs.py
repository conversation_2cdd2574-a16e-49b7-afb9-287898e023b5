from typing import List, Optional

from app.api.repository import upload_file
from app.core.logging import get_logger
from fastapi import APIRouter, Depends, status, Body, Request, Query, UploadFile, File, HTTPException
from datetime import datetime, timedelta
from app.utils.enum import UseCase
from app.api.repository.user_default_model import get_user_model
from app.api.deps import get_current_user_from_state
from app.models.project_configs import ProjectConfig
from app.api.schemas.project_configs import (
    ProjectConfigCreate,
    ProjectConfigUpdate,
    ProjectConfigResponse2,
    ProjectConfigStatus,
    ProjectConfigResponseWithoutModel
)
from app.utils.utils import (
    send_data,
    ResponseModel,
    PageQuery,
    send_page_data,
    ResponsePageModel,
    # save_text_to_file,
    sanitize_filename,
    handle_text_before_use,
    validate_file_security
)
from app.services.product_configs_service import ProductConfigsService
from app.api.schemas.role import Inset<PERSON>ole
from app.services.system_config_service import system_config_service
from app.api.repository.project_config import (
  get_one_project_config,
  get_project_list
)
from app.api.repository.upload_file import save_upload_file, get_file_content_by_id
from app.api.schemas.upload_file import UploadFileResponse
import os
from app.utils.enum import ProjectConfigError, UploadFileError
from app.core.config import settings
from fastapi.responses import FileResponse
from starlette.background import BackgroundTask

logger = get_logger(__name__)
router = APIRouter()


# 管理员接口
# 管理员获取项目配置列表
@router.get("/list", response_model=ResponseModel[List[ProjectConfigResponseWithoutModel]])
async def read_project_configs_admin(
    request: Request,
    is_deleted: Optional[int] = 0,
    status: Optional[str] = None
):
    current_user = get_current_user_from_state(request)
    result = await get_project_list(is_deleted=is_deleted, status=status, current_user=current_user)
    return send_data(True, result)

# 管理员获取指定项目配置
@router.get("/{project_id}", response_model=ResponseModel[ProjectConfigResponseWithoutModel])
async def read_project_config_admin(
    project_id: str,
    request: Request
):
    try:
        current_user = get_current_user_from_state(request)
        result = await get_one_project_config(project_id)
        if result is None:
            return send_data(False, None, "项目配置不存在")
        if (result.user.id != current_user.id
            and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
        ):
            return send_data(False, None, "无权访问此项目")
        
        # 管理员只能查看本组织的项目（超级管理员除外）
        if (current_user.role.identifier == InsetRole.ADMIN 
            and result.user.organization and current_user.organization
            and result.user.organization.id != current_user.organization.id
        ):
            return send_data(False, None, "无权访问此项目")
        return send_data(True, ProjectConfigResponseWithoutModel.model_validate(result.model_dump(), from_attributes=True))
    except Exception as e:
        return send_data(True, None, str(e))

# 管理员创建项目配置
@router.post("", response_model=ResponseModel[ProjectConfigResponseWithoutModel], status_code=status.HTTP_201_CREATED)
async def create_project_config_admin(
    request: Request,
    config_in: ProjectConfigCreate = None
):
    """创建新的项目配置（管理员接口）"""
    current_user = get_current_user_from_state(request)
    try:
        # 创建项目配置
        config_data = config_in.model_dump() if config_in else {"is_deleted": 0}
        
        # 获取用户的默认模型
        first_model = await get_user_model(
            current_user=current_user,
            use_case=UseCase.PAPER_GENERATE.value
        )
        # model_id = first_model.id if first_model else None
        # 移除config_data中的model字段（如果存在）
        # config_data.pop("model", None)
        # research_id = None
        # if "research" in config_data:
        #     research_id = config_data.pop("research")    
        # logger.info(f"config_data: {config_data}")
        config = await ProjectConfig.create(
          **config_data,
          model_id=first_model.id,
          user_id=current_user.id,
          word_count_requirement=settings.PAPER_WORD_COUNT,
          status=ProjectConfigStatus.CONFIGURING.value
        )
        logger.info(f"config_data: {config_data}")
        result = await get_one_project_config(config.id)
        
        return send_data(True, ProjectConfigResponseWithoutModel.model_validate(result.model_dump(), from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"创建项目配置失败: {str(e)}")

# 管理员更新项目配置
@router.put("/{project_id}", response_model=ResponseModel[ProjectConfigResponseWithoutModel])
async def update_project_config_admin(
    project_id: str, config_in: ProjectConfigUpdate
):
    """更新项目配置（管理员接口）"""
    config = await ProjectConfig.filter(id=project_id).first()
    if config is None:
        return send_data(False, None, "项目配置不存在")
    
    try:
        # 处理更新字段，只更新用户提交的字段
        update_data = config_in.model_dump(exclude_unset=True, exclude_none=True)
        
        name = update_data.get("name")
        # application_category = update_data.get("application_category")
        if config.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
            return send_data(False, None, "大纲生成中，无法修改")
        if config.status == ProjectConfigStatus.REPORT_GENERATING.value:
            return send_data(False, None, "报告生成中，无法修改")
        # 验证名称和申报口径是否可以更改
        if name and name != config.name and config.status != ProjectConfigStatus.CONFIGURING.value:
            return send_data(False, None, "研究主体不可以更改")
        # 验证外键关联是否存在
        if "user_add_demo_id" in update_data:
            demo_id = update_data.pop("user_add_demo_id")
            if demo_id:
                demo_data = await get_file_content_by_id(demo_id)
                if not demo_data:
                    return send_data(False, None, UploadFileError.NOT_RECORD.value)
                else:
                    update_data["user_add_demo_id"] = demo_id
        # 如果更新了is_deleted字段为1，设置deleted_at时间
        if "is_deleted" in update_data and update_data["is_deleted"] == 1:
            update_data["deleted_at"] = datetime.now()
        await config.update_from_dict(update_data)
        
        result = await get_one_project_config(config.id)
        return send_data(True, ProjectConfigResponseWithoutModel.model_validate(result.model_dump(), from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"更新项目配置失败: {str(e)}")

# 管理员软删除项目配置
@router.delete("/{project_id}", response_model=ResponseModel[ProjectConfigResponseWithoutModel])
async def soft_delete_project_config_admin(
    project_id: str,
    request: Request
):
    """软删除项目配置（管理员接口）"""
    current_user = get_current_user_from_state(request)
    config = await ProjectConfig.filter(id=project_id).prefetch_related("user", "user__organization").first()
    if config is None:
        return send_data(False, None, "项目配置不存在")
    if (config.user_id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]    
    ):
        return send_data(False, None, "权限不足")
    
    # 管理员只能删除本组织的项目（超级管理员除外）
    if (current_user.role.identifier == InsetRole.ADMIN 
        and config.user.organization and current_user.organization
        and config.user.organization.id != current_user.organization.id
    ):
        return send_data(False, None, "权限不足")
    if config.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
        return send_data(False, None, "大纲生成中，无法删除")
    if config.status == ProjectConfigStatus.REPORT_GENERATING.value:
        return send_data(False, None, "报告生成中，无法删除")
    try:
        # 标记为已删除
        config.is_deleted = 1
        config.deleted_at = datetime.now()
        await config.save()
        
        result = await get_one_project_config(config.id)
        return send_data(True, ProjectConfigResponseWithoutModel.model_validate(result.model_dump(), from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"删除项目配置失败: {str(e)}")

# 更新手动修改大纲接口
@router.post("/{project_id}/update-manual-outline", response_model=ResponseModel[ProjectConfigResponseWithoutModel])
async def update_manual_outline(
    request: Request,
    project_id: str, 
    manual_modified_outline: str = Body(..., embed=True)
):
    """手动更新项目大纲内容"""
    # 检查项目是否存在并验证权限
    current_user = get_current_user_from_state(request)
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related(
        "user",
        "user__role",
        "user__organization",
    ).first()
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return send_data(False, None, "无权访问此项目")
    
    # 管理员只能修改本组织的项目（超级管理员除外）
    if (current_user.role.identifier == InsetRole.ADMIN 
        and config_db.user.organization and current_user.organization
        and config_db.user.organization.id != current_user.organization.id
    ):
        return send_data(False, None, "无权访问此项目")
    
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    timestamp = int(datetime.now().timestamp() * 1000)
    file_name = f"manual_outline_{timestamp}.txt"
    if config_db.name:
      file_name = sanitize_filename(f"manual_outline_{config_db.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = config_db.manual_modified_outline or f"{project_folder}/{file_name}"
    upload_file.write(
        path=relative_path,
        content=handle_text_before_use(manual_modified_outline)
    )
    # 更新项目配置
    config_db.manual_modified_outline = relative_path
    config_db.manual_modified_outline_time = datetime.now()
    config_db.ai_generated_report = None
    config_db.report_generation_time = None
    config_db.manual_modified_report_time = None
    config_db.manual_modified_report = None
    await config_db.save()
    
    result = await get_one_project_config(config_db.id)
    return send_data(True, ProjectConfigResponseWithoutModel.model_validate(result.model_dump(), from_attributes=True))
# 更新修改报告接口
@router.post("/{project_id}/update-report", response_model=ResponseModel[ProjectConfigResponseWithoutModel])
async def update_report(
    request: Request,
    project_id: str, 
    manual_modified_report: str = Body(..., embed=True)
):
    """手动更新报告的内容"""
    # 检查项目是否存在并验证权限
    current_user = get_current_user_from_state(request)
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related(
        "user",
        "user__role",
        "user__organization"
    ).first()
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]    
    ):
        return send_data(False, None, "无权访问此项目")
    
    # 管理员只能修改本组织的项目（超级管理员除外）
    if (current_user.role.identifier == InsetRole.ADMIN 
        and config_db.user.organization and current_user.organization
        and config_db.user.organization.id != current_user.organization.id
    ):
        return send_data(False, None, "无权访问此项目")
    
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    timestamp = int(datetime.now().timestamp() * 1000)
    file_name = f"manual_report_{timestamp}.txt"
    if config_db.name:
      file_name = sanitize_filename(f"manual_report_{config_db.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"

    upload_file.write(
        path=relative_path,
        content=handle_text_before_use(manual_modified_report),
    )
    # 更新项目配置
    config_db.manual_modified_report = relative_path
    config_db.manual_modified_report_time = datetime.now()
    await config_db.save()
   
    result = await get_one_project_config(config_db.id)
    return send_data(True, ProjectConfigResponseWithoutModel.model_validate(result.model_dump(), from_attributes=True))

# 优化项目名称接口
@router.post("/optimize-name", response_model=ResponseModel[str], deprecated=True)
async def optimize_project_name(
    request: Request,
    project_configs_name: str = Body(..., embed=True)
):
    """优化项目名称"""
    current_user = get_current_user_from_state(request)
    service = ProductConfigsService()
    model = await get_user_model(
        current_user=current_user,
        use_case=UseCase.PROJECT_CONFIG_NEED.value
    )
    return await service.optimize_project_name(project_configs_name, model)

@router.get("/list/paginated", response_model=ResponsePageModel[ProjectConfigResponseWithoutModel])
async def read_project_configs_paginated(
    request: Request,
    page_query: PageQuery = Depends(),
    keyword: Optional[str] = None,
    status: Optional[str] = None
):
    """
    获取分页的项目配置列表
    
    Args:
        page: 页码，从1开始
        page_size: 每页数量
        is_deleted: 是否已删除 (0: 未删除, 1: 已删除)
        current_user: 当前用户
        
    Returns:
        包含分页信息的项目配置列表
    """
    current_user = get_current_user_from_state(request)
    page = page_query.page
    size = page_query.size
    try:
        # 计算偏移量
        skip = (page - 1) * size
        
        # 计算配置天数前的时间
        filter_days_str = await system_config_service.get_config("history_record_filter_days")
        filter_days = int(filter_days_str) if filter_days_str and filter_days_str.isdigit() else 30
        thirty_days_ago = datetime.now() - timedelta(days=filter_days)
        
        # 构建基础查询
        base_query = None
        # 根据用户角色添加过滤条件
        if current_user.role.identifier != InsetRole.SUPER_ADMIN:
            base_query = ProjectConfig.filter(
                is_deleted=0, 
                user=current_user.id,
                updated_at__gte=thirty_days_ago
            )
        else:
            base_query = ProjectConfig.filter(
                is_deleted=0,
                updated_at__gte=thirty_days_ago
            )
        
        # 添加状态过滤
        if status:
            base_query = base_query.filter(status=status)
        
        # 添加关键字模糊匹配name字段
        if keyword:
            base_query = base_query.filter(name__icontains=keyword)
        
        base_query = base_query.prefetch_related(
            "user",
            "model",
            "user__role",
            "user__organization"
        )    
        # 获取总记录数
        total = await base_query.count()
        
        # 获取当前页的数据
        configs = await base_query.order_by("-updated_at").offset(skip).limit(size)
        
        result = []
        for config in configs:
            result.append(ProjectConfigResponseWithoutModel.model_validate(config))    
        # 构建分页响应
        paginated_response = {
            "items": [ProjectConfigResponseWithoutModel.model_validate(config_dict) for config_dict in result],
            "total": total,
            "page": page,
            "size": size
        }
        
        return send_page_data(True, paginated_response)
    except Exception as e:
        logger.error(f"获取分页项目配置列表失败: {str(e)}")
        return send_data(False, None, f"获取分页项目配置列表失败: {str(e)}")


@router.post("/upload-outline", response_model=ResponseModel[UploadFileResponse], summary="上传文件（仅支持word、md、txt）")
async def upload_user_file(
    request: Request,
    file: UploadFile = File(..., description="用户上传的文件")
):
    """
    上传文件接口，只允许上传word、md、txt文件。
    - 文件名用时间戳覆盖。
    - 文件存储到本地并写入upload_files表。
    - 返回文件信息。
    """
    current_user = get_current_user_from_state(request)
    # 校验文件类型
    try:
        await validate_file_security(file=file, allowed_mimes=[
            'text/plain',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'application/pdf',
            'application/zip'
        ])
    except Exception as e:
        return send_data(False, None, str(e))
    # 保存文件
    try:
        result = await save_upload_file(file, current_user)
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, str(e))
