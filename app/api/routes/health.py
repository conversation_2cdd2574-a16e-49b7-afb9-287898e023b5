from fastapi import APIRouter

from app.api.schemas.health import HealthResponse
from app.core.config import settings
from app.utils.utils import send_data, ResponseModel

router = APIRouter()

@router.get("", response_model=ResponseModel[HealthResponse], summary="健康检查接口")
async def health_check():
    """
    健康检查接口
    
    返回服务的基本状态信息，用于负载均衡器、监控系统等检查服务是否正常运行。
    """
    try:

        health_data = HealthResponse(
            status="healthy",
            instance_id=settings.APP_INSTANCE_ID,
            uptime="running"
        )
        health_response = ResponseModel(
            success=True,
            code=0 ,
            data=health_data
        )
        return health_response
    except Exception as e:
        # 即使出现异常，也要返回基本的健康状态
        error_health_data = HealthResponse(
            status="unhealthy",
            instance_id=settings.APP_INSTANCE_ID,
            uptime="error"
        )
        health_response = ResponseModel(
            success=False,
            code=503,
            data=error_health_data,
            error="健康检查失败"
        )
        return health_response

