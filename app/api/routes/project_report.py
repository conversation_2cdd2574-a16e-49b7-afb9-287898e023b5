from fastapi import APIRouter, HTTPException, status
from typing import Optional, Callable

from app.api.repository import upload_file
from app.api.repository.project_config import update_estimated_time
from fastapi import APIRouter, Request
from fastapi.responses import StreamingResponse
from datetime import datetime
import os
import asyncio
import json
from app.api.deps import get_current_user_from_state
from app.models.project_configs import ProjectConfig
from app.api.schemas.project_configs import ProjectConfigResponse2, ProjectConfigStatus, GenerateConfig, UpdateEstimatedTimeProcessingMode
from app.services.prompts import generate_outline_prompt, OUTLINE_SYSTEM_PROMPT
from app.services.wallet.interface import AGENT
from app.services.wallet.service import wallet_service
from app.utils.llm_service import stream_llm_and_save
from app.utils.utils import (
    send_data,
    ResponseModel,
    stream_file_content_sse,
    handle_text_before_use,
    # save_text_to_file,
    sanitize_filename,
    ContentStatus,
    remove_markdown_h1_and_text,
    docx_file_to_markdown,
    read_file_content,
    convert_markdown_to_pdf,
    adjust_markdown_heading_levels
)
from app.api.routes.project_configs import get_one_project_config
from app.utils.content_manager import Data
# from app.services.llm_token_service import get_generation_result
from app.models.research import Research, ResearchStatus
from starlette.background import BackgroundTask
from app.api.repository.user_default_model import get_user_model
from app.utils.enum import UseCase
from app.api.repository.project_report import (
    generate_report,
    report_content_manager,
    outline_content_manager,
    download_file
    # generate_images
)
from app.core.config import settings
from app.core.logging import get_logger
from app.models.user_report_usage import UserReportUsage
from app.api.repository.user_report_usage import check_user_usage_limit
from app.api.schemas.role import InsetRole
from app.models.user import User
from app.utils.enum import CallLLMFlag
from app.api.schemas.user import UserResponse
from app.api.repository.upload_file import get_file_content_by_id
from app.utils.enum import ProjectConfigError
from app.api.repository.user import is_user_authed
from app.core.redis_client import get_llm_interaction_instance
from app.utils.enum import ManagerScene
from app.services.text_moderation_service import text_moderation_service

# 获取logger实例
logger = get_logger(__name__)
llm_interaction_instance = get_llm_interaction_instance()

router = APIRouter()

logger.info("ProjectReport - LLM交互管理器已初始化")

# 生成项目大纲接口
@router.post("/{project_id}/generate-outline", response_model=ResponseModel[ProjectConfigResponse2])
async def generate_project_outline(
    project_id: str,
    request: Request
):
    logger.info(f"ProjectReportRoute.generate_project_outline - 开始生成大纲 项目ID:{project_id}")
    
    is_generating = (await llm_interaction_instance.is_active(
        scene=ManagerScene.LAYOUT_GENERATE,
        id=project_id
    )) or (await llm_interaction_instance.is_active(
        scene=ManagerScene.PAPER_GENERATE,
        id=project_id
    ))
    if is_generating:
        logger.warning(f"ProjectReportRoute.generate_project_outline - 项目正在生成中 项目ID:{project_id}")
        return send_data(False, None, "材料已经处于生成中，不能再次生成")
    
    # 检查用户报告使用次数限制
    starttime = datetime.now()
    logger.info(f"ProjectReportRoute.generate_project_outline - 大纲生成开始 项目ID:{project_id} 时间:{starttime.strftime('%Y-%m-%d %H:%M:%S')}")
    current_user = get_current_user_from_state(request)
    user_obj = await User.filter(id=current_user.id, is_deleted=False).prefetch_related('role', 'organization').first()
    if not user_obj:
        return send_data(False, None, "用户不存在")
    try:
        await check_user_usage_limit(user_obj.id)
    except Exception as e:
        return send_data(False, None, str(e))
    # 直接从数据库获取ProjectConfig模型
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user", "model").first()
    if not config_db:
        return send_data(False, None, "项目配置不存在")

    if not await is_user_authed(config_db.user.id, current_user.id):
        return send_data(False, None, "无权访问此项目")
        # return send_data(False, None, f"读取大纲文件失败: {str(e)}")
    if config_db.model is None:
        return send_data(False, None, "请先配置模型")
    firstModel = await get_user_model(
        current_user=current_user,
        use_case=UseCase.PAPER_GENERATE.value
    )
    apiKey = firstModel.api_key
    apiUrl = firstModel.api_url
    model = firstModel.model_name
    is_thinking = firstModel.is_thinking

    # 重新计算预估时间
    try:
        # 如果是已经生成大纲的数据。那么再次生成的时候就
        # 先把历史的大模型请求的缓存数据抹除一下。
        # 这里是防止大纲生成之后，用户没有调用流式获取大纲的接口
        # 直接再次生成大纲。
        if config_db.status == ProjectConfigStatus.OUTLINE_GENERATED:
            await llm_interaction_instance.end_interaction(
                scene=ManagerScene.LAYOUT_GENERATE,
                id=project_id
            )
        logger.info(f"项目 {config_db.id} 预估时间重新计算开始")
        estimated_time = await update_estimated_time(config_db, UpdateEstimatedTimeProcessingMode.GENERATE_OUTLINE)
        config_db.estimated_time = estimated_time
        config_db.updated_at = datetime.now()
        await config_db.save()
        logger.info(
            f"项目 {config_db.id} 预估时间重新计算完成: {config_db.estimated_time}")
    except Exception as e:
        logger.warning(f"项目 {config_db.id} 预估时间重新计算失败: {e}")

    project_folder = f"llm_file/{project_id}"
    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    timestamp = int(datetime.now().timestamp() * 1000)
    file_name = f"outline_{timestamp}.txt"
    if config_db.name:
        file_name = sanitize_filename(
            f"outline_{config_db.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"
    upload_file.write(path=relative_path, content="")
    # 更新项目配置 - 使用数据库模型
    config_db.ai_generated_outline = relative_path  # 存储大纲内容
    config_db.status = ProjectConfigStatus.OUTLINE_GENERATING.value
    await config_db.save()
    # 添加一个空字符串，用于触发流式处理
    await outline_content_manager.add_content(project_id, "thinking", ContentStatus.HEART_BEAT.value)

    demo = ""
    try:
        if config_db.user_add_demo_id:
            file_data = await get_file_content_by_id(config_db.user_add_demo_id)
            file_path = file_data.file_path
            try:
                upload_file.download_file(file_path)
                if file_path.endswith(".doc"):
                    # .doc 文件先转换为 .docx
                    from app.utils.document_parser import convert_doc_to_docx_with_libreoffice
                    converted_docx_path = convert_doc_to_docx_with_libreoffice(file_path)
                    try:
                        demo = docx_file_to_markdown(converted_docx_path)
                    finally:
                        # 清理临时转换的文件
                        if os.path.exists(converted_docx_path):
                            os.unlink(converted_docx_path)
                elif file_path.endswith(".docx"):
                    demo = docx_file_to_markdown(file_path)
                else:
                    demo = read_file_content(file_path)
            finally:
                upload_file.remove_local_file(file_path)
    except Exception as e:
        error_msg = f"{ProjectConfigError.GET_USER_ADD_DEMO_FAIL.value}: {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)
    # demo = adjust_markdown_heading_levels(demo, 2)

    # saas用户预计费
    order_id = None
    try:
        order_id = await wallet_service.pre_charge(current_user.id, AGENT.PAPER.value)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

    # 内容审核 - 审核四个字段
    try:
        # 审核项目名称
        if config_db.name:
            is_name_safe = await text_moderation_service.is_content_safe(
                content=config_db.name
            )
            if not is_name_safe:
                return send_data(False, None, "项目名称包含不当内容，请修改后重试")
        
       
        
        # 审核示例内容
        if demo:
            is_demo_safe = await text_moderation_service.is_content_safe(
                content=demo
            )
            if not is_demo_safe:
                return send_data(False, None, "示例内容包含不当内容，请修改后重试")
        
        # 审核用户自定义提示词
        if config_db.user_add_prompt:
            is_prompt_safe = await text_moderation_service.is_content_safe(
                content=config_db.user_add_prompt
            )
            if not is_prompt_safe:
                return send_data(False, None, "自定义提示词包含不当内容，请修改后重试")
        
        logger.info("内容审核通过，开始生成大纲")
    except Exception as e:
        logger.error(f"内容审核失败: {str(e)}")
        return send_data(False, None, f"内容审核失败: {str(e)}")

    prompt = generate_outline_prompt(
        name=config_db.name,
        count=config_db.word_count_requirement,
        demo=demo,
        user_prompt=config_db.user_add_prompt
    )
    logger.info(f"大纲demo内容:\n\n {demo}")
    logger.info(f"生成大纲的提示词：\n\n {prompt}")
    # 调用LLM生成大纲 - 流式处理并保存到文件
    messages = [
        {"role": "system", "content": OUTLINE_SYSTEM_PROMPT},
        {"role": "user", "content": prompt}
    ]
    async def complete_callback(token_consume: dict, all_content: str):  # 存储大纲内容
        logger.info(f"材料ID：{config_db.id} 材料名：{config_db.name}的大纲消耗token：{token_consume},")
        config_db.status = ProjectConfigStatus.OUTLINE_GENERATED.value
        config_db.outline_generated_time = datetime.now()
        # 生成大纲的时候要把之前的内容清空
        config_db.manual_modified_outline = None
        config_db.manual_modified_outline_time = None
        config_db.ai_generated_report = None
        config_db.report_generation_time = None
        config_db.manual_modified_report_time = None
        config_db.manual_modified_report = None
        upload_file.write(path=relative_path, content=handle_text_before_use(remove_markdown_h1_and_text(
            all_text=all_content,
            title=config_db.name
        )))
        await config_db.save()
        endtime = datetime.now()
        logger.info(
            f"大纲生成结束时间: {endtime.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(endtime - starttime).total_seconds()}秒")

        asyncio.create_task(wallet_service.charge_confirm(current_user.id, order_id, config_db.id))
        logger.info("complete函数要清空数据了")

    async def error_callback(error: str):
        # 处理错误
        logger.error(f"生成大纲时发生错误: {error}")
        await outline_content_manager.add_content(
            project_id, error, ContentStatus.ERROR.value)
        config_db.status = ProjectConfigStatus.OUTLINE_FAILED.value
        await config_db.save()
        asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, config_db.id))

    async def callback(content: str):
        await outline_content_manager.add_content(project_id, content)
    # 创建异步任务并存储
    asyncio.create_task(stream_llm_and_save(
        messages=messages,
        model=model,
        apiKey=apiKey,
        apiUrl=apiUrl,
        user=current_user,
        flag=CallLLMFlag.GENERATE_OUTLINE.value,
        callback=callback,
        complete_callback=complete_callback,
        error_callback=error_callback,
        related_id=config_db.id,
        is_thinking=is_thinking
    ))
    # 将任务实例保存到内容管理器
    # outline_content_manager.add_asyncio(project_id, task)
    await llm_interaction_instance.start_interaction(
        scene=ManagerScene.LAYOUT_GENERATE,
        id=project_id
    )

    return send_data(True, await get_one_project_config(project_id))

# 流式返回大纲内容的接口（SSE）
@router.get("/{project_id}/stream-outline")
async def stream_project_outline(
    project_id: str,
    request: Request
):
    """
    流式返回项目大纲内容（SSE格式）
    """
    # 检查项目是否存在并验证权限
    current_user = get_current_user_from_state(request)
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        logger.error("项目配置不存在")
        return StreamingResponse(
            content=iter(
                [f"data: {json.dumps({'content': '项目配置不存在', 'status': ContentStatus.ERROR.value})}\n\n"]),
            media_type="text/event-stream"
        )

    if not await is_user_authed(config_db.user.id, current_user.id):
        logger.error("无权访问此项目")
        return StreamingResponse(
            content=iter(
                [f"data: {json.dumps({'content': '无权访问此项目', 'status': ContentStatus.ERROR.value})}\n\n"]),
            media_type="text/event-stream"
        )
    if config_db.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
        logger.info("处于生成状态中")
        async def stream_realtime_content():
            read_count = 0
            project_content = await outline_content_manager.get_project_content(project_id)
            
            llm_data = await llm_interaction_instance.is_active(
                scene=ManagerScene.LAYOUT_GENERATE,
                id=project_id
            )

            if not llm_data:
                config_db.status = ProjectConfigStatus.OUTLINE_FAILED.value
                await config_db.save()
                error_msg = "没有正在生成的任务"
                logger.error(error_msg)
                yield f"data: {json.dumps({'content': error_msg, 'status': ContentStatus.ERROR.value})}\n\n"
                # outline_content_manager.add_content(project_id, "thinking", ContentStatus.HEART_BEAT.value)

            # 先发历史数据
            if project_content and project_content.read:
                content = ""
                for item in project_content.read:
                    if item.status == ContentStatus.NORMAL.value:
                        content += item.content
                yield f"data: {json.dumps({'content': content, 'status': ContentStatus.NORMAL.value})}\n\n"
                read_count += len(project_content.read)

            try:
                while True:
                    # 💡 每次循环前检测是否客户端断开连接
                    if await request.is_disconnected():
                        logger.warning("客户端断开连接，停止推送")
                        break

                    # 从内容管理器读取新内容
                    chunk = await outline_content_manager.read_next_chunk(project_id)
                    if chunk:
                        logger.info("读取了一次信息")
                        yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                        read_count += 1
                    else:
                        await asyncio.sleep(0.5)
                        await config_db.refresh_from_db()
                        if config_db.status != ProjectConfigStatus.OUTLINE_GENERATING.value:
                            logger.info(f"配置的状态为：{config_db.status}")
                            break
                        # outline_content_manager.add_content(project_id, "thinking", ContentStatus.HEART_BEAT.value)
                        yield f"data: {json.dumps({'content': 'thinking', 'status': ContentStatus.HEART_BEAT.value})}\n\n"
                logger.info("来到了while循环外面")
                # 发送剩余未读内容
                project_content = await outline_content_manager.get_project_content(project_id)
                if project_content and project_content.unread:
                    content = ""
                    for item in project_content.unread:
                        if item.status == ContentStatus.NORMAL.value:
                            content += item.content
                    yield f"data: {json.dumps({'content': content, 'status': ContentStatus.NORMAL.value})}\n\n"
                    read_count += len(project_content.unread)
                yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
            except Exception as e:
                logger.error(e)
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )
    # 如果不是正在生成状态，则回退到从文件读取
    outline_path = config_db.manual_modified_outline or config_db.ai_generated_outline
    if not outline_path:
        return StreamingResponse(
            content=iter(
                [f"data: {json.dumps({'content': '项目尚未生成大纲', 'status': ContentStatus.ERROR.value})}\n\n"]),
            media_type="text/event-stream"
        )
    # 如果用户没有一直在页面等到流式结束就会导致内存里面的内容不会被清除
    await outline_content_manager.clear_project(project_id)
    await llm_interaction_instance.end_interaction(
        scene=ManagerScene.LAYOUT_GENERATE,
        id=project_id
    )
    upload_file.download_file(outline_path)
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(outline_path),
        media_type="text/event-stream",
        background=BackgroundTask(lambda: upload_file.remove_local_file(outline_path))
    )

# 生成项目报告接口


# @router.post("/{project_id}/generate-report-step", response_model=ResponseModel[ProjectConfigResponse2])
@router.post("/{project_id}/generate-report-step")
async def generate_project_report_step(
    project_id: str,
    request: Request
):
    try:
        is_generating = (await llm_interaction_instance.is_active(
            scene=ManagerScene.LAYOUT_GENERATE,
            id=project_id
        )) or (await llm_interaction_instance.is_active(
            scene=ManagerScene.PAPER_GENERATE,
            id=project_id
        ))
        if is_generating:
            return send_data(False, None, "材料已经处于生成中，不能再次生成")
        
        current_user = get_current_user_from_state(request)

        # 检查用户报告使用次数限制
        user_obj = await User.filter(id=current_user.id, is_deleted=False).prefetch_related('role', 'organization').first()
        if not user_obj:
            return send_data(False, None, "用户不存在")
        try:
            await check_user_usage_limit(user_obj.id)
        except Exception as e:
            return send_data(False, None, str(e))
         # 直接从数据库获取ProjectConfig模型
        config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user", "model").first()
        # config_db = ProjectConfigResponse2.model_validate(result)
        if not config_db:
            return send_data(False, None, "项目配置不存在")
        # logger.error(config_db, 'config_db')
        if not await is_user_authed(config_db.user.id, current_user.id):
            return send_data(False, None, "无权访问此项目")
        if config_db.model is None:
            return send_data(False, None, "请先配置模型")
        
        # 内容审核 - 审核项目相关字段
        try:
            # 审核项目名称
            if config_db.name:
                is_name_safe = await text_moderation_service.is_content_safe(
                    content=config_db.name
                )
                if not is_name_safe:
                    return send_data(False, None, "项目名称包含不当内容，请修改后重试")
            
            # 审核字数要求
            if config_db.word_count_requirement:
                is_count_safe = await text_moderation_service.is_content_safe(
                    content=str(config_db.word_count_requirement)
                )
                if not is_count_safe:
                    return send_data(False, None, "字数要求包含不当内容，请修改后重试")
            
            # 审核用户自定义提示词
            if config_db.user_add_prompt:
                is_prompt_safe = await text_moderation_service.is_content_safe(
                    content=config_db.user_add_prompt
                )
                if not is_prompt_safe:
                    return send_data(False, None, "自定义提示词包含不当内容，请修改后重试")
            
            logger.info("生成报告接口内容审核通过")
        except Exception as e:
            logger.error(f"生成报告接口内容审核失败: {str(e)}")
            return send_data(False, None, f"内容审核失败: {str(e)}")
        
        # 如果是已经生成报告的数据。那么再次生成的时候就
        # 先把历史的大模型请求的缓存数据抹除一下。
        # 这里是防止报告生成之后，用户没有调用流式获取报告的接口
        # 直接再次生成报告。
        if config_db.status == ProjectConfigStatus.REPORT_GENERATED:
            await llm_interaction_instance.end_interaction(
                scene=ManagerScene.PAPER_GENERATE,
                id=project_id
            )
        result = await generate_report(project_id, current_user)
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, str(e))

# 流式返回报告内容的接口（SSE）
@router.get("/{project_id}/stream-report")
async def stream_project_report(
    project_id: str,
    request: Request
):
    """
    流式返回项目报告内容（SSE格式）
    """
    current_user = get_current_user_from_state(request)
    logger.info(f"流式返回项目报告内容（SSE格式），项目ID: {project_id}")
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        return StreamingResponse(
            content=iter(
                [f"data: {json.dumps({'content': '项目配置不存在', 'status': ContentStatus.ERROR.value})}\n\n"]),
            media_type="text/event-stream"
        )

    if not await is_user_authed(config_db.user.id, current_user.id):
        return StreamingResponse(
            content=iter(
                [f"data: {json.dumps({'content': '无权访问此项目', 'status': ContentStatus.ERROR.value})}\n\n"]),
            media_type="text/event-stream"
        )

    # 检查正在生成的内容
    if config_db.status == ProjectConfigStatus.REPORT_GENERATING.value:
        async def stream_realtime_content():
            # 已读取的内容计数
            read_count = 0
            project_content = await report_content_manager.get_project_content(
                project_id)
            llm_data = await llm_interaction_instance.is_active(
                scene=ManagerScene.PAPER_GENERATE,
                id=project_id
            )
            logger.info(llm_data)

            if not llm_data:
                config_db.status = ProjectConfigStatus.REPORT_FAILED.value
                await config_db.save()
                yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR.value})}\n\n"
                return
            if project_content and project_content.read:
                # 先发送已有的内容
                for chunk in project_content.read:
                    logger.info(f"已经读取过的：{chunk.content}")
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                # await asyncio.sleep(0.02)

            # 然后持续检查是否有新内容
            while config_db.status == ProjectConfigStatus.REPORT_GENERATING.value:
                chunk = await report_content_manager.read_next_chunk(project_id)
                if chunk:
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                    read_count += 1
                else:
                    # 暂时没有新内容，等待一会再检查
                    await asyncio.sleep(5)
                    # 重新获取数据库状态，检查是否仍在生成中
                    await config_db.refresh_from_db()
                    yield f"data: {json.dumps({'content': 'thinking', 'status': ContentStatus.HEART_BEAT.value})}\n\n"

            # 获取剩余未读取的内容
            project_content = await report_content_manager.get_project_content(
                project_id)
            if project_content and project_content.unread:
                for chunk in project_content.unread:
                    logger.error('发送剩余未读内容', chunk)
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                    read_count += 1
            logger.info("\n\n\n我要清空本地数据啦！\n\n\n")
            # 生成已完成，发送完成状态
            yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )

    # 如果不是正在生成状态，则回退到从文件读取
    report_path = config_db.manual_modified_report or config_db.ai_generated_report
    if not report_path:
        return StreamingResponse(
            content=iter(
                [f"data: {json.dumps({'content': '项目生成报告失败', 'status': ContentStatus.ERROR.value})}\n\n"]),
            media_type="text/event-stream"
        )
    logger.info("\n\n\n我要清空本地数据啦2！\n\n\n")
    # 如果用户没有一直在页面等到流式结束就会导致内存里面的内容不会被清除
    await report_content_manager.clear_project(project_id)
    await llm_interaction_instance.end_interaction(
        scene=ManagerScene.PAPER_GENERATE,
        id=project_id
    )
    upload_file.download_file(report_path)
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(report_path),
        media_type="text/event-stream",
        background=BackgroundTask(lambda: upload_file.remove_local_file(report_path))
    )

# 下载项目大纲的Word文档格式的接口（基于Pandoc实现）
@router.get("/{project_id}/download-outline")
async def download_outline(
    project_id: str,
    request: Request
):
    current_user = get_current_user_from_state(request)
    try:
        # project = await get_one_project_config(project_id)
        # return convert_markdown_to_pdf(project.ai_generated_report)
        return await download_file(project_id=project_id, current_user=current_user, flag="outline")
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
# 下载项目大纲的Word文档格式的接口（基于Pandoc实现）
@router.get("/{project_id}/download-report")
async def download_report(
    project_id: str,
    request: Request
):
    current_user = get_current_user_from_state(request)
    try:
        return await download_file(project_id=project_id, current_user=current_user, flag="report")
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
# 下载项目大纲的Word文档格式的接口（基于Pandoc实现）
@router.get("/{project_id}/download-pdf")
async def download_report(
    project_id: str,
    flag: Optional[str] = "outline"
):
    try:
        project = await get_one_project_config(project_id)
        file_path = project.manual_modified_outline or project.ai_generated_outline if flag == 'outline' else project.manual_modified_report or project.ai_generated_report
        try:
            upload_file.download_file(file_path)
            return convert_markdown_to_pdf(file_path)
        finally:
            upload_file.remove_local_file(file_path)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
# 下载项目大纲的Word文档格式的接口（基于Pandoc实现）
# @router.get("/images")
# async def generate_img(
#     request: Request
# ):
#     current_user = get_current_user_from_state(request)
#     project = await get_one_project_config(
#         project_id="ff86165a-07d5-4acb-9baa-ae67b578688b"
#     )
#     content = read_file_content(project.ai_generated_report)
    
#     return await generate_images(
#         current_user=current_user,
#         content=content,
#         name=project.name
#     )