import os
import requests # type: ignore
import uuid
from app.api.deps import get_current_user_from_state
from fastapi import APIRouter, Request, Query
from fastapi.responses import JSONResponse

from app.api.repository import upload_file
from app.core.logging import get_logger
from app.models.co_ai_ppt import CoAiPpt
from datetime import datetime, timedelta
import time
from jose import jwt
from app.core.config import settings
from app.models.upload_file import UploadFile  # 新增import

logger = get_logger(__name__)
router = APIRouter()
# 创建不需要认证的路由器用于回调接口
callback_router = APIRouter()

@callback_router.post("/save", summary="OnlyOffice文档保存回调")
async def onlyoffice_callback(request: Request,
                          ppt_id: str = Query(..., description="PPT ID"),
                          user_id: str = Query(..., description="用户ID")
                          ):
    
     # 获取当前用户
    # current_user = get_current_user_from_state(request)
    # logger.info(f"current_user = {current_user}")
    # if current_user.id != user_id:
    #     logger.error(f"用户ID不匹配: {current_user.id} != {user_id}")
    #     return JSONResponse({"error": 1, "message": "用户ID不匹配"}, status_code=403)
    
    logger.info("OnlyOffice文档保存回调")
    data = await request.json()

    logger.info(f"ppt_id = {ppt_id}")
    status_code = data.get('status')
    download_url = data.get('url')
    logger.info(f"OnlyOffice回调数据 = {data}")
    logger.info(f"OnlyOffice回调状态 status = {status_code}")
    logger.info(f"文档下载地址 = {download_url}")
    
    # 状态为 2文档准备好保存）或 6（强制保存）
    if status_code in (2, 6) and download_url:
        try:
            # 验证PPT ID格式
            try:
                uuid.UUID(ppt_id)
            except ValueError:
                logger.error(f"无效的PPT ID格式: {ppt_id}")
                return JSONResponse({"error": 1, "message": "PPT ID格式"}, status_code=400)
            # 查询PPT记录
            ppt_record = await CoAiPpt.filter(id=ppt_id, is_deleted=False).first()
            if not ppt_record:
                logger.error(f"PPT记录不存在或已被删除: {ppt_id}")
                return JSONResponse({"error": 1, "message": "PPT记录不存在或已被删除"}, status_code=404)
            # 获取文档 key 和名称（可选字段）
            file_key = data.get('key', 'default')
            file_name = data.get('title', f"{file_key}.pptx")  # 改为.pptx扩展名
            
            # 使用PPT生成的标准路径结构
            date_str = datetime.now().strftime("%Y%m%d")
            attachments_path = f"attachments/{date_str}"
            os.makedirs(attachments_path, exist_ok=True)
            
            # 生成带时间戳的文件名
            timestamp = int(datetime.now().timestamp() * 1000)
            ppt_filename_with_timestamp = f"{timestamp}_{file_name}"
            
            # 构建相对路径和绝对路径
            relative_ppt_path = f"{attachments_path}/{ppt_filename_with_timestamp}"
            absolute_ppt_path = os.path.join(os.getcwd(), relative_ppt_path)

            # 下载并保存文件
            response = requests.get(download_url)
            if response.status_code == 200:
                content = response.content
                upload_file.write(path=relative_ppt_path, content=content)
                # with open(absolute_ppt_path, 'wb') as f:
                #     f.write(response.content)
                
                # 获取文件大小
                file_size = len(content)

                # === 新增：插入upload_files表 ===
                upload_file_record = await UploadFile.create(
                    file_path=relative_ppt_path,
                    file_name=ppt_filename_with_timestamp,
                    user_id=ppt_record.user_id  # 使用PPT记录中的用户ID
                )
                # ============================

                # 更新数据库中的PPT文件路径和相关信息
                await ppt_record.update_from_dict({
                  "ppt_file_path": relative_ppt_path,
                  "ppt_filename": ppt_filename_with_timestamp,
                  "ppt_file_size": file_size,
                  "upload_file_id": upload_file_record.id,  # 关联新生成的PPT文件记录
                  "status": "completed"  # 更新状态为完成
                })
                await ppt_record.save()
                
                logger.info(f"文件已保存并更新数据库：{relative_ppt_path}")
                logger.info(f"PPT记录已更新，ID: {ppt_id}, 文件大小: {file_size} 字节")
                
                return JSONResponse({"error": 0})
            else:
                logger.error(f"下载失败，状态码: {response.status_code}")
                return JSONResponse({"error": 1, "message": "Failed to download file"}, status_code=500)
        except Exception as e:
            logger.error(f"保存出错: {e}")
            return JSONResponse({"error": 1, "message": str(e)}, status_code=500)
    else:
        logger.info("状态不是2存")
        return JSONResponse({"error": 0})  # 返回 0 表示正常响应 
   
@router.post("/jwt", summary="生成OnlyOffice专用JWT")  
async def onlyoffice_jwt(request: Request):  
    try:  
        data = await request.json()  
        ppt_id = data.get("ppt_id")  
        user_id = data.get("user_id")  
        document = data.get("document")  
        logger.info(f"OnlyOffice JWT 参数ppt_id = {ppt_id}, user_id = {user_id}, document = {document}")
        logger.info(f"OnlyOffice JWT 参数settings.ONLYOFFICE_CALLBACK_BASE_URL = {settings.ONLYOFFICE_CALLBACK_BASE_URL}")
        if not ppt_id or not user_id or not document:  
            return JSONResponse({  
                "success": False,  
                "message": "缺少必要参数: ppt_id, user_id, document"  
            }, status_code=400)  

        payload = {  
            "document": document,    # 关键：用前端传来的  
            "documentType": "presentation",  
            "editorConfig": {  
                "mode": "edit",  
                "callbackUrl":  f"{settings.ONLYOFFICE_CALLBACK_BASE_URL}/api/onlyoffice/save?ppt_id={ppt_id}&user_id={user_id}",  
            },  
            "exp": int(time.time())+2*60*60  
        }  
        token = jwt.encode(payload, settings.SECRET_KEY, algorithm="HS256")  

        return JSONResponse({  
            "success": True,  
            "token": token,  
            "message": "JWT令牌生成成功",  
            "payload_preview": {  
                "document_key": payload["document"]["key"],  
                "exp": payload["exp"]  
            }  
        })  

    except Exception as e:  
        return JSONResponse({  
            "success": False,  
            "message": f"生成JWT令牌失败: {str(e)}"  
        }, status_code=500)