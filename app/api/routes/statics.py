from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Query, HTTPException, status, Request, Depends
from fastapi.responses import RedirectResponse, FileResponse
from app.core.config import settings
import os
from app.api.repository.upload_file import download_file, remove_local_file
from starlette.background import BackgroundTask
from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.core.security import verify_download_token
from app.core.logging import get_logger

logger = get_logger(__name__)
oss_router = APIRouter()

@oss_router.get("/pictures/{filename:path}", include_in_schema=False)
@oss_router.get("/pictures/{filename:path}", include_in_schema=False)
async def serve_pictures_oss_file(filename: str):
  relative_path = f"pictures/{filename}"
  try:
    download_file(relative_path)
    absolute_path = os.path.join(os.getcwd(), relative_path)
    return FileResponse(
      path=absolute_path,
      filename=filename,
      # media_type="image/png",
      # headers={"Content-Disposition": f'attachment; filename="{filename}"'},
      background=BackgroundTask(lambda: remove_local_file(relative_path))
    )
  except Exception as e:
    raise HTTPException(
      status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
      detail=f"获取文件失败: {str(e)}"
    )
  # finally:
  #   remove_local_file(relative_path)
@oss_router.get("/common-files/{filename:path}")
async def serve_common_oss_file(
  filename: str,
  current_user: UserResponse = Depends(get_current_user)
):
  relative_path = f"common-files/{filename}"
  try:
    download_file(relative_path)
    absolute_path = os.path.join(os.getcwd(), relative_path)
    return FileResponse(
      path=absolute_path,
      filename=filename,
      background=BackgroundTask(lambda: remove_local_file(relative_path))
    )
  except Exception as e:
    raise HTTPException(
      status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
      detail=f"获取文件失败: {str(e)}"
    )
  
@oss_router.get("/download/{resource_id}")
async def download_resource(
    resource_id: str,
    path: str = Query(..., description="文件相对路径"),
    token: str = Query(..., description="下载令牌")
):
    """
    通用文件下载接口，支持OSS和本地文件
    """
    # 验证令牌
    token_data = verify_download_token(token)
    if not token_data or token_data.get("ppt_id") != resource_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无效的下载令牌或令牌已过期"
        )
    
    # 下载文件（如果是OSS，会从OSS下载到本地临时目录）
    download_file(path)
    
    # 构建绝对路径
    absolute_path = os.path.join(os.getcwd(), path)
    
    # 获取文件名
    filename = os.path.basename(path)
    logger.info(f"filename: {filename}")
    logger.info(f"文件下载请求: resource_id={resource_id}, path={path}")
    
    # 返回文件下载响应
    return FileResponse(
        path=absolute_path,
        filename=filename,
        background=BackgroundTask(lambda: remove_local_file(path) if settings.FILE_MODE == 'oss' else None)
    )
