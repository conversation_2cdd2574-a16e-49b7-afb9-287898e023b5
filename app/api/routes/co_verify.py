from fastapi import (
  APIRouter,
  HTTPException,
  Query,
  Request,
  UploadFile,
  File,
  Depends,
  status
)
import os
from typing import Optional
from uuid import UUID
from app.api.schemas.co_verify import (
    CoVerifyCreate,
    CoVerifyUpdate,
    CoVerifyResponse
)
from app.api.schemas.user import UserResponse
from app.services.wallet.interface import AGENT
from app.services.wallet.service import wallet_service
from app.utils.utils import (
    PageQuery,
    ResponsePageModel,
    send_page_data,
    send_data,
    ResponseModel,
    validate_file_security,
    read_file_content,
    validate_text_length,
    ALLOW_EXTENSION,
    stream_handle_before_send
)
from app.api.repository import co_verify as co_verify_repo, upload_file
from app.api.deps import get_current_user_from_state
from app.core.logging import get_logger
from app.utils.utils import convert_markdown_to_docx, convert_markdown_to_pdf
from app.utils.enum import CoVerifyStatus, ProjectConfigError
from app.api.repository.upload_file import save_upload_file, delete_upload_file
from app.api.schemas.upload_file import UploadFileResponse
import asyncio
from app.utils.enum import ManagerScene
from app.utils.document_parser import convert_doc_to_docx_with_libreoffice, check_libreoffice_available
from app.services.text_moderation_service import text_moderation_service
from app.services.content_moderation_service import content_moderation_service

logger = get_logger(__name__)
router = APIRouter()

@router.post("", response_model=ResponseModel[CoVerifyResponse])
async def create_co_verify(data: CoVerifyCreate, request: Request):
    """
    创建co验证记录
    """
    order_id = None
    business_id = None
    current_user = None

    async def success_callback():
        await wallet_service.charge_confirm(current_user.id, order_id, business_id)

    async def error_callback():
        await wallet_service.charge_cancel(current_user.id, order_id, business_id)

    try:
        current_user = get_current_user_from_state(request)
        
        # 添加内容审查 - 检查文件内容是否安全
        if data.file_id:
            try:
                # 获取文件记录
                file_record = await upload_file.get_file_content_by_id(data.file_id)
                if not file_record:
                    return send_data(False, None, "文件不存在")
                
                # 使用统一的内容审核服务
                is_safe, error_msg = await content_moderation_service.verify_file_content(
                    file_path=file_record.file_path,
                    description=f"幻觉审查文件 {data.file_id}"
                )
                
                if not is_safe:
                    return send_data(False, None, error_msg)
                    
                logger.info(f"文件 {data.file_id} 的文档内容审核通过，可以继续创建幻觉审查记录")
                        
            except Exception as content_error:
                error_msg = f"文档内容审核失败: {str(content_error)}"
                logger.error(error_msg)
                return send_data(False, None, error_msg)
        
        data.status = CoVerifyStatus.ONGOING.value

        result = await co_verify_repo.create_co_verify(data, current_user)

        try:
            order_id = await wallet_service.pre_charge(current_user.id, AGENT.HALLUCINATION.value)
        except Exception as e:
            return send_data(False, None, f"幻觉审查失败：{e}")
        business_id = result.id
        asyncio.create_task(
            co_verify_repo.handle_hallucination_data(id=result.id, current_user=current_user, success_callback=success_callback, error_callback=error_callback))
        await co_verify_repo.llm_interaction_instance.start_interaction(
            id=str(result.id),
            scene=ManagerScene.HALLUCINATION_REMOVE
        )
        return send_data(True, result)
    except Exception as e:
        error_msg = f"创建co验证记录失败: {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)


@router.get("/{verify_id}", response_model=ResponseModel[CoVerifyResponse])
async def get_co_verify(verify_id: UUID, request: Request):
    """
    根据ID获取co验证记录
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await co_verify_repo.get_co_verify_by_id(verify_id, current_user)
        if result.status == CoVerifyStatus.SUCCESS.value and result.ai_report:
            try:
                upload_file.download_file(result.ai_report)
                result.ai_report = stream_handle_before_send(read_file_content(result.ai_report))
            finally:
                upload_file.remove_local_file(result.ai_report)
        return send_data(True, result)
    except Exception as e:
        error_msg = str(e)
        logger.error(error_msg)
        return send_data(False, None, error_msg)


@router.get("/list/paginated", response_model=ResponsePageModel[CoVerifyResponse])
async def get_co_verify_list(
    request: Request,
    page_query: PageQuery = Depends(),
    keyword: Optional[str] = Query(default=None, description="搜索关键词"),
):
    """
    分页获取co验证列表（只显示近配置天数的记录）
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await co_verify_repo.get_co_verify_list(page_query, current_user, keyword)
        return send_page_data(True, result)
    except Exception as e:
        error_msg = f"获取co验证列表失败: {str(e)}"
        logger.error(error_msg)
        return send_page_data(False, None, error_msg)


@router.put("/{verify_id}", response_model=ResponseModel[CoVerifyResponse])
async def update_co_verify(verify_id: UUID, data: CoVerifyUpdate, request: Request):
    """ 
    更新co验证记录
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await co_verify_repo.update_co_verify(verify_id, data, current_user)
        return send_data(True, result)
    except Exception as e:
        error_msg = f"更新co验证记录失败: {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)


@router.delete("/{verify_id}", response_model=ResponseModel[bool])
async def delete_co_verify(verify_id: UUID, request: Request):
    """
    删除co验证记录
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await co_verify_repo.delete_co_verify(verify_id, current_user)
        return send_data(True, result)
    except Exception as e:
        error_msg = f"删除co验证记录失败: {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)


@router.get("/{id}/download", summary="下载幻觉审查报告")
async def download_report(
    id: str,
    request: Request,
    flag: str = Query(..., description="pdf/doc")
):
    current_user = get_current_user_from_state(request)
    try:
        result = await co_verify_repo.get_co_verify_by_id(id, current_user)
        if not result.ai_report or result.status != CoVerifyStatus.SUCCESS.value:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="报告不存在"
            )
        try:
            if flag == 'pdf':
                upload_file.download_file(result.ai_report)
                return convert_markdown_to_pdf(result.ai_report)
            else:
                logger.info("下载DOCX的幻觉审查")
                upload_file.download_file(result.ai_report)
                return convert_markdown_to_docx(result.ai_report)
        finally:
            upload_file.remove_local_file(result.ai_report)
    except Exception as e:
        logger.error(f"下载幻觉审查报告失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="下载报告失败"
        )


@router.post("/upload-paper", response_model=ResponseModel[UploadFileResponse], summary="上传文件（仅支持word、md、txt）")
async def upload_user_file(
    request: Request,
    file: UploadFile = File(..., description="用户上传的文件")
):
    """
    上传文件接口，只允许上传word、md、txt文件。
    - 文件名用时间戳覆盖。
    - 文件存储到本地并写入upload_files表。
    - 返回文件信息。
    """
    # 校验文件类型
    current_user = get_current_user_from_state(request)
    ext = os.path.splitext(file.filename)[1].lower()
    if ext not in ALLOW_EXTENSION:
        return send_data(False, None, ProjectConfigError.UPLOAD_FILE_ERROR.value)
    # 保存文件
    try:
        
        # 在文件转换后进行安全验证和文本长度验证
        logger.info(f"validate_file_security: {file}")
        await validate_file_security(file)
        logger.info(f"validate_text_length: {file}")
        await validate_text_length(file)
        
        result = await save_upload_file(file, current_user)
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, str(e))


@router.post("/project/{project_id}", response_model=ResponseModel[CoVerifyResponse], summary="为论文创建幻觉审查")
async def create_co_verify_from_project(
    project_id: str,
    request: Request,
    skip_compare: Optional[bool] = Query(default=False, description="是否跳过内容比较检测")
):
    """
    根据项目ID创建幻觉审查记录并开始审查
    
    - 自动选择项目中的最新内容进行幻觉审查
    - 优先级：手动修改的报告 > AI生成的报告 > 手动修改的大纲 > AI生成的大纲
    - 如果已存在相同内容的审查记录，将返回现有记录
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await co_verify_repo.create_co_verify_from_project(
            project_id=project_id,
            current_user=current_user,
            skip_compare=skip_compare
        )
        return send_data(True, result)
    except Exception as e:
        error_msg = str(e)
        logger.error(error_msg)
        return send_data(False, None, error_msg)
