from fastapi import APIRouter, Depends, Query
from typing import List, Optional
from app.api.schemas.user import UserResponse
from pydantic import UUID4
from app.api.repository.organization_model import (
  get_organization_model,
  create_organization_model,
  change_default_use,
  get_default_use
)
from app.models.model_config import ModelConfig
from app.api.schemas.organization_model import OrganizationModelCreate, OrganizationModelWithUse, OrganizationDefaultUse, OrganizationDefaultUseResponse
from app.api.deps import (
  auth_super_admin_depend,
  auth_admin_or_super_admin,
  auth_org_admin_depend
)
from app.api.schemas.role import InsetRole
from app.utils.utils import send_data, ResponseModel
from app.utils.enum import (
  OrganizationModelError,
  OrganizationError,
  ModelConfigError
)
from app.core.logging import get_logger
from app.models.organizations import Organizations

logger = get_logger(__name__)

router = APIRouter()

@router.get("/detail", response_model=ResponseModel[List[OrganizationModelWithUse]], summary="获取机构的所有可用模型")
async def api_get_organization_model(
    organization_id: Optional[UUID4] = Query(default=None, description="机构的ID（超级管理员需要传）"),
    current_user: UserResponse = Depends(auth_admin_or_super_admin)
):
    """
    获取机构的所有可用模型
    """
    try:
      orgId = ""
      if current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
        if not organization_id:
          return send_data(False, None, OrganizationModelError.ORGANIZATION_ID_EMPTY.value)
        orgId = organization_id
      else:
        orgId = current_user.organization.id
      result = await get_organization_model(orgId)
      return send_data(True, result)
    except Exception as e:
       error_msg = f"{OrganizationModelError.LIST_GET_ERROR.value}{str(e)}"
       logger.error(error_msg)
       return send_data(False, None, error_msg)

@router.post("/arrange-model", response_model=ResponseModel[bool], summary="为机构批量添加模型")
async def api_create_organization_model(
    data: OrganizationModelCreate,
    current_user: UserResponse = Depends(auth_super_admin_depend)
):
    """
    为机构批量添加模型
    """
    try:
      organization = await Organizations.filter(
        id=data.organization_id,
        is_deleted=False
      ).first()
      if not organization:
        error_msg = f"{OrganizationError.NO_RECORD.value}：{data.organization_id}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)
      for model_id in data.list_model:
        model_data = await ModelConfig.filter(id=model_id).first()
        if not model_data:
           error_msg = f"{ModelConfigError.NOT_RECORD.value}： {model_id}"
           logger.error(error_msg)
           return send_data(False, None, error_msg)
      result = await create_organization_model(data)
      return send_data(True, result)
    except Exception as e:
       error_msg = f"{OrganizationModelError.CREATED_MODEL_TO_ORG_ERROR.value}: {str(e)}"
       logger.error(error_msg)
       return send_data(False, None, error_msg)

@router.post("/default-use", response_model=ResponseModel[bool], summary="设置机构各场景默认模型")
async def api_change_default_use(
    data: OrganizationDefaultUse,
    current_user: UserResponse = Depends(auth_admin_or_super_admin)
):
    """
    设置机构各场景默认模型
    """
    try:
        for model in data.list:
          model_data = await ModelConfig.filter(
            is_deleted=False,
            id=model.model_id
          )
          if not model_data:
            error_msg = f"{ModelConfigError.NOT_RECORD.value}: {model.model_id}"
            logger.error(error_msg)
            return send_data(False, None, error_msg)
        orgId = ""
        if current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
          if not data.organization_id:
            return send_data(False, None, OrganizationModelError.ORGANIZATION_ID_EMPTY.value)
          orgId = data.organization_id
        else:
          orgId = current_user.organization.id
        org_exist = await Organizations.filter(
          id=orgId,
          is_deleted=False
        )
        if not org_exist:
          error_msg = OrganizationError.NO_RECORD.value
          logger.error(error_msg)
          return send_data(False, None, error_msg)
        result = await change_default_use(data, orgId)
        return send_data(True, result)
    except Exception as e:
        error_msg = f"{OrganizationModelError.CHANGE_DEFAULT_USE_ERROR.value}: {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)

@router.get("/default-use", response_model=ResponseModel[List[OrganizationDefaultUseResponse]], summary="获取机构所有场景的默认模型")
async def api_get_default_use(
  organization_id: Optional[UUID4] = Query(default=None, description="机构ID（超级管理员需要传）"),
  current_user: UserResponse = Depends(auth_admin_or_super_admin)
):
    """
    获取机构所有场景的默认模型
    """
    try:
        orgId = ""
        if current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
            if not organization_id:
                return send_data(False, None, OrganizationModelError.ORGANIZATION_ID_EMPTY.value)
            orgId = organization_id
        else:
            orgId = current_user.organization.id
        result = await get_default_use(orgId)
        return send_data(True, result)
    except Exception as e:
        error_msg = f"{OrganizationModelError.LIST_GET_ERROR.value}: {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)

