from typing import List
from uuid import UUID
from fastapi import APIRouter, HTTPException, Depends, status, Query, Request
from tortoise.functions import Sum
from app.api.schemas.user_report_usage import (
    UserReportUsageCreate,
    UserReportUsageResponse
)
from app.models.user_report_usage import UserReportUsage
from app.models.user import User
from app.models.organizations import Organizations
from app.utils.utils import send_data, ResponseModel, PageQuery, send_page_data, ResponsePageModel
from app.api.schemas.user import UserResponse
from app.api.schemas.role import InsetRole
from app.core.logging import get_logger
from app.api.repository.organizations import calculate_organization_left_usable_count
from app.api.repository.user_report_usage import create_user_report_usage
from app.utils.mobile_crypto import encrypt_data

logger = get_logger(__name__)


router = APIRouter(
    prefix="/user-report-usages",
    tags=["用户报告使用次数"],
    responses={404: {"description": "Not found"}},
)


@router.post("/", response_model=ResponseModel[UserReportUsageResponse])
async def create_user_usage(
    user_report_usage: UserReportUsageCreate
):
    """创建用户报告使用次数记录"""
    try:
        # 检查用户是否存在
        user = await User.filter(id=user_report_usage.user_id, is_deleted=False).first()
        if not user:
            return send_data(False, None, "用户不存在")
        
        # 检查是否已经存在记录
        existing = await UserReportUsage.filter(user_id_id=user_report_usage.user_id, is_deleted=False).first()
        if existing:
            # 更新现有记录
            existing.max_allowed_count = user_report_usage.max_allowed_count
            await existing.save()
            return send_data(True, UserReportUsageResponse.model_validate(existing), "更新成功")
        
        # 创建新记录
        new_usage = await UserReportUsage.create(
            user_id_id=user_report_usage.user_id,
            max_allowed_count=user_report_usage.max_allowed_count
        )
        return send_data(True, UserReportUsageResponse.model_validate(new_usage), "创建成功")
    except Exception as e:
        logger.error(f"创建用户报告使用次数记录失败: {str(e)}")
        return send_data(False, None, f"创建失败: {str(e)}")


@router.get("/by-user/{user_id}", response_model=ResponseModel[UserReportUsageResponse])
async def get_user_report_usage(
    user_id: UUID
):
    """获取用户报告使用次数"""
    try:
        # 验证用户存在并预加载关联数据
        user_result = await User.filter(id=user_id, is_deleted=False).prefetch_related('role', 'organization').first()
        if not user_result:
            return send_data(
                False,
                None,
                "用户不存在"
            )
        user = UserResponse.model_validate(user_result, from_attributes=True)
        
        # 加密用户手机号
        if user_result.mobile:
            user.mobile = encrypt_data(user_result.mobile)
            
        user_report_usage = await UserReportUsage.filter(user_id_id=user_id, is_deleted=False).first()
        if not user_report_usage:
            return send_data(
                False,
                None,
                "用户报告使用次数记录不存在"
            )
        
        # 如果是机构管理员，需要计算机构剩余未分配的次数
        if user.role.identifier == InsetRole.ADMIN:
            logger.info(f"机构管理员 {user.username} 查询次数，开始计算机构剩余未分配次数")
            
            # 获取机构总次数
            organization = await Organizations.filter(id=user.organization.id, is_deleted=False).first()
            if not organization:
                return send_data(False, None, "用户所属机构不存在")
            
            # 计算剩余未分配次数
            remaining_unallocated = await calculate_organization_left_usable_count(
               organization_id=user.organization.id
            )
            logger.info(f"机构剩余未分配次数: {remaining_unallocated}")
            
            # 为机构管理员返回特殊的响应，显示剩余未分配次数
            user_report_data = {
                "id": str(user_report_usage.id),
                "user_id": str(user_report_usage.user_id_id),
                "used_count": user_report_usage.used_count,
                "max_allowed_count": remaining_unallocated,  # 显示剩余未分配次数
                "created_at": user_report_usage.created_at,
                "updated_at": user_report_usage.updated_at,
                "is_deleted": user_report_usage.is_deleted
            }
            
            logger.info(f"机构管理员 {user.username} 次数查询完成，返回剩余未分配次数: {remaining_unallocated}")
            
        else:
            # 普通用户直接返回自己的使用次数记录
            user_report_data = {
                "id": str(user_report_usage.id),
                "user_id": str(user_report_usage.user_id_id),
                "used_count": user_report_usage.used_count,
                "max_allowed_count": user_report_usage.max_allowed_count,
                "created_at": user_report_usage.created_at,
                "updated_at": user_report_usage.updated_at,
                "is_deleted": user_report_usage.is_deleted
            }
            
            logger.info(f"普通用户 {user.username} 次数查询完成，已用: {user_report_usage.used_count}, 最大允许: {user_report_usage.max_allowed_count}")
        
        result = UserReportUsageResponse.model_validate(user_report_data)
        return send_data(
            True,
            result,
            ""
        )
    except Exception as e:
        logger.error(f"获取用户报告使用次数失败: {str(e)}")
        return send_data(
            False,
            None,
            f"获取用户报告使用次数失败: {str(e)}"
        )

# 注释掉的代码已经不再使用，可以完全移除
# 或者保留但修改类名为正确的类名
# @router.patch("/{user_id}", response_model=ResponseModel[UserReportUsageResponse])
# async def update_user_report_usage(
#     user_id: UUID,
#     user_report_usage: UserReportUsageCreate
# ):
#     """更新用户报告使用次数"""
#     # 查找记录
#     db_user_report_usage = await UserReportUsage.filter(user_id=user_id, is_deleted=False).first()
#     if not db_user_report_usage:
#         return send_data(
#           False,
#           None,
#           "用户报告使用次数记录不存在"
#         )
#     
#     # 更新记录
#     update_data = user_report_usage.model_dump(exclude_unset=True)
#     db_user_report_usage.max_allowed_count = update_data.get("max_allowed_count", db_user_report_usage.max_allowed_count)
#     await db_user_report_usage.save()
#     
#     result = UserReportUsageResponse.model_validate(db_user_report_usage)
#     return send_data(
#         True,
#         result,
#         ""
#     ) 