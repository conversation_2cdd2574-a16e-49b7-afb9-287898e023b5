from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import J<PERSON>NResponse
from app.api.deps import get_current_user_from_state
from app.api.schemas.system_config import (
    SystemConfigCreate,
    SystemConfigUpdate,
    SystemConfigResponse,
    SystemConfigSyncResponse
)
from app.services.system_config_service import system_config_service
from app.utils.utils import send_data, ResponseModel, send_page_data, ResponsePageModel, PageQuery
from app.core.logging import get_logger
from app.core.exceptions import (
    SystemConfigError, ConfigValidationError, ConfigPermissionError,
    ConfigNotFoundError, ConfigSyncError, create_error_response, create_success_response
)

logger = get_logger(__name__)
router = APIRouter()
# 创建不需要认证的路由器用于普通用户访问的接口
public_router = APIRouter()


@public_router.get("/config/{key}", response_model=ResponseModel[str])
async def get_system_config(key: str):
    """获取单个系统配置值（普通用户可访问）"""
    try:
        if not key or not key.strip():
            raise ConfigValidationError("配置键不能为空", "key", key)
        
        value = await system_config_service.get_config(key)
        if value is None:
            raise ConfigNotFoundError(key)
        return send_data(True, value)
    except (ConfigValidationError, ConfigNotFoundError):
        raise
    except Exception as e:
        logger.error(f"获取系统配置 {key} 时出错: {e}")
        raise SystemConfigError(f"获取系统配置失败: {str(e)}")


@router.post("/sync", response_model=ResponseModel[SystemConfigSyncResponse])
async def sync_system_configs():
    """同步系统默认配置"""
    try:
        result = await system_config_service.sync_default_configs()
        return send_data(True, result, "系统配置同步完成")
    except Exception as e:
        logger.error(f"同步系统配置时出错: {e}")
        raise ConfigSyncError(f"同步系统配置失败: {str(e)}", {"error": str(e)})



@router.get("/list", response_model=ResponsePageModel[SystemConfigResponse])
async def get_system_configs(
    page_query: PageQuery = Depends(),
    category: Optional[str] = Query(default=None, description="配置分类"),
    keyword: Optional[str] = Query(default=None, description="搜索关键词（支持key、description）")
):
    """获取系统配置列表（分页）"""
    try:
        page = page_query.page
        size = page_query.size
        
        # 获取所有配置
        if category:
            configs = await system_config_service.get_configs_by_category(category)
        else:
            configs = await system_config_service.get_all_configs()
        
        # 关键词过滤
        if keyword:
            filtered_configs = []
            for config in configs:
                if (keyword.lower() in config["key"].lower() or 
                    keyword.lower() in config.get("description", "").lower()):
                    filtered_configs.append(config)
            configs = filtered_configs
        
        # 计算总数
        total = len(configs)
        
        # 分页处理
        start_index = (page - 1) * size
        end_index = start_index + size
        paginated_configs = configs[start_index:end_index]
        
        # 构建分页响应
        paginated_response = {
            "items": paginated_configs,
            "total": total,
            "page": page,
            "size": size
        }
        
        return send_page_data(True, paginated_response)
    except Exception as e:
        logger.error(f"获取系统配置列表时出错: {e}")
        return send_page_data(False, {
            "items": [],
            "total": 0,
            "page": page_query.page,
            "size": page_query.size
        }, f"获取系统配置列表失败: {str(e)}")


@router.get("/detail/{config_id}", response_model=ResponseModel[SystemConfigResponse])
async def get_system_config_detail(config_id: str):
    """根据ID获取系统配置详情"""
    try:
        if not config_id or not config_id.strip():
            raise ConfigValidationError("配置ID不能为空", "config_id", config_id)
        
        config_detail = await system_config_service.get_config_by_id(config_id)
        if config_detail is None:
            raise ConfigNotFoundError(f"配置ID: {config_id}")
        
        return send_data(True, config_detail, "获取配置详情成功")
    except (ConfigValidationError, ConfigNotFoundError):
        raise
    except Exception as e:
        logger.error(f"获取系统配置详情 {config_id} 时出错: {e}")
        raise SystemConfigError(f"获取系统配置详情失败: {str(e)}")


# @router.post("/", response_model=ResponseModel[SystemConfigResponse])
# async def create_system_config(
#     config: SystemConfigCreate
# ):
#     """创建系统配置"""
#     try:
#         # 验证key不能为空
#         if not config.key or not config.key.strip():
#             raise ConfigValidationError("配置键不能为空", "key", config.key)
        
#         # 验证配置值长度
#         if config.value and len(config.value) > 1000:
#             raise ConfigValidationError("配置值长度不能超过1000字符", "value", config.value)
        
#         # 检查key是否已存在
#         if await system_config_service.config_exists(config.key):
#             # 如果key已存在，返回错误信息
#             return send_data(False, None, "配置键已存在，无法创建重复配置")
            
#         # 创建新配置 - value和default_value分开存储，order_no自动生成
#         success = await system_config_service.set_config(
#             key=config.key,
#             value=config.value,
#             default_value=config.default_value,
#             category=config.category,
#             description=config.description,
#             order_no=config.order_no
#         )
        
#         if not success:
#             raise SystemConfigError("创建系统配置失败")
        
#         # 获取创建的配置
#         configs = await system_config_service.get_all_configs() 
#         created_config = next((c for c in configs if c["key"] == config.key), None)
        
#         if not created_config:
#             raise SystemConfigError("创建系统配置失败")
        
#         return send_data(True, created_config, "系统配置创建成功")
#     except (ConfigValidationError, SystemConfigError):
#         raise
#     except Exception as e:
#         logger.error(f"创建系统配置时出错: {e}")
#         raise SystemConfigError(f"创建系统配置失败: {str(e)}")


@router.put("/detail/{config_id}", response_model=ResponseModel[SystemConfigResponse])
async def update_system_config_by_id(
    config_id: str,
    config: SystemConfigUpdate
):
    """根据ID更新系统配置"""
    try:
        # 验证config_id不能为空
        if not config_id or not config_id.strip():
            raise ConfigValidationError("配置ID不能为空", "config_id", config_id)
        
        # 先检查配置是否存在
        existing_config = await system_config_service.get_config_by_id(config_id)
        if existing_config is None:
            raise ConfigNotFoundError(f"配置ID: {config_id}")
        
        # 验证更新值
        if config.value and len(config.value) > 1000:
            raise ConfigValidationError("配置值长度不能超过1000字符", "value", config.value)
        
        # 更新配置 - value和default_value分开处理，order_no也支持更新
        success = await system_config_service.update_config_by_id(
            config_id=config_id,
            value=config.value,
            default_value=config.default_value,
            category=config.category,
            description=config.description,
            order_no=config.order_no
        )
        
        if not success:
            raise SystemConfigError("更新系统配置失败")
        
        # 获取更新后的配置
        updated_config = await system_config_service.get_config_by_id(config_id)
        
        if not updated_config:
            raise SystemConfigError("更新系统配置失败")
        
        return send_data(True, updated_config, "系统配置更新成功")
    except (ConfigValidationError, ConfigNotFoundError, SystemConfigError):
        raise
    except Exception as e:
        logger.error(f"更新系统配置 {config_id} 时出错: {e}")
        raise SystemConfigError(f"更新系统配置失败: {str(e)}")


# @router.delete("/detail/{config_id}", response_model=ResponseModel[bool])
# async def delete_system_config_by_id(config_id: str):
#     """根据ID删除系统配置"""
#     try:
#         # 验证config_id不能为空
#         if not config_id or not config_id.strip():
#             raise ConfigValidationError("配置ID不能为空", "config_id", config_id)
        
#         success = await system_config_service.delete_config_by_id(config_id)
#         if not success:
#             raise ConfigNotFoundError(f"配置ID: {config_id}")
#         return send_data(True, True, "系统配置删除成功")
#     except (ConfigValidationError, ConfigNotFoundError):
#         raise
#     except Exception as e:
#         logger.error(f"删除系统配置 {config_id} 时出错: {e}")
#         raise SystemConfigError(f"删除系统配置失败: {str(e)}")


@router.post("/cache/clear", response_model=ResponseModel[bool])
async def clear_system_config_cache():
    """清除系统配置缓存"""
    try:
        await system_config_service.clear_cache()
        return send_data(True, True, "系统配置缓存清除成功")
    except Exception as e:
        logger.error(f"清除系统配置缓存时出错: {e}")
        return send_data(False, None, f"清除系统配置缓存失败: {str(e)}")


@router.post("/locks/check", response_model=ResponseModel[dict])
async def check_and_clear_locks():
    """检查并清理Redis中的死锁"""
    try:
        from app.utils.system_config_helper import config_helper
        await config_helper.check_and_clear_locks()
        return send_data(True, {"message": "锁检查完成"}, "锁检查完成")
    except Exception as e:
        logger.error(f"检查锁时出错: {e}")
        return send_data(False, None, f"检查锁失败: {str(e)}")


@router.post("/locks/force-release/{key}", response_model=ResponseModel[bool])
async def force_release_config_lock(key: str):
    """强制释放指定配置的锁"""
    try:
        from app.utils.system_config_helper import config_helper
        await config_helper.force_release_config_lock(key)
        return send_data(True, True, f"已强制释放配置锁: {key}")
    except Exception as e:
        logger.error(f"强制释放配置锁时出错: {e}")
        return send_data(False, None, f"强制释放配置锁失败: {str(e)}") 