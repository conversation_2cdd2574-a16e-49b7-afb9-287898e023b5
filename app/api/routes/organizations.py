from fastapi import APIRouter, Query, status, Depends
from datetime import datetime
from typing import List
from uuid import UUID
from app.api.schemas.organizations import (
    OrganizationCreate, 
    OrganizationUpdate, 
    OrganizationResponse, 
    OrganizationWithUserResponse,
    UseCaseResponse
)
from app.models.organizations import Organizations
from app.utils.enum import UseCase
from app.utils.utils import (
    send_data,
    ResponseModel,
    send_page_data,
    PageQuery,
    ResponsePageModel
)
from tortoise.expressions import Q
from app.api.repository.organizations import create_org_and_admin
# 使用新的手机号加密解密工具
from app.utils.mobile_crypto import encrypt_data, with_mobile_decryption, handle_mobile_response
from app.core.logging import get_logger
from app.api.repository.organization_model import get_default_use
from pydantic import UUID4

logger = get_logger(__name__)
router = APIRouter()

@router.post("", response_model=ResponseModel[OrganizationWithUserResponse], status_code=status.HTTP_201_CREATED)
@with_mobile_decryption(field_name="contact_phone")  # 使用装饰器自动解密联系电话
async def create_organization(
    organization: OrganizationCreate,
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    创建新机构
    """
    # 检查机构代码是否已存在（排除已删除的）
    existing = await Organizations.filter(code=organization.code).first()
    if existing:
        return send_data(False, None, f"机构代码 '{organization.code}' 已被使用")
    
    try:
        # 联系电话已通过装饰器解密，无需手动解密
        result = await create_org_and_admin(organization)
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"创建机构失败: {str(e)}")

@router.get("/paginated", response_model=ResponsePageModel[OrganizationResponse], summary="分页获取机构列表")
async def list_page_organizations(
    query: PageQuery = Depends(),
    keyword: str = Query(default=None, description="搜索关键词"),
    contain_deactivated: bool = Query(default=True, description="是否包含已停用机构"),
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    分页获取机构
    """
    try:
        # 查询所有未删除的机构，按修改时间降序排序
        base_query = Organizations.filter(is_deleted=False)
        if keyword:
            queries = Q()
            for field in ["name", "code", "contact_person", "type"]:
                queries |= Q(**{f"{field}__icontains": keyword})
            base_query = base_query.filter(queries)
        if not contain_deactivated:
            base_query = base_query.filter(is_active=True)
        total = await base_query.count()
        orgs = await base_query.order_by("-updated_at").offset(query.page-1).limit(query.size)
        # 返回结果
        org_list = []
        for org in orgs:
            org_response = OrganizationResponse.model_validate(org)
            # 加密联系电话
            if org.contact_phone:
                org_response.contact_phone = encrypt_data(org.contact_phone)
            org_list.append(org_response)
            
        return send_page_data(True, {
            "items": org_list,
            "total": total,
            "page": query.page,
            "size": query.size
        })
    except Exception as e:
        logger.error(f"获取机构列表失败: {str(e)}")
        return send_page_data(False, {
            "items": [],
            "total": 0,
            "page": query.page,
            "size": query.size
        }, f"获取机构列表失败: {str(e)}")


@router.get("", response_model=ResponseModel[list[OrganizationResponse]])
async def list_organizations(
    contain_deactivated: bool = Query(default=True, description="是否包含已停用机构"),
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    列出所有未删除的机构
    """
    try:
        # 查询所有未删除的机构，按修改时间降序排序
        orgs = await Organizations.filter(is_deleted=False).order_by("-updated_at") if contain_deactivated else await Organizations.filter(is_deleted=False, is_active=True).order_by("-updated_at")
        
        # 返回结果
        org_list = []
        for org in orgs:
            org_response = OrganizationResponse.model_validate(org)
            # 加密联系电话
            if org.contact_phone:
                org_response.contact_phone = encrypt_data(org.contact_phone)
            org_list.append(org_response)
        return send_data(True, org_list)
    except Exception as e:
        return send_data(False, None, f"获取机构列表失败: {str(e)}")
@router.get("/use-case/list", response_model=ResponseModel[List[UseCaseResponse]], summary="获取所有UseCase列表")
async def get_use_cases(
    org_id: UUID4 = Query(description="机构的ID"),
):
    """
    获取所有场景的默认模型
    """
    try:
        default_model = await get_default_use(org_id)
        use_cases = []
        for use_case in UseCase:
            if (use_case.startswith("INSIGHT")):
                continue
            # 根据UseCase的值生成对应的名称和描述
            name_mapping = {
                "PROJECT_CONFIG_NEED": "项目配置需求",
                "PAPER_GENERATE": "高效写作",
                "HANDLE_TEXT": "文本处理",
                "INSIGHT_MIND_MAP": "洞察思维导图",
                "INSIGHT_GENERATE": "洞察生成",
                "INSIGHT_HANDLE_TEXT": "洞察文本处理",
                "AI_TRACES_REMOVE": "降低AI",
                "HALLUCINATION": "幻觉审查",
                "WEB_PAGE_USEFUL": "网页内容有用性判断",
                "AI_PPT_GENERATE": "PPT生成"
            }
            
            description_mapping = {
                "PROJECT_CONFIG_NEED": "项目配置需要用到的模型",
                "PAPER_GENERATE": "生成大纲、生成文章的默认模型",
                "HANDLE_TEXT": "扩写、续写、缩写、润色",
                "INSIGHT_MIND_MAP": "脑图、重点服务的默认模型",
                "INSIGHT_GENERATE": "生成灵感使用的模型",
                "INSIGHT_HANDLE_TEXT": "灵感里面的扩写、续写、缩写、润色、翻译服务的默认模型",
                "AI_TRACES_REMOVE": "去AI痕迹",
                "HALLUCINATION": "幻觉审查",
                "WEB_PAGE_USEFUL": "判断网页内容有用性",
                "AI_PPT_GENERATE": "PPT生成"
            }
            case_model = next((u for u in default_model if u.default_way.value == use_case.value), None)
            
            use_case_response = UseCaseResponse(
                value=use_case.value,
                is_thinking=case_model.is_thinking if case_model else False,
                model_id=case_model.model.id if case_model else None,
                label=name_mapping.get(use_case.value, use_case.value),
                description=description_mapping.get(use_case.value, "")
            )
            use_cases.append(use_case_response)
        
        return send_data(True, use_cases)
    except Exception as e:
        logger.error(f"获取UseCase列表失败: {str(e)}")
        return send_data(False, None, f"获取UseCase列表失败: {str(e)}")


@router.get("/{org_id}", response_model=ResponseModel[OrganizationResponse])
async def get_organization(
    org_id: UUID,
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    获取机构详情
    """
    try:
        # 只查询未删除的机构
        org = await Organizations.filter(id=org_id, is_deleted=False).first()
        
        if not org:
            return send_data(False, None, "机构不存在或已被删除")
        
        # 构建详细响应
        response = OrganizationResponse.model_validate(org, from_attributes=True)
        
        # 加密联系电话
        if org.contact_phone:
            response.contact_phone = encrypt_data(org.contact_phone)
        
        return send_data(True, response)
    except Exception as e:
        return send_data(False, None, f"获取机构详情失败: {str(e)}")

@router.put("/{org_id}", response_model=ResponseModel[OrganizationResponse])
@with_mobile_decryption(field_name="contact_phone")  # 使用装饰器自动解密联系电话
async def update_organization(
    org_id: UUID,
    org_update: OrganizationUpdate,
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    更新机构信息
    """
    try:
        # 查找机构
        org = await Organizations.filter(id=org_id, is_deleted=False).first()
        if not org:
            return send_data(False, None, "机构不存在或已被删除")
        
        # 联系电话已通过装饰器解密，无需手动解密
        
        # 更新机构信息
        update_data = org_update.model_dump(exclude_unset=True)
        update_data["updated_at"] = datetime.now()
        
        # 更新数据库
        await Organizations.filter(id=org_id).update(**update_data)
        
        # 获取更新后的机构
        updated_org = await Organizations.filter(id=org_id).first()
        response = OrganizationResponse.model_validate(updated_org, from_attributes=True)
        
        # 加密联系电话
        if updated_org.contact_phone:
            response.contact_phone = encrypt_data(updated_org.contact_phone)
        
        return send_data(True, response)
    except Exception as e:
        return send_data(False, None, f"更新机构失败: {str(e)}")


@router.delete("/{org_id}", response_model=ResponseModel[OrganizationResponse])
async def delete_organization(
    org_id: UUID,
    removed: bool = Query(False, description="是否物理删除"),
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    删除机构 (逻辑删除)
    """
    try:
        org = await Organizations.filter(id=org_id).first()
        if not org:
            return send_data(False, None, "机构不存在")
        
        # 如果已经被逻辑删除，返回相应提示
        if org.is_deleted:
            return send_data(False, None, "该机构已被删除")
        
        if removed:
            # 物理删除
            await org.delete()
            return send_data(True, None)
        else:
            # 执行逻辑删除
            org.is_deleted = True
            org.deleted_at = datetime.now()
            await org.save()
            
            return send_data(True, OrganizationResponse.model_validate(org, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"删除机构失败: {str(e)}")
