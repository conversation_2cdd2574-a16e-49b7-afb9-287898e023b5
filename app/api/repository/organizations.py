from app.models.organizations import Organizations
from app.models.user_report_usage import UserReportUsage
from app.core.logging import get_logger
from app.api.schemas.organizations import OrganizationCreate, OrganizationResponse, OrganizationWithUserResponse
from app.models.role import Role
from app.api.schemas.role import InsetRole
from app.core.security import get_password_hash, generate_complex_password
from app.models.user import User
from app.core.config import settings


logger = get_logger(__name__)
async def get_default_org():
  try:
    organization = await Organizations.filter(
      code="default_org"
    ).first()
    logger.info(f"默认机构：{organization}")
    return organization
  except Exception as e:
    logger.error(f"获取默认机构信息失败")
    raise Exception(str(e))
async def calculate_organization_left_usable_count(
  organization_id: str
):
  ### 利用机构的可使用次数总数减去已经分配给用户的额度得到剩余可使用的次数 ###
  try:
    organization = await Organizations.filter(
      id=organization_id
    ).first()
    # 计算机构内所有用户已分配的次数总和（排除管理员，因为管理员的max_allowed_count是null）
    result = await UserReportUsage.filter(
        user_id__organization__id=organization_id,
        user_id__is_deleted=False,
        is_deleted=False,
        max_allowed_count__not_isnull=True  # 排除管理员（max_allowed_count为null的用户）
    ).all()
    distributed_total = sum(item.max_allowed_count for item in result if item.max_allowed_count is not None)
    logger.info(f"已经分配的数量：{distributed_total}")
    total_allocated = distributed_total if distributed_total else 0
    return (organization.limit_count or 0) - (total_allocated or 0)
  except Exception as e:
    logger.info(f"calculate_organization_left_usable_count函数报错：{str(e)}")
    raise Exception(str(e))

async def create_org_and_admin(
    organization: OrganizationCreate
) -> OrganizationWithUserResponse:
  # 创建新机构
  new_org = await Organizations.create(
      **organization.model_dump()
  )
  
  # 创建管理员角色
  admin_role = await Role.create(
      identifier=InsetRole.ADMIN.value,
      name="管理员",
      organization=new_org
  )
  
  # 生成复杂随机密码
  admin_password = generate_complex_password()
  
  # 创建管理员用户
  admin_user = await User.create(
    username=f"{new_org.code}_admin",
    hashed_password=get_password_hash(admin_password),
    role=admin_role,
    organization=new_org,
    mobile=organization.contact_phone or settings.DEFAULT_ORG_ADMIN_MOBILE.value
  )
  
  # 为管理员创建使用次数记录
  await UserReportUsage.create(
      user_id_id=admin_user.id,
      used_count=0,
      max_allowed_count=None
  )
  # 构建响应
  return OrganizationWithUserResponse.model_validate({
    **OrganizationResponse.model_validate(new_org, from_attributes=True).model_dump(),
    "username": admin_user.username,
    "password": admin_password
  }, from_attributes=True)