import os
import time
from fastapi import UploadFile
import alibabacloud_oss_v2 as oss
from app.core.config import settings
from app.models.upload_file import UploadFile as UploadFileModel
from app.core.logging import get_logger
from datetime import datetime
from app.utils.enum import UploadFileError
from app.api.schemas.upload_file import UploadFileResponse
from app.api.schemas.user import UserResponse

logger = get_logger(__name__)

UPLOAD_DIR = 'attachments'  # 可根据实际需要调整

def get_oss_client() -> oss.Client:
    """
    初始化并返回OSS客户端实例。
    """
    credentials_provider = oss.credentials.StaticCredentialsProvider(
        settings.OSS_ACCESS_KEY_ID,
        settings.OSS_ACCESS_KEY_SECRET
    )
    
    cfg = oss.config.Config(
        region=settings.OSS_REGION,
        credentials_provider=credentials_provider,
        endpoint=settings.OSS_ENDPOINT
    )

    return oss.Client(cfg)

async def save_upload_file(
    file: UploadFile,
    current_user: UserResponse
) -> UploadFileResponse:
    """
    上传文件到阿里云OSS，并保存到upload_files表，文件名用时间戳覆盖。
    :param file: FastAPI 的 UploadFile 对象
    :return: 保存后的文件信息字典
    """
    date_str = datetime.now().strftime("%Y%m%d")
    ext = os.path.splitext(file.filename)[1].lower()
    timestamp = int(time.time() * 1000)
    new_filename = f"{timestamp}{ext}"
    oss_object_key = f"{UPLOAD_DIR}/{date_str}/{new_filename}"

    # 读取文件内容
    content = await file.read()

    # 上传到OSS
    try:
        client = get_oss_client()
        result = client.put_object(
            oss.PutObjectRequest(
                bucket=settings.OSS_BUCKET,
                key=oss_object_key,
                body=content
            )
        )
        logger.info(f"文件已上传到OSS: {oss_object_key}, ETag: {result.etag}")
    except Exception as e:
        error_msg = f"{UploadFileError.SAVE_FILE_FAIL.value}: {str(e)}"
        logger.error(e)
        raise Exception(error_msg)

    # 保存数据库记录
    try:
        upload_file = await UploadFileModel.create(
            file_path=oss_object_key,
            file_name=file.filename,
            user_id=current_user.id
        )
        return UploadFileResponse.model_validate(upload_file, from_attributes=True)
    except Exception as e:
        error_msg = f"{UploadFileError.SAVE_DB_FAIL.value}: {str(e)}"
        logger.error(error_msg)
        # 如果数据库保存失败，需要删除刚刚上传的OSS文件，以保持数据一致性
        try:
            client = get_oss_client()
            client.delete_object(settings.OSS_BUCKET, oss_object_key)
            logger.warning(f"数据库保存失败，已从OSS回滚删除文件: {oss_object_key}")
        except Exception as rollback_e:
            logger.error(f"回滚删除OSS文件失败: {oss_object_key}, 错误: {rollback_e}")
        raise Exception(error_msg)

def write(path: str, content):
    """
    写入文件内容到OSS指定路径，支持文本(str)和字节(bytes)数据。
    :param path: OSS对象key
    :param content: 文件内容（str或bytes）
    """
    client = get_oss_client()
    if isinstance(content, str):
        content = content.encode('utf-8')
    try:
        result = client.put_object(
            oss.PutObjectRequest(
                bucket=settings.OSS_BUCKET,
                key=path,
                body=content
            )
        )
        logger.info(f"写入OSS文件成功: {path}, ETag: {result.etag}")
    except Exception as e:
        logger.error(f"写入OSS文件失败: {path}, 错误: {e}")
        raise 
    

def download_file(file_path: str):
    """
    确保本地有该文件：如果本地文件存在直接返回路径，否则从OSS下载并保存到本地（路径与入参一致），最后返回本地文件路径。
    Args:
        file_path: OSS对象key（也是本地相对路径）
    Returns:
        str: 本地文件绝对路径
    """
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    # 检查文件是否存在
    if os.path.exists(abs_path):
        return abs_path
    # 若不存在则从OSS下载
    client = get_oss_client()
    os.makedirs(os.path.dirname(abs_path), exist_ok=True)
    try:
        result = client.get_object(
            oss.GetObjectRequest(
                bucket=settings.OSS_BUCKET,
                key=file_path
            )
        )
        with open(abs_path, 'wb') as f:
            f.write(result.body.read())
        logger.info(f"OSS文件已下载到本地: {abs_path}")
        return abs_path
    except Exception as e:
        logger.error(f"OSS读取文件失败: {file_path}, 错误: {e}")
        raise


def remove_local_file(file_path: str):
    """
    删除本地文件，file_path可以是相对路径或绝对路径，文件不存在时不报错。
    """
    import os
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    try:
        logger.info("OSS模式删除本地文件咯！")
        if os.path.exists(abs_path):
            os.remove(abs_path)
    except Exception as e:
        logger.error(f"删除本地文件失败: {abs_path}, 错误: {e}")
