from typing import List, Optional, Callable, Coroutine, Any
from uuid import UUID
from datetime import datetime, timedelta

from app.api.repository import upload_file
from app.models.co_verify import CoVerify as CoVerifyModel
from app.api.schemas.co_verify import CoVerifyCreate, CoVerifyUpdate, CoVerifyResponse
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.services.wallet.interface import AGENT
from app.services.wallet.service import wallet_service
from app.utils.utils import (
  PageQuery,
  PageInfo,
  read_file_content,
  split_by_citation_section,
  extract_citations_in_paper_by_sentence,
  extract_reference_item_list,
  Reference,
  FAULT,
  hallucination_combine_citation,
  fault_normal,
  reference_valid,
  reference_related,
  insert_before_last_char,
  sanitize_filename
)
from app.utils.enum import CoVerifyStatus
from app.api.repository.user_default_model import get_user_model
from app.services.content_moderation_service import content_moderation_service
from app.utils.enum import UseCase
from app.services.system_config_service import system_config_service
from app.api.repository.project_config import get_one_project_config
import asyncio
from app.models.upload_file import UploadFile as UploadFileModel
from app.api.repository.user import is_user_authed
from app.core.redis_client import get_llm_interaction_instance
from app.utils.enum import ManagerScene
from app.utils.redis_content_manager import RedisContentManager


llm_interaction_instance = get_llm_interaction_instance()
content_manager = RedisContentManager(
    scene=ManagerScene.HALLUCINATION_REMOVE
)
logger = get_logger(__name__)

async def handle_hallucination_data(
    id: str,
    current_user: UserResponse,
    # 要不要对符合标准的参考文献进行内容或者存在性检测
    skip_compare: bool = False,
    success_callback:  Optional[Callable[[], Coroutine[Any, Any, None]]] = None,
    error_callback:  Optional[Callable[[], Coroutine[Any, Any, None]]] = None
):
    logger.info(f"项目：{id}开始幻觉审查")
    token_consume = {
        "input": 0,
        "output": 0
    }
    starttime = datetime.now()
    logger.info(f"幻觉审查开始时间：{starttime.strftime('%Y-%m-%d %H:%M:%S')}")
    try:
        data = await get_co_verify_by_id(id, current_user)
        model_response = await get_user_model(
            current_user=current_user,
            use_case=UseCase.HALLUCINATION_REMOVE.value
        )
        # 报告内容
        report_content = data.file.file_path
        try:
            upload_file.download_file(report_content)
            result_content = read_file_content(report_content)
        finally:
            upload_file.remove_local_file(report_content)
        # 将原文分成左侧文本，右侧文本和参考文献标题文本
        origin_data = split_by_citation_section(result_content)
        project_folder = f"llm_file/{data.id}"
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        timestamp = int(datetime.now().timestamp() * 1000)
        file_name = f"hallucination_{timestamp}.txt"
        relative_path = f"{project_folder}/{file_name}"
        co_verify = await CoVerifyModel.filter(id=data.id).first()

        if not origin_data:
            logger.info(f"材料{data.id}没有参考文献段落{data.id}")
            # 保存幻觉审查的报告
            upload_file.write(
                path=relative_path,
                content=result_content
            )
            co_verify.updated_at = datetime.now()
            co_verify.ai_report = relative_path
            co_verify.status = CoVerifyStatus.SUCCESS.value
            await co_verify.save()
            await content_manager.clear_project(project_id=id)
            await llm_interaction_instance.end_interaction(
                scene=ManagerScene.HALLUCINATION_REMOVE,
                id=co_verify.id
            )
            return

        # 将引文内容提取出来
        sentences = extract_citations_in_paper_by_sentence(
            origin_data.text_before_citation_title)
        references = extract_reference_item_list(
            origin_data.citation_paragraph_without_title)
        # 这是用来暂存参考文献的数组，后续要基于这个List重新排序基于index属性
        right_text: List[dict] = []
        # 这里放置的是所有格式符合的标准参考文献。
        next_list: List[Reference] = []
        for i, reference in enumerate(references):
            # 格式不正确的文献
            if reference.remark and FAULT.TYPE_ERROR.value in reference.remark:
                content_text = hallucination_combine_citation(
                    url=reference.url,
                    index=reference.index,
                    full_text=reference.full_text,
                    error_list=reference.remark
                )
                right_text.append({
                    "content": content_text,
                    "index": reference.index
                })
                # 如果参考文献不标准，那么在正文引用这个参考文献的地方也要加上参考文献不标准的错误
                for sentence in sentences:
                    nums = sentence.nums
                    if reference.index in nums:
                        sentence.remark = [fault_normal(
                            reference.index, FAULT.TYPE_ERROR)]
            # 格式正确的文献
            else:
                if (skip_compare):
                    continue
                next_list.append(reference)
        # 添加一個標記來確保只調用一次成功回調
        success_callback_called = False
        
        async def next_fn(reference: Reference):
            nonlocal token_consume, success_callback_called
            async def complete_callback(token_data: dict):
                token_consume["input"] += token_data.get("input")
                token_consume["output"] += token_data.get("output")
            
            # 如果已經調用過成功回調，則傳遞None
            async def conditional_success_callback():
                nonlocal success_callback_called
                if not success_callback_called and success_callback:
                    success_callback_called = True
                    await success_callback()
            
            logger.info(
                f"项目：{id}正在进行幻觉审查的文献标号：{reference.index}")
            result_text = await reference_valid(
                data=reference,
                api_key=model_response.api_key,
                api_url=model_response.api_url,
                model=model_response.model_name,
                is_thinking=model_response.is_thinking,
                complete_callback=complete_callback,
                success_callback=conditional_success_callback if not success_callback_called else None
            )
            # 文献可能不存在
            if result_text == 'No':
                if reference.remark:
                    reference.remark.append(FAULT.FAKE_REFERENCE.value)
                else:
                    reference.remark = [FAULT.FAKE_REFERENCE.value]
            # 检测内容是否和总结文本一致和将幻觉文献的引用内容打上标记
            for sentence in sentences:
                content = sentence.text
                nums = sentence.nums
                if reference.index in nums:
                    # 有总结内容，也就是文献存在
                    if result_text != 'No':
                        if (skip_compare):
                            break
                        # 判断从大模型对网页结果进行总结得到的文献观点和正文中引述的观点是否相关
                        is_related = await reference_related(
                            content=result_text,
                            expression=content,
                            api_key=model_response.api_key,
                            api_url=model_response.api_url,
                            model=model_response.model_name,
                            is_thinking=model_response.is_thinking,
                            complete_callback=complete_callback
                        )
                        # 如果不想关就写入错误
                        if not is_related:
                            if sentence.remark:
                                sentence.remark.append(fault_normal(
                                    reference.index, FAULT.OPINION_UNFIT))
                            else:
                                sentence.remark = [fault_normal(
                                    reference.index, FAULT.OPINION_UNFIT)]
                    # 文献不存在
                    else:
                        if sentence.remark:
                            sentence.remark.append(fault_normal(
                                reference.index, FAULT.FAKE_REFERENCE))
                        else:
                            sentence.remark = [fault_normal(
                                reference.index, FAULT.FAKE_REFERENCE)]
            # 判断文献是否有被正文引用
            is_referenced = any(
                reference.index in item.nums for item in sentences)
            if not is_referenced:
                if reference.remark:
                    reference.remark.append(FAULT.NOT_REFERENCED.value)
                else:
                    reference.remark = [FAULT.NOT_REFERENCED.value]
            text = reference.full_text.rstrip('\n')
            full_text = hallucination_combine_citation(
                url=reference.url,
                index=reference.index,
                full_text=text,
                error_list=reference.remark
            )
            right_text.append({
                "content": full_text,
                "index": reference.index
            })
        # 将给定的一维数组切换成元素个数固定的二维数组

        def chunk_list(lst: List[Reference], chunk_size=5):
            return [lst[i:i+chunk_size] for i in range(0, len(lst), chunk_size)]
        wrapper_list = chunk_list(next_list)
        for item in wrapper_list:
            tasks = [next_fn(item1) for item1 in item]
            await asyncio.gather(*tasks)
        logger.info(f"项目：{id}幻觉审查的异步协程处理完毕。")
        # 这是参考文献段落里面所有的参考文献的序号组成的List
        all_reference_list = [reference.index for reference in references]
        for sentence in sentences:
            # 判断正文里面的角标是不是在参考文献列表里面有
            for num in sentence.nums:
                if num not in all_reference_list:
                    if sentence.remark:
                        sentence.remark.append(
                            fault_normal(num, FAULT.NOT_IN_REFERENCE))
                    else:
                        sentence.remark = [fault_normal(
                            num, FAULT.NOT_IN_REFERENCE)]
            if sentence.remark:
                remark = ('、').join(sentence.remark)
                # sentence.origin形如【我是一个兵。】
                # new_sentence形如【<span style='text-decoration: underline;text-decoration-color: red;'>我是一个兵</span><span style='color: red'>(文献有错误)</span>。】
                new_sentence = insert_before_last_char(
                    f"<span style='text-decoration: underline;text-decoration-color: red;'>{sentence.origin}",
                    f"</span><span style='color: red'>&#40;{remark}&#41;</span>"
                )
                # 将正文里面引述句子更改为新的带错误提示的句子
                origin_data.text_before_citation_title = origin_data.text_before_citation_title.replace(
                    sentence.origin,
                    new_sentence
                )
        # 将参考文献列表按照index属性重新排序。因为刚刚存在异步操作。导致right_text数组里面的每一项可能序号不对
        right_text = sorted(
            right_text, key=lambda x: x["index"], reverse=False)
        logger.info(f"项目：{id}幻觉审查完成")
        # logger.info(f"right_text:{right_text}")
        origin_data.citation_paragraph_without_title = "\n\n".join(
            [item["content"] for item in right_text])
        # logger.info("right_text后面")
        content_list = [
            origin_data.text_before_citation_title,
            origin_data.citation_title,
            origin_data.citation_paragraph_without_title,
        ]
        # 如果参考文献段落后面还有段落
        if origin_data.paragraph_after_citation:
            content_list.append(origin_data.paragraph_after_citation)
        try:
            # logger.info(f"content_list: {content_list}")
            upload_file.write(
                path=relative_path,
                content=("\n\n").join(content_list)
            )
        except Exception as e:
            logger.error("save_text_to_file这里报错了")
        logger.info(f"项目：{id}幻觉审查的文件已经写进去了。")
        endtime = datetime.now()
        logger.info(
            f"幻觉审查结束时间: {endtime.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(endtime - starttime).total_seconds()}秒")
        logger.info(f"幻觉审查的token消耗：{token_consume}")
        co_verify.updated_at = datetime.now()
        co_verify.ai_report = relative_path
        co_verify.status = CoVerifyStatus.SUCCESS.value
        await co_verify.save()
        # 注意：成功回调現在在第一次搜索成功時就已經調用了，這裡不再重複調用
    except Exception as e:
        logger.error(f"项目：{id}去幻觉的异步处理流程报错：{str(e)}")
        # 失败回调
        error_callback and await error_callback()

    await content_manager.clear_project(project_id=id) 
    await llm_interaction_instance.end_interaction(
        scene=ManagerScene.HALLUCINATION_REMOVE,
        id=id
    )   

async def create_co_verify(
  data: CoVerifyCreate,
  current_user: UserResponse,
) -> CoVerifyResponse:
    """
    创建验证记录
    """
    try:
        co_verify = await CoVerifyModel.create(
            file_id=data.file_id,
            user_id=current_user.id,
            status=data.status or CoVerifyStatus.NO_START.value
        )
        return await get_co_verify_by_id(co_verify.id, current_user)
    except Exception as e:
        logger.error(f"创建验证失败: {str(e)}")
        raise Exception(f"创建验证失败: {str(e)}")


async def get_co_verify_by_id(verify_id: UUID, current_user: UserResponse) -> CoVerifyResponse:
    """
    根据ID获取验证记录
    """
    try:
        co_verify = await CoVerifyModel.filter(
          id=verify_id,
          is_deleted=False,
          user_id=current_user.id
        ).prefetch_related("file", "user").first()
        
        if not co_verify:
            raise Exception("验证记录不存在")
        
        return CoVerifyResponse.model_validate(co_verify, from_attributes=True)
    except Exception as e:
        error_msg = str(e)
        logger.error(error_msg)
        raise Exception(error_msg)


async def get_co_verify_list(
    query: PageQuery,
    current_user: UserResponse,
    keyword: Optional[str] = None
) -> PageInfo[CoVerifyResponse]:
    """
    分页获取验证列表（只查询近配置天数的记录）
    """
    try:
        queryset = CoVerifyModel.filter(is_deleted=False, user_id=current_user.id)
        
        # 只查询近配置天数的记录
        filter_days_str = await system_config_service.get_config("history_record_filter_days")
        filter_days = int(filter_days_str) if filter_days_str and filter_days_str.isdigit() else 30
        thirty_days_ago = datetime.now() - timedelta(days=filter_days)
        queryset = queryset.filter(updated_at__gte=thirty_days_ago)
        
        # 如果有关键字，则添加文件名模糊匹配
        if keyword:
            queryset = queryset.filter(file__file_name__icontains=keyword)
        
        total = await queryset.count()
        
        items = await queryset.prefetch_related("file", "user").offset(
            (query.page - 1) * query.size
        ).limit(query.size).order_by("-updated_at")
        
        result_items = [
            CoVerifyResponse.model_validate(item, from_attributes=True) 
            for item in items
        ]
        
        return {
            "total": total,
            "page": query.page,
            "size": query.size,
            "items": result_items
        }
    except Exception as e:
        logger.error(f"获取验证列表失败: {str(e)}")
        raise Exception(f"获取验证列表失败: {str(e)}")


async def update_co_verify(verify_id: UUID, data: CoVerifyUpdate, current_user: UserResponse) -> CoVerifyResponse:
    """
    更新验证记录的ai_report和status
    """
    try:
        co_verify = await CoVerifyModel.filter(
            id=verify_id,
            user_id=current_user.id,
            is_deleted=False
        ).first()
        
        if not co_verify:
            raise Exception("验证记录不存在")
        
        update_data = {}
        if data.ai_report is not None:
            update_data["ai_report"] = data.ai_report
        if data.status is not None:
            update_data["status"] = data.status
        if data.file_id is not None:
            update_data["file_id"] = data.file_id
        
        if update_data:
            await CoVerifyModel.filter(id=verify_id).update(**update_data)
        
        return await get_co_verify_by_id(verify_id, current_user)
    except Exception as e:
        logger.error(f"更新验证记录失败: {str(e)}")
        raise Exception(f"更新验证记录失败: {str(e)}")


async def delete_co_verify(verify_id: UUID, current_user: UserResponse) -> bool:
    """
    软删除验证记录
    """
    try:
        co_verify = await CoVerifyModel.filter(
            id=verify_id,
            is_deleted=False,
            user_id=current_user.id 
        ).first()
        
        if not co_verify:
            raise Exception("验证记录不存在")
        
        await CoVerifyModel.filter(id=verify_id).update(
            is_deleted=True,
            deleted_at=datetime.now()
        )
        
        return True
    except Exception as e:
        logger.error(f"删除验证记录失败: {str(e)}")
        raise Exception(f"删除验证记录失败: {str(e)}")


async def create_co_verify_from_project(
    project_id: str,
    current_user: UserResponse,
    skip_compare: bool = False
) -> CoVerifyResponse:
    """
    根据项目ID创建幻觉审查记录并开始审查
    
    Args:
        project_id: 项目配置ID
        current_user: 当前用户
        skip_compare: 是否跳过内容比较检测
        
    Returns:
        CoVerifyResponse: 创建的验证记录
    """
    order_id = None
    business_id = None

    async def success_callback():
        await wallet_service.charge_confirm(current_user.id, order_id, business_id)

    async def error_callback():
        await wallet_service.charge_cancel(current_user.id, order_id, business_id)

    try:
        logger.info(f"开始为项目 {project_id} 创建幻觉审查记录")
        
        # 1. 获取项目配置详情
        project_config = await get_one_project_config(project_id)
        if not project_config:
            raise Exception("项目配置不存在")
        # 2. 检查项目是否属于当前用户或用户有权限访问
        if not await is_user_authed(
            resource_belong_user=project_config.user.id,
            operator=current_user.id
        ):
            raise Exception("无权访问此项目")
        # 3. 确定要审查的正文内容路径（优先级：手动修改的报告 > AI生成的报告 > 手动修改的大纲 > AI生成的大纲）
        content_path = project_config.manual_modified_report or project_config.ai_generated_report
        if not content_path:
            raise Exception("论文还没有生成")        
        
        # 4. 文本内容审核 - 检查是否包含敏感词或违规内容
        is_safe, error_msg = await content_moderation_service.verify_file_content(
            file_path=content_path,
            description=f"项目 {project_id} 的文档"
        )
        
        if not is_safe:
            raise Exception(error_msg)
        
        # 5. 创建文件记录（使用项目内容的路径）
        file_record = await UploadFileModel.create(
            file_path=content_path,
            file_name=sanitize_filename(project_config.name) + '.txt',
            user_id=current_user.id
        )
        
        # 6. 创建幻觉审查记录
        co_verify = await CoVerifyModel.create(
            file_id=file_record.id,
            user_id=current_user.id,
            status=CoVerifyStatus.ONGOING.value
        )
        
        logger.info(f"项目 {project_id} 的幻觉审查记录已创建: {co_verify.id}")

        # saas预计费
        order_id = await wallet_service.pre_charge(current_user.id, AGENT.HALLUCINATION.value)
        business_id = co_verify.id

        # 7. 异步开始幻觉审查处理
        asyncio.create_task(
            handle_hallucination_data(
                id=str(co_verify.id), 
                current_user=current_user, 
                skip_compare=skip_compare,
                success_callback=success_callback,
                error_callback=error_callback,
            )
        )
        await llm_interaction_instance.start_interaction(
            scene=ManagerScene.HALLUCINATION_REMOVE,
            id=co_verify.id
        )
        
        # 8. 返回创建的记录
        return await get_co_verify_by_id(co_verify.id, current_user)
        
    except Exception as e:
        error_msg = f"创建幻觉审查失败: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg) 