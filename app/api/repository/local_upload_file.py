import os
import time
from fastapi import UploadFile
from app.models.upload_file import UploadFile as UploadFileModel
from app.core.logging import get_logger
from datetime import datetime
from app.utils.enum import UploadFileError
from app.api.schemas.upload_file import UploadFileResponse
from app.api.schemas.user import UserResponse

logger = get_logger(__name__)

UPLOAD_DIR = 'attachments'  # 可根据实际需要调整

async def save_upload_file(
    file: UploadFile,
    current_user: UserResponse
) -> UploadFileResponse:
    """
    上传文件并保存到 upload_files 表，文件名用时间戳覆盖。
    :param file: FastAPI 的 UploadFile 对象
    :return: 保存后的文件信息字典
    """
    date_str = datetime.now().strftime("%Y%m%d")
    document_path = f"{UPLOAD_DIR}/{date_str}"
    # 确保上传目录存在
    if not os.path.exists(document_path):
        os.makedirs(document_path)
    
    # 生成新文件名：时间戳+原后缀
    ext = os.path.splitext(file.filename)[1].lower()
    timestamp = int(time.time() * 1000)
    new_filename = f"{timestamp}{ext}"
    file_path = f"{document_path}/{new_filename}"

    # 获取绝对路径
    abs_path = os.path.join(os.getcwd(), file_path)
    # 保存文件到本地
    try:
        with open(abs_path, "wb") as f:
            content = await file.read()
            f.write(content)
        logger.info(f"文件已保存到: {file_path}")
    except Exception as e:
        error_msg = f"{UploadFileError.SAVE_FILE_FAIL.value}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)

    # 保存数据库记录
    try:
        upload_file = await UploadFileModel.create(
            file_path=file_path,
            file_name=file.filename,
            user_id=current_user.id
        )
        return UploadFileResponse.model_validate(upload_file, from_attributes=True)
    except Exception as e:
        error_msg = f"{UploadFileError.SAVE_DB_FAIL.value}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


def write(path: str, content):
    """
    写入文件内容到指定路径，支持文本(str)和字节(bytes)数据。
    :param path: 文件路径
    :param content: 文件内容（str或bytes）
    """
    abs_path = os.path.join(os.getcwd(), path) if not os.path.isabs(path) else path
    os.makedirs(os.path.dirname(abs_path), exist_ok=True)
    mode = 'wb' if isinstance(content, bytes) else 'w'
    encoding = None if isinstance(content, bytes) else 'utf-8'
    try:
        with open(abs_path, mode, encoding=encoding) as f:
            f.write(content)
    except Exception as e:
        logger.error(f"写入文件失败: {abs_path}, 错误: {e}")
        raise


def download_file(file_path: str):
    """
    确保本地有该文件：返回本地文件的绝对路径
    Args:
        file_path: 文件相对路径
    Returns:
        str: 本地文件绝对路径
    """
    return

def remove_local_file(file_path: str):
    """
    删除本地文件，file_path可以是相对路径或绝对路径，文件不存在时不报错。
    Args:
        file_path: 文件路径（相对路径或绝对路径）
    """
    return
   
