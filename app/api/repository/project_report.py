from app.api.repository import upload_file
from app.services.research_service import (
    generate_search_queries,
    process_search_query,
    get_new_search_queries
)
from typing import Optional, Callable
from app.api.repository.research import get_context_length
from app.utils.llm_service import stream_llm_and_save
from app.services.llm_service import call_llm
from app.api.repository.dictionary import get_one_dict_by_category_and_value
import asyncio
from app.api.schemas.user import UserResponse
from app.utils.enum import (
  CallLLMFlag,
  ProjectReportError,
  ProjectConfigError,
  ErrorType
)
from app.models.project_configs import ProjectConfig, ProjectConfigStatus
from app.models.project_configs import ProjectConfig
from app.utils.utils import (
    read_file_content,
    remove_markdown_h1_and_text,
    # save_text_to_file,
    handle_text_before_use,
    sanitize_filename,
    convert_markdown_to_docx,
    remove_after_reference,
    remove_markdown_heading,
    remove_word_count_tag,
    adjust_markdown_heading_levels,
    remove_markdown_code_fence
)
from app.models.user_report_usage import UserReportUsage
import re
from typing import List, Optional, Callable
import json
from app.api.schemas.literatures import LiteratureResponse
from datetime import datetime
from app.models.research import Research, ResearchStatus
from app.core.logging import get_logger
from app.api.repository.project_config import (
    get_one_project_config
)
from app.utils.enum import ErrorType
import asyncio
from app.utils.llm_service import stream_llm_and_save
from pydantic import BaseModel
from app.api.schemas.project_configs import ProjectConfigResponse2, ProjectConfigStatus
from app.utils.constants import ProductType
from app.api.repository.literatures import (
    get_literature_by_project,
    get_literature_length
)
from app.api.schemas.user import UserResponse
from app.utils.enum import CallLLMFlag
from app.utils.content_manager import ContentStatus
from app.core.config import settings
from app.api.repository.user import is_user_authed
from app.services.prompts import TRANSLATE_PAPER_TITLE_PROMPT
from app.utils.picture import (
    extract_chart_placeholders,
    get_search_keywords,
    get_demo_data,
    get_figure_type,
    create_bar_chart,
    create_line_chart,
    create_pie_chart,
    create_scatter_plot,
    replace_text_with_markdown_image_remove
)
from app.services.search_service import (
    fetch_webpage_text_async,
    perform_serpapi_search
)
from app.api.repository.user_default_model import get_user_model
from app.utils.enum import UseCase
from app.services.prompts import generate_figure_data_prompt
from app.utils.enum import ManagerScene
from app.utils.redis_content_manager import RedisContentManager
from app.core.redis_client import get_llm_interaction_instance
from app.api.schemas.model_config import ModelConfigBase

llm_interaction_instance = get_llm_interaction_instance()
logger = get_logger(__name__)

report_content_manager = RedisContentManager(
    scene=ManagerScene.PAPER_GENERATE
)
outline_content_manager = RedisContentManager(
    scene=ManagerScene.LAYOUT_GENERATE
)


async def wrapper_error_callback(
    error_msg: str,
    error_callback: Optional[Callable[[str], None]] = None
):
    logger.error(error_msg)
    if error_callback:
        await error_callback(error_msg)


async def search_to_carry_info(
    research: Research,
    config_response: ProjectConfigResponse2,
    current_user: UserResponse,
    model_info: ModelConfigBase,
    # 搜索关键词的引擎的标识符
    search_method_flag: Optional[str] = None,
    open_literature_summary=False,
):
    logger.info(f"材料{config_response.id}: 开始生成初始网络搜索查询关联词列表")
    # 從系統配置中動態獲取搜索引擎配置
    from app.services.system_config_service import system_config_service
    search_engine_config = await system_config_service.get_config("SEARCH_ENGINE")
    if not search_engine_config:
        search_engine_config = settings.SEARCH_ENGINE
    logger.info(f"search_engine_config: {search_engine_config}")
    # 获得初始的google查询关键词列表
    initial_keywords_list = await generate_search_queries(
        title=config_response.name,
        api_key=model_info.api_key,
        api_url=model_info.api_url,
        model=model_info.model_name,
        search_engine=search_engine_config,
        is_thinking=model_info.is_thinking
    )
    if not initial_keywords_list:
        error_msg = f"{ProjectReportError.NOT_KEY_WORDS.value}"
        raise Exception(error_msg)

    # 打印关键词列表
    queries_list = '\n'.join([f"- {query}" for query in initial_keywords_list])
    progress_msg = f"{ProjectReportError.KEY_WORDS_LIST.value}：\n\n{queries_list}\n\n"
    logger.info(progress_msg)

    # 已经迭代的次数
    iteration = 0
    # 最大迭代次数
    max_iterations = settings.RESEARCH_ITERATION_LIMIT
    # 添加最大搜索查询次数限制
    max_search_queries = settings.MAX_SEARCH_QUERIES  # 使用配置的最大搜索查询数
    max_contexts = settings.MAX_LITERATURE_AND_CONTEXTS if open_literature_summary else settings.MAX_CONTEXTS  # 使用配置的最大上下文数量
    # 已经处理的关键词个数
    total_processed_queries = 0
    # 所有的关键词列表
    keywords_list = initial_keywords_list.copy()
    # 关键词的计位数字
    keyword_index = 0
    model = await get_user_model(
        current_user=current_user,
        use_case=UseCase.CONCLUDE_WEB_PAGE_USEFUL.value
    )
    logger.info(f"判断有用性用的大模型是：{model.model_name}")
    while iteration < max_iterations:
        # 检查是否已处理所有查询或达到查询上限
        remaining_queries = keywords_list[keyword_index:]
        if not remaining_queries or total_processed_queries >= max_search_queries:
            logger.info(f"结束搜索的条件是：所有查询已经处理完毕({bool(remaining_queries)}),达到查询上限({bool(total_processed_queries > max_search_queries)})")
            done_msg = "✅ 所有查询已处理完毕或达到查询上限\n\n"
            logger.info(done_msg)
            break

        # 处理每个查询，但确保不超过最大查询次数
        for query in remaining_queries:
            # 检查是否达到查询上限
            if total_processed_queries >= max_search_queries:
                limit_msg = f"⚠️ 已达到查询上限({max_search_queries}个)，停止搜索\n\n"
                logger.info(limit_msg)
                # yield f"data: {json.dumps({'content': limit_msg, 'type': 'warning'})}\n\n".encode('utf-8')
                break

            search_msg = f"正在搜索关键词: \"{query}\"\n\n"
            logger.info(search_msg)
            # yield f"data: {json.dumps({'content': search_msg, 'type': 'status'})}\n\n".encode('utf-8')

            # 处理搜索查询
            search_engine_site_url = ""
            if search_method_flag:
                result = await get_one_dict_by_category_and_value(
                    category="参考文献库",
                    value=search_method_flag
                )
                if result:
                    search_engine_site_url = result.remark
            contexts = await process_search_query(
                title=config_response.name,
                query=query,
                research_id=str(research.id),
                api_key=model.api_key,
                api_url=model.api_url,
                model=model.model_name,
                is_thinking=model.is_thinking,
                is_summary_literature=open_literature_summary,
                search_method_flag=search_method_flag,
                search_engine_site_url=search_engine_site_url
            )
            logger.info(f"关键词：【{query}】的上下文收集完毕")
            keyword_index += 1
            # 增加迭代计数和处理查询计数
            research.iterations += 1
            research.contexts += contexts
            total_processed_queries += 1
            await research.save()

            # 如果开启了有用的context总结文献的话，
            # 就context的长度为有用的context加上文献条数，
            # 否则仅是有用的context长度
            reference_length = await get_literature_length(research_id=research.id)
            other_length = await get_context_length(research_id=research.id)
            context_length = len(
                research.contexts) if not open_literature_summary else reference_length + other_length

            # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
            if context_length >= max_contexts:  # 使用配置的最大上下文数量
                enough_msg = f"✅ 已收集足够的信息（{len(research.contexts)}条上下文）\n\n（318）"
                logger.info(enough_msg)
                # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
            # 报告进度
            if contexts:
                success_msg = f"✅ 已找到 {len(contexts)} 条相关信息\n\n"
                logger.info(success_msg)
                # yield f"data: {json.dumps({'content': success_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            else:
                warning_msg = "⚠️ 未找到相关信息\n\n"
                logger.info(warning_msg)
                # yield f"data: {json.dumps({'content': warning_msg, 'type': 'warning'})}\n\n".encode('utf-8')
        # 如果开启了有用的context总结文献的话，
        # 就context的长度为有用的context加上文献条数，
        # 否则仅是有用的context长度
        reference_length = await get_literature_length(research_id=research.id)
        other_length = await get_context_length(research_id=research.id)
        context_length = len(
            research.contexts) if not open_literature_summary else reference_length + other_length

        # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
        if context_length >= max_contexts:  # 使用配置的最大上下文数量
            enough_msg = f"✅ 已收集足够的信息：（{context_length}条背景信息（文献加context））max_contexts：{max_contexts}\n\n（334）"
            logger.info(enough_msg)
            # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            break

        # 分析已收集的信息并获取新的搜索查询
        analyzing_msg = "正在分析已收集的信息...\n\n"
        logger.info(analyzing_msg)
        # yield f"data: {json.dumps({'content': analyzing_msg, 'type': 'status'})}\n\n".encode('utf-8')

        research.status = ResearchStatus.ANALYZING
        await research.save()

        try:
            new_queries = await get_new_search_queries(
                research=research,
                api_key=model_info.api_key,
                api_url=model_info.api_url,
                model=model_info.model_name,
                max_contexts=max_contexts,
                is_thinking=model_info.is_thinking
            )

            # 如果不需要更多查询，退出循环
            if new_queries is None:
                complete_msg = "✅ 已收集足够的信息\n\n（357）"
                logger.info(complete_msg)
                # yield f"data: {json.dumps({'content': complete_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break

            # 更新搜索查询并继续
            if new_queries and len(new_queries) > 0:
                # 限制新增查询数量，避免无限增长
                new_queries = new_queries[:5]  # 每轮最多添加5个新查询
                research.search_queries = research.search_queries + new_queries
                research.status = ResearchStatus.SEARCHING
                await research.save()

                new_queries_list = '\n'.join(
                    [f"- {query}" for query in new_queries])
                new_queries_msg = f"已生成新的搜索查询：\n\n{new_queries_list}\n\n"
                logger.info(new_queries_msg)
                keywords_list += new_queries
                # yield f"data: {json.dumps({'content': new_queries_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            else:
                # 无新查询，退出循环
                done_msg = "✅ 搜索完成，未生成新的查询\n\n"
                logger.info(done_msg)
                # yield f"data: {json.dumps({'content': done_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
        except Exception as e:
            error_msg = f"⚠️ 生成新查询时出错: {str(e)}\n\n"
            # yield f"data: {json.dumps({'content': error_msg, 'type': 'error'})}\n\n".encode('utf-8')
            logger.error(f"研究 {research.id}: 生成新查询时出错: {str(e)}")
            # 出错时也要继续，避免整个过程中断
            break

        iteration += 1


class OutlineNode(BaseModel):
    title: str
    content: Optional[str] = ""
    children: List["OutlineNode"] = []
    level: Optional[int] = 0
    count: int

############################# 参考文献提示词##############################
# 将literature根据title去重


def deduplicate_by_title(list_data):
    seen_titles = set()
    result = []
    for item in list_data:
        title = item.title
        if title and title not in seen_titles:
            seen_titles.add(title)
            result.append(item)
    return result


def final_report_reference_prompt(
    literatures: List[LiteratureResponse]
) -> str:
    if literatures:
        data = [
            ({
                "sort": i + 1,
                "citation_format": literature.citation_format,
                "summary": literature.summary,
                "url": literature.url,
                "doi": literature.doi
            }) for i, literature in enumerate(deduplicate_by_title(literatures))
        ]
        literature_text = json.dumps(data, ensure_ascii=False, indent=2)
        # print(literature_text)
    format_text = FINAL_REPORT_REFERENCE_PROMPT_TEMPLATE
    return format_text.format(literatures='' if not literatures else literature_text)


# 最终正文的必须使用的参考文献部分提示词，输入到用户提示词中
FINAL_REPORT_REFERENCE_PROMPT_TEMPLATE = """
这下面是我围绕用户研究主题给你精心准备的高质量参考文献列表（后续称为【预设参考文献】，这一部分中很多内容是多余的，你要生成的目标这一段或者这几段文本在严格遵守字数要求的前提下自己选择参考，但是每一段话的引用标号最多不要超过2个）：
{literatures}
【预设参考文献】的json解释说明：
[{{
  "sort": "序号",
  "doi": "文献的唯一数字对象标识符（DOI），用于全球唯一定位该文献，通常由发布机构提供。",
  "url": "指向该文献网页的超链接地址，通常用于在线访问或查看原始文献内容。",
  "citation_format": "包含文献的作者、标题、期刊名称、年份、卷期页码等参考信息，用于引用该文献。",
  "summary": "用简洁文字对文献的核心内容、研究方法、主要结论或价值进行总结概述。字数大约在1000字左右或以内"
}}]
"""
############################# 参考文献提示词##############################


################# 这是开启强制参考文献引用的情况下，提示词里面的关于参考文献要求的提示词#####################
LITERATURE_PROMPT = """
## 参考文献【及其重要，重点反思核验、确保严格执行以下要求】
  • 在写作正文的过程中需要引述他人研究成果时只能从【预设参考文献】进行选取，而且表述的内容必须和【预设参考文献】的summary字段内容表述一致。
  •【预设参考文献】就是最终正文里面参考文献段落的内容，所以在引用【预设参考文献】时，正文里面的角标必须用sort字段的值。
  • 如果你引用了【预设参考文献】里面的文献，你需要反复核对文献的真实性，包括但不限于期刊名称、题目、作者等。
  • 正文引用的最大序号不得大于【预设参考文献】里面的最大sort；
  • “研究内容与目标”、“研究方法与技术路线”、“选题背景”段落需多引用【预设参考文献】里面文献。
  • 如果【预设参考文献】里面的文献出现除文章标题和sort字段以外的错误，请进行更正。
  • 在输出任何参考文献时，你必须严格遵守期刊名称准确性的规则。严禁使用任何不完整、不标准或错误的期刊缩写。你只能使用官方标准缩写（如PubMed/NLM标准）或期刊的完整官方全称。错误范例： 'Proceedings of the National Academy of Sciences'，正确范例： 'Proc Natl Acad Sci U S A'，若无法确认标准缩写，必须使用期刊完整全称，绝不能自行简化。
"""

# 最终正文的用户提示词，输入到用户提示词中
FINAL_REPORT_BACKGROUND_PROMPT = """
{literature_prompt}

## 输出内容要求    
  1. 段落策略：每一级标题下主阐述 ≤3 段，每段≥200 字，避免碎片化。  
  2. 伦理声明(如需) 与图表占位留文字提示，不生成图片。  
  3. 禁止输出：繁体、日文、韩文及乱码
  4. 你必须将【预设参考文献】和网络检索信息作为撰写正文的核心依据和首要信息来源。报告中的关键论点、数据支撑和分析都应优先从这些指定材料中提炼和整合，确保生成内容与用户提供的背景信息高度一致。
  5. 篇幅和水平要求：生成的内容研究水平要很高，特别是所给标题类似于"研究内容与目标"的部分要重点阐述，要用很大段来表达，不要用一小段一小段的生成，输出最好不要超过3段。
  6. 如果所给标题有类似于：选题背景、立项依据与研究内容。那么这部分要重点阐述： 1. 研究意义（含科学前沿、国家需求，至少 2 段）  2. 国内外现状与差距（综述＋批判性分析）。这部分写作过程中需要引用【预设参考文献】，这部分写作的正文引用的参考文献不要超过10篇。
  7. 如果所给标题有类似于：研究内容和目标。请在研究内容和目标开头处分别增加一段概括性的文字，对应字数200-400字左右，详细描述它的下面几个部分研究内容及它们之间的联系，特别是研究内容篇幅和深度要多、要深入。这部分写作过程中需要引用【预设参考文献】，这部分写作的正文引用的参考文献不要超过10篇。
  8. 如果所给标题有类似于：拟解决的关键科学问题。那么输出篇幅和深度要多、要非常的具体和深入（这部分是核心体现出大专家院士级别水平部分的重点），这部分要加入一些关键的非常前沿的技术、研究或者实验方法，根据研究主题判断是否需要技术、研究、或者实验方法，这部分写作过程中需要引用【预设参考文献】，这部分写作的正文引用的参考文献不要超过10篇。
  9. 如果所给标题有类似于：申请人\主要参与人\团队成员简介。这部分要重点阐述，并且要参考用户提供的申请人信息（不要虚构，如果没有，则不需要输出，这部分严格按照用户给定的信息来生成，不要虚构，也不要给我示例，如果没有就不要输出，这部分千万不能有虚构和捏造），结合用户提供的参考资料，不要虚构，也不要给我示例，如果没有就不要输出，这部分千万不能有虚构和捏造，【这部分不要引用参考文献】
  10.如果所给标题有类似于：研究方法与技术路线。这部分也是重点阐述内容，每个部分都要用大段来表达，不要用一小段一小段的生成，每个项目阐述分段不超过3段，特别是篇幅和深度要多、要深入，要加入关键技术、实验方法，具有一定的前沿性并得到国际认同，拟采取的研究方案及可行性分析（包括研究方法、技术路线、实验手段、关键技术等说明，加入关键分析方法或算法、相关数据库等，适度根据前沿研究和用户主题展开，不要夸夸而谈、不要泛泛而谈，要很具体，很有深度，并且经得起专家的挑战和质疑）；这部分写作过程中需要引用【预设参考文献】，这部分写作的正文引用的参考文献不要超过10篇。
  11.如果所给标题有类似于：预期成果与创新点。预期成果与创新点中的内容分成两个子章节，一个子章节描述预期成果，以及考核指标和考核方法，另一个子章节描述科学价值、特色和创新点。其中：
    * 预期成果【这部分不要参考文献】：可以是新理论、新原理、新产品、新技术、新方法、软件、应用解决方案、临床指南/规范、工程工艺、标准、论文、专利等等。考核指标尽量是可以量化的指标，不能量化的指标要描述清楚，要具有可操作性，不能是一个范围，可以写不少于多少个/多少篇。
    * 创新点【这部分最多5篇参考文献】：科学价值、特色和创新点，需要精简凝练，体现出本研究内容和方法的创新和价值，要避免小段阐述，要大段描述，具有很高的国际水平与视野，具有较好的学科专业度和创新性，并列出意义，这部分篇幅内容要多，但要避免小段阐述，要大段描述，并且要具有一定的前沿性和创新性，一定要适度提炼和高度要高
    * 四维创新
      1.  理论/认知 ——提出全新假说或规律，解决源头科学问题。
      2.  视角/范式 ——跨学科或系统生物学等新框架，重塑研究思路。
      3.  内容/体系 ——选取前所未有的对象或构建多因素耦合体系。
      4.  方法/技术 ——自研或首创实验/算法/平台，实现关键指标突破。
    * 精炼总结：结尾用 2–3 行列出可验证、可区分的最关键创新点。
  12.如果所给标题有类似于：研究基础、工作条件与可行性分析。如果提供了对应的参考资料，重点大段的表达阐述用户提供的参考资料部分；如果用户提供了参考资料，要用2大段重点阐述、大量参考用户提供的参考资料，如果没有，不要随意生成！【这个很明显能看出来】，这部分写作过程中需要引用【预设参考文献】，这部分写作的正文引用的参考文献不要超过10篇。
  13.如果所给标题有类似于：研究团队与协作优势。这部分不要随意编造，一定要可以核实。单位和团队概述：用大段来表达，不要用一小段一小段的生成，分段不超过3段，单位基本信息可以爬取百度百科、维基百科、单位的官方网站、以及其他权威来源内容来补充，单位研究团队从权威文章、杂志、文献中获取。不要随意撰写，这部分如果出错很容易被看出来，所以，高压线：如果你没有爬取到，或者不知道相关信息，不要编造！，不要编造！，不要编造！）【这部分不要参考文献】。
  14.如果所给标题有类似于要求：年度研究计划、研究进度与经费预算。【这部分不要引用参考文献】【这部分不要参考文献】
    * 时间安排：请将时间安排以年度为单位进行整理，并增加一个基于整体研究进度的中期考核时间节点和具体的考核内容（考核内容要具体，不能是空洞的，要具有可操作性和可衡量）
    * 按年度或项目周期进行阶段划分
    * 每阶段需要完成的关键任务、评估指标与预期成果。
    (要求：计划合理可行、时间节点明确、成果具体可考核，必须使用大段落或清晰的列表形式呈现，每个子标题下主要论述不超过3段)
    • 年度研究计划 (【按自然年度/项目执行期分段详细列出】)【这部分不要引用参考文献】
    • 【大段落或列表清晰呈现】 按照项目执行的自然年度 (例如：2025.01-2025.12, 2026.01-2026.12, ...) 详细列出每年度拟开展的主要研究内容、工作重点、关键实验节点、预期达到的具体进展和阶段性目标。
    • 【必须包含】 计划中应明确体现学术交流安排，如：计划参加的国内外重要学术会议（可列出目标会议名称）、拟进行的口头报告/墙报交流、计划开展的国际合作与交流活动（如合作研究、互访等）。
    • 计划需与前面的研究内容和研究方案紧密衔接，时间安排应合理、可行、循序渐进。可设置中期检查节点 (例如，项目执行期过半时) 及相应的考核内容。
    • 示例格式 (按年度)：
    • 第一年度 (YYYY.MM - YYYY.MM):
    • 主要研究任务：[列出具体任务1, 任务2... 与研究内容对应]
    • 关键节点/实验：[如完成XX模型构建与验证, 完成XX初步筛选]
    • 预期进展：[如获得XX初步数据, 验证XX可行性]
    • 学术交流：[计划参加XX国内会议, 准备XX中期报告]
    • 第二年度 (YYYY.MM - YYYY.MM): [同上]
    • 第三年度 (YYYY.MM - YYYY.MM): [同上，通常包括深入验证、机制解析、数据整合、论文撰写等]
    • (根据项目周期调整年限)
  15.如果输出内容需要涉及经费申请说明 (要求：符合预算合理、必要、详细，结合网络最佳实践，给出具体金额细节)，【这部分不要引用参考文献】
    • 【如果用户给定的主题和研究内容中，有经费申请说明，并且是科研项目，则需要参照科研资助类最新预算模板和编制说明】
    • 经费预算表： 按照科研资助类基金最新的经费预算表格科目（如设备费、材料费、测试化验加工费、燃料动力费、差旅/会议/国际合作与交流费、出版/文献/信息传播/知识产权事务费、劳务费、专家咨询费、其他支出等）列出各项预算金额。
    • 预算说明（详细测算依据）： 【极其重要】 对每一项预算支出科目，必须提供详细的测算依据和必要性说明。
    • 设备费： 购置单价50万元以上设备的必要性、与研究任务的相关性、现有设备无法满足需求的理由、设备共享使用的承诺等。租赁/试制/改造/维护费的测算细节。
    • 材料费： 需大致估算主要试剂、耗材、实验动物等的种类、数量、单价，说明其与研究方案的直接联系。
    • 测试化验加工费： 列出需要外协完成的主要测试项目、测试次数/样本量、单价/收费标准、选择该测试单位的理由等。
    • 差旅/会议/国际合作与交流费： 需结合年度研究计划中的学术交流安排，说明调研/参会/合作交流的目的地、天数、次数、人数、标准等。
    • 劳务费： 明确支付对象（博士后、研究生、项目聘用人员），说明人数、月数、发放标准（参照国家和依托单位规定）。
    • 专家咨询费： 说明咨询专家的领域、人次、咨询内容、发放标准。
    • 总预算需与分项的申请金额总和一致，各项支出需与研究任务量、研究方案、研究周期和团队规模高度匹配，做到经济合理、实事求是、依据充分。
    •   ⚠️！！！不要出现那种正文中引用序号最大数和参考文献列表的序号最大数不一致的情况，如果出现，则视为重大错误，直接杀掉！！！
  16.如果有转化应用成果要列出可能的成果转化方式及意义（社会效应、经济效益）
    预期研究成果 (【大段落或分点列出，要求具体、量化、高质量，可考核，分段不超过3段】)【最多5篇参考文献】
    • 【详细具体地列出】 明确列出项目完成时预期能够产出的所有形式的成果，并尽可能量化。需与项目资助强度、研究目标和研究内容相匹配，体现高水平研究的产出。
    • 学术论文（不要过多）： 计划发表高水平SCI/SSCI/EI收录论文 [明确数量（不要过多）] 篇，其中在 [学科领域公认的重要期刊/JCR Q1/Q2区/中科院分区X区] 期刊发表 [明确数量] 篇。可列举1-3个代表性目标期刊。
    • 学位论文（不要过多）： 计划培养博士研究生 [数量] 名，硕士研究生 [数量] 名，支撑其完成高质量学位论文。
    • 专利/软件著作权 (若适用)： 计划申请发明专利 [数量] 项 [简述专利核心内容/方向]；申请/登记软件著作权 [数量] 项 [简述软件功能]。
    • 学术交流成果： 计划在国内外重要学术会议上做邀请报告/口头报告 [数量] 次，墙报展示 [数量] 次。
    • 其他成果 (根据实际情况列出)： 如研究报告、决策咨询报告、专著章节/书稿、新理论/模型/方法/技术体系、关键部件/样品/数据库、技术标准草案、成果推广应用证明等。
    • 【成果质量与水平描述】 简要说明预期成果的学术水平、创新性和潜在影响力。
    • 【成果考核指标】 明确以上述预期成果作为主要的考核指标，考核方式包括但不限于：论文接收函/在线发表证明、专利受理/授权通知书、研究生学位证明、会议邀请函/程序册、软件登记证书、成果应用证明等。确保成果是可检查、可衡量的。
    • (可选) 成果应用前景： 简要阐述研究成果可能的转化应用前景及潜在的社会经济效益（呼应研究意义）。

  17.如果所给标题有类似于要求：伦理及法律法规：[这部分是重点内容，但是也不是所有的课题或者方案都需要，要根据用户给定的主题和研究内容，决定是否需要，因为这部分材料要求比较敏感]
    * 根据用户给定的主题和研究内容，决定是否需要实验动物的伦理审查（不同实验动物的选择、替代与保护。）
    * 如果用户没有提供伦理批准号，不要写具体的批准号出来
    * 生物类课题需要考虑，生物安全与动物福利措施[这部分要根据用户给定的主题和研究内容，决定是否需要，不要随意编造和编写]
    * 临床或人体样本研究的伦理审查[这部分要根据用户给定的主题和研究内容，决定是否需要，不要随意编造和编写]
    * 知情同意与隐私保护（这部分要根据用户给定的主题和研究内容，决定是否需要）
    * 知识产权与成果保护（这部分要根据用户给定的主题和研究内容，决定是否需要）
    * 研究成果的产权归属、专利申请与保护策略（这部分要根据用户给定的主题和研究内容，决定是否需要）
    * 对相关法规、政策的合规性说明（这部分要根据用户给定的主题和研究内容，决定是否需要）

  18.如果所给标题有类似于要求：总结与展望（扩充偏于大段描述，少分段，少于三段）【最多3篇参考文献】
    理论与方法的综合
    归纳以上研究内容、方法以及可能的创新之处，强调多学科交叉的重要性。
    强调本课题对整个主题和研究领域的重要贡献。
    展望在相关主题、研究内容等方面的深远意义。
    对下一步可能的持续研究计划或新课题方向做出简要预判。

## 输出内容的其他要求
  1.形式：结合大纲和已经生成的内容来输出本次的内容保证逻辑一致且可连贯成完整文章。
  2.篇幅：根据需要将展开深度、描述性内容，力求段落完整、详实。
  3.写作风格：以学术性、严谨性为主，可适当加入实例、图表设计说明或研究结果的可视化思路。
  4.深度：需要结合具体研究领域进行进一步展开，如互作机制等，可以针对某些焦点问题进行深入论述。
  5.各科学或者研究计划、方案书的问题属性的具体内涵如下：
    （1）"鼓励探索、突出原创"是指科学问题源于科研人员的灵感和新思想，且具有鲜明的首创性特征，旨在通过自由探索产出从无到有的原创性成果。
    （2）"聚焦前沿、独辟蹊径"是指科学问题源于世界科技前沿的热点、难点和新兴领域，且具有鲜明的引领性或开创性特征，旨在通过独辟蹊径取得开拓性成果，引领或拓展科学前沿。
    （3）"需求牵引、突破瓶颈"是指科学问题源于国家重大需求和经济主战场，且具有鲜明的需求导向、问题导向和目标导向特征，旨在通过解决技术瓶颈背后的核心科学问题，促使基础研究成果走向应用。
    （4）"共性导向、交叉融通"是指科学问题源于多学科领域交叉的共性难题，具有鲜明的学科交叉特征，旨在通过交叉研究产出重大科学突破，促进分科知识融通发展为知识体系。
  6.篇幅要求：必须严格达到字数要求，不要低于也不要超过太多（此字数仅包括中文和英文的文字数不算Markdown格式的符号）；
  7.输出内容必须使用markdown格式。
  8.图表需求：合适的地方需要包含图表（如研究框架、技术路线、实验设计示意图等），最好有研究框架、技术路线、实验设计示意图更好；
  9.输出内容里面引用参考文献时，请严格遵守下列正确的格式，禁止错误格式：
    正确格式如下：
        -而晚期诊断则骤降至不足5%[11]。
    错误格式如下：
        -而晚期诊断则骤降至不足5%\[11\]


────────────────────────────────────────────────
## 以下是部分我要给你的信息
────────────────────────────────────────────────
• 课题主题/名称: {name}，全篇生成的核心部分，所有生成的内容都要围绕这个主题
 ────────────────────────────────────────────────
• 大纲: {outline}
────────────────────────────────────────────────
• 写作语言风格: 学术论文风格
 ────────────────────────────────────────────────
请在段落输出中参考下列的网络检索内容，这部分是重点参考部分，而且要有对应的参考依据：{contexts}。
 
【预设参考文献】，这一部分中很多内容是多余的，你要生成的目标这一段或者这几段文本在严格遵守字数要求的前提下自己选择参考，但是每一段话的引用标号最多不要超过2个。
【字数要求】再次强调：这一部分段落你要撰写的字数要严格控制，不可以超过{num_count}字。
"""
THIS_TIME_INFO_PROMPT = """
## 本次任务背景与目标
（1）核心任务:根据用户的研究主题、网络检索信息、【预设参考文献】、已经生成出来的内容、后续段落的标题和其他一些附加信息（比如团队信息、主体信息等）去生成一份深度研究报告书或者方案书的一个给定段落。
（2）这是你要生成的内容的标题:{para_title}{para_content}。
（3）下面三个反引号里面内容是前面我已经撰写过的段落内容（需要你保证跟前面衔接好、行文连贯、不要显得硬衔接）: ```{generated_text}```
（4）字数控制:这一部分段落撰写的字数严格控制，不可以超过{num_count}字。 
（5）禁止事项:
    - 任何非正文开场白、角色自述、解释性语句一律禁止。
    - 输出必须直接从内容开始，并且不要有其他的信息，直接输出内容。
    - 生成的内容里面禁止包含标题内容:{para_title}。
"""
# 最终报告的系统提示词 - 中文研究专家
REPORT_SYSTEM_PROMPT = """角色：你是一位资深研究员和文档材料撰写专家，具有很好的国际视野和深厚的专业度，研究能力和水平达到院士水平并具有数十年的应用或者实验操作经验，紧跟当前研究技术前沿邻域。基于以下收集到的资料和原始查询，撰写一份全面、结构清晰且详细的项目申报书，充分解答查询内容。请使用用户提供的主要语言撰写，并保持语言统一，不要有大量的中英韩繁体混杂，确保包含所有相关的见解和结论，不要添加额外的评论。"""


def iteration_prompt(
    title: str,
    content: str,
    generated: str,
    count: int
):
    return THIS_TIME_INFO_PROMPT.format(
        para_title=title,
        para_content=content,
        generated_text=generated,
        num_count=count
    )


async def final_report_background_prompt(
    project: ProjectConfigResponse2,
    contexts: List[str],
    num_count: int
):
    outline_path = project.manual_modified_outline or project.ai_generated_outline
    try:
        upload_file.download_file(outline_path)
        outline = read_file_content(outline_path)
    finally:
        upload_file.remove_local_file(outline_path)

    return FINAL_REPORT_BACKGROUND_PROMPT.format(
        literature_prompt=LITERATURE_PROMPT,
        name=project.name,
        outline=outline,
        contexts=contexts,
        num_count=num_count
    )


async def generate_prompt(
    project: ProjectConfigResponse2,
    contexts: List[str],
    literatures: List[LiteratureResponse],
    generated_text: str,
    title: str,
    content: str,
    text_count: int
):
    target = iteration_prompt(
        title=title,
        content=f"，以及对应的这个标题的大纲要求::{content}" if content else '',
        generated=generated_text,
        count=text_count
    )
    literature_text = final_report_reference_prompt(literatures=literatures)
    background_text = await final_report_background_prompt(
        contexts=contexts,
        project=project,
        num_count=text_count
    )
    return f"{target}\n{literature_text}\n{background_text}"

def markdown_to_tree(text: str, total_count: int) -> List[OutlineNode]:
    lines = text.splitlines()
    nodes = []
    stack: List[OutlineNode] = []

    # 正则匹配标题行，如 "# 一级标题"、"## 二级标题"
    header_pattern = re.compile(r'^(#{1,6})\s+(.*)')

    for line in lines:
        header_match = header_pattern.match(line)
        if header_match:
            level = len(header_match.group(1))
            title = header_match.group(2).strip()
            node = OutlineNode(**{
                "title": title,
                "content": "",
                "children": [],
                "count": 0,
                "level": level  # 仅内部使用，最后会删除
            })

            while stack and stack[-1].level >= level:
                stack.pop()
            if stack:
                stack[-1].children.append(node)
            else:
                nodes.append(node)

            stack.append(node)
        elif line.strip() != "":
            if stack:
                if stack[-1].content:
                    stack[-1].content += "\n" + line
                else:
                    stack[-1].content = line

    def assign_count(nodes: List[OutlineNode], total: int):
        if not nodes:
            return

        abstract_nodes = [n for n in nodes if n.title.strip().upper() in {"ABSTRACT", "摘要"}]
        other_nodes = [n for n in nodes if n.title.strip().upper() not in {"ABSTRACT", "摘要"}]

        abstract_total = 500 * len(abstract_nodes)
        remaining_total = max(total - abstract_total, 0)

        # 分配摘要
        for node in abstract_nodes:
            node.count = 500
            assign_to_children(node)

        # 平均分配剩余段落
        if other_nodes:
            per = remaining_total // len(other_nodes)
            for node in other_nodes:
                node.count = per
                assign_to_children(node)

    def assign_to_children(node: OutlineNode):
        if not node.children:
            return
        per = node.count // len(node.children)
        for child in node.children:
            child.count = per
            assign_to_children(child)

    assign_count(nodes, total_count)

    def set_count_to_0_for_parents(nodes: List[OutlineNode]):
        for node in nodes:
            if node.children:
                node.count = 0
                set_count_to_0_for_parents(node.children)

    set_count_to_0_for_parents(nodes)
    return nodes

async def search_related_information(
    research: Research,
    config_response: ProjectConfigResponse2,
    current_user: UserResponse,
    model_info: ModelConfigBase,
    open_literature_summary: Optional[bool] = True,
):
    try:
        await search_to_carry_info(
            research=research,
            config_response=config_response,
            current_user=current_user,
            # 搜索关键词的引擎的标识符
            search_method_flag=None,
            open_literature_summary=open_literature_summary,
            model_info=model_info
        )
    except Exception as e:
        logger.error(e)


async def generate_report(
    project_id: str,
    current_user: UserResponse,
    skip_search: Optional[bool] = False,
    research_id: Optional[str] = None
):
    project = await ProjectConfig.filter(
        id=project_id
    ).prefetch_related("model").first()
    outline_path = project.ai_generated_outline or project.manual_modified_outline
    try:
        upload_file.download_file(outline_path)
        outline = remove_markdown_h1_and_text(
            all_text=read_file_content(outline_path),
            title=project.name,
            combine_title=False
        )
    finally:
        upload_file.remove_local_file(outline_path)
    paragraph_tree = markdown_to_tree(outline, project.word_count_requirement)
    # 创建一个研究实例来处理报告生成
    # 注意：不再使用api_key_id参数
    research = await Research.filter(id=research_id).first() if skip_search else await Research.create(
        query=project.name,
        search_queries=[],
        contexts=[],
        status=ResearchStatus.PENDING
    )
    project.research = research
    project.status = ProjectConfigStatus.REPORT_GENERATING.value
    is_diff_count = False
    # 获取用户使用记录（后续更新用）
    user_report_usage = await UserReportUsage.filter(user_id_id=current_user.id, is_deleted=False).first()

    # async def complete_callback(open_router_id: str):  # 存储大纲内容
        # await create_workflow(
        #     project_id=project_id,
        #     content=relative_path,
        #     name="GENERATED_CONTENT_FIRST_TIME",
        #     current_user=current_user
        # )
        # try:
        #     temp = await get_generation_result(
        #         generation_id=open_router_id,
        #         api_key=project.model.api_key
        #     )
        #     project.report_tokens_consumed += temp.data.tokens_completion + temp.data.tokens_prompt
        #     await project.save()
        # except Exception as e:
        #     print(f"{e}")
        #     project.report_tokens_consumed += 0
        #     await project.save()

    async def error_callback(error):
        # 处理错误
        logger.error(f"生成报告时发生错误: {error}")
        await report_content_manager.add_content(
            str(project.id), error, ContentStatus.ERROR.value)
        project.status = ProjectConfigStatus.REPORT_FAILED.value
        await project.save()

    async def callback(content: str):
        nonlocal is_diff_count
        if not is_diff_count:
            logger.info("更新用户使用次数")
            # 更新用户使用次数
            user_report_usage.used_count += 1
            await user_report_usage.save()
            is_diff_count = True
        await report_content_manager.add_content(
            str(project.id), content)
    # 添加一个空字符串，用于触发流式处理
    await report_content_manager.add_content(
        str(project.id), "")
    await project.save()
    project_data = await get_one_project_config(project_id=project.id)
    asyncio.create_task(async_generate_report(
        research=research,
        project=project_data,
        paragraph_tree=paragraph_tree,
        current_user=current_user,
        callback=callback,
        error_callback=error_callback,
        skip_search=skip_search
    ))
    await llm_interaction_instance.start_interaction(
        scene=ManagerScene.PAPER_GENERATE,
        id=project_id
    )
    # result = await call_llm(
    #     messages=[{
    #         "role": "user",
    #         "content": "给我返回五个基础求极限的公式"
    #     }],
    #     apiKey=project_data.model.api_key,
    #     apiUrl=project_data.model.api_url,
    #     model="deepseek/deepseek-r1"
    # )
    # save_text_to_file(handle_text_before_use(result), project_data.ai_generated_report)
    return paragraph_tree


async def async_generate_report(
    research: Research,
    project: ProjectConfigResponse2,
    paragraph_tree: List[OutlineNode],
    current_user: UserResponse,
    callback: Callable,
    # complete_callback: Callable,
    error_callback: Callable,
    skip_search: Optional[bool] = False
):
    starttime = datetime.now()
    logger.info(f"报告开始时间：{starttime.strftime('%Y-%m-%d %H:%M:%S')}")
    # 暂存的文档内容
    temp_text = ""
    model_info = await get_user_model(
        current_user=current_user,
        use_case=UseCase.PAPER_GENERATE.value
    )
    api_url = model_info.api_url
    api_key = model_info.api_key
    model = model_info.model_name
    is_thinking = model_info.is_thinking
    not skip_search and await search_related_information(
        research=research,
        config_response=project,
        current_user=current_user,
        model_info=model_info
    )
    literatures = await get_literature_by_project(str(project.id))
    logger.info(f"材料ID：{project.id}，材料研究主题：{project.name}总共收集到：{len(literatures)}条文献。")
    config_db = await ProjectConfig.filter(
        id=project.id
    ).prefetch_related("model").first()
    # api_url = config_db.model.api_url
    # api_key = config_db.model.api_key
    # model = config_db.model.model_name
    project_folder = f"llm_file/{project.id}"
    token_consume = {
        "input": 0,
        "output": 0
    }
    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    timestamp = int(datetime.now().timestamp() * 1000)
    file_name = f"report_{timestamp}.txt"
    if project.name:
        file_name = sanitize_filename(
            f"report_{project.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"
    eng_title = await call_llm(
        messages=[
            {
                "role": "user",
                "content": TRANSLATE_PAPER_TITLE_PROMPT.format(
                    title=config_db.name
                )
            }
        ],
        apiKey=api_key,
        apiUrl=api_url,
        model=model,
        is_thinking=is_thinking
    )

    async def traverse_tree(node: OutlineNode):
        nonlocal temp_text
        nonlocal callback
        nonlocal error_callback
        nonlocal current_user
        nonlocal project
        nonlocal token_consume
        if node is None:
            return
        elif node.title:
            title_level = "#" * node.level
            await report_content_manager.add_content(
                str(project.id), f"{title_level} {node.title}\n\n")
            if node.count > 0:
                user_prompt = await generate_prompt(
                    project=project,
                    literatures=literatures,
                    generated_text=temp_text,
                    title=node.title,
                    content=node.content,
                    contexts=research.contexts,
                    text_count=node.count
                )
                async def middle_error_callback(error: str):
                    # 如果没有接收到有效内容就重新来一遍
                    if error == ErrorType.NOT_VALID_TEXT.value:
                        await traverse_tree(node)
                    else:
                        await error_callback(error)
                        raise Exception(error)
                async def middle_callback(token_data: dict, content: str):
                    nonlocal node
                    nonlocal title_level
                    nonlocal temp_text
                    nonlocal current_user
                    nonlocal project
                    nonlocal token_consume
                    pipeline = [
                        remove_word_count_tag,
                        remove_after_reference,
                        lambda text: remove_markdown_heading(text, node.title),
                        lambda text: adjust_markdown_heading_levels(text, node.level + 1),
                        remove_markdown_code_fence
                    ]
                    for fn in pipeline:
                        content = fn(content)
                    content = await generate_images(
                        current_user=current_user,
                        content=content,
                        name=project.name,
                        api_url=api_url,
                        api_key=api_key,
                        model=model,
                        is_thinking=is_thinking
                    )
                    temp_text += f"\n\n{title_level} {node.title}\n\n{content}\n\n"
                    logger.info(f"目前为止的完整文章：\n\n{temp_text}")
                    token_consume["input"] += token_data.get("input")
                    token_consume["output"] += token_data.get("output")
                    logger.info(f"本段生成消耗token：{token_data}")
                messages = [
                    {
                        "role": "system",
                        "content": REPORT_SYSTEM_PROMPT
                    },
                    {
                        "role": "user",
                        "content": user_prompt
                    }
                ]
                await stream_llm_and_save(
                    messages=messages,
                    user=current_user,
                    callback=callback,
                    complete_callback=middle_callback,
                    error_callback=middle_error_callback,
                    flag=CallLLMFlag.GENERATE_REPORT.value,
                    model=model,
                    apiKey=api_key,
                    apiUrl=api_url,
                    is_thinking=is_thinking,
                    related_id=project.id
                )
            else:
                temp_text += f"{title_level} {node.title}\n\n"
            await report_content_manager.add_content(
                str(project.id), "\n\n")
        for child in node.children:
            await traverse_tree(child)
    try:
        await traverse_tree(OutlineNode(**{
            "title": "",
            "content": "",
            "children": paragraph_tree,
            "count": 0,
            "level": 0  # 仅内部使用，最后会删除
        }))
        reference_data = normal_literature(literatures)
        temp_text += reference_data
        await report_content_manager.add_content(str(project.id), reference_data)
        logger.info("准备把正文写到文件里面了。")
        logger.info(temp_text)
        replace_title = remove_markdown_h1_and_text(
            all_text=temp_text,
            title=config_db.name,
            eng_title=eng_title
        )
        completed_text = handle_text_before_use(replace_title)
        upload_file.write(path=relative_path, content=completed_text)
        logger.info("正文已经写到文件里面了。")
        logger.info(f"材料ID：{config_db.id} 材料名：{config_db.name}的正文消耗token：{token_consume},")
        endtime = datetime.now()
        logger.info(
            f"报告生成结束时间: {endtime.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(endtime - starttime).total_seconds()}秒")
        logger.info(f"正文长度：{len(temp_text)}")
        # 增加机构的
        config_db.status = ProjectConfigStatus.REPORT_GENERATED.value
        config_db.report_generation_time = datetime.now()  # 确保使用不带时区的日期时间
        config_db.manual_modified_report_time = None
        config_db.manual_modified_report = None
        config_db.ai_generated_report = relative_path
        await config_db.save()
    except Exception as e:
        config_db.status = ProjectConfigStatus.REPORT_FAILED.value
        await config_db.save()
        logger.error(f"生成报告报错：{str(e)}")
    await report_content_manager.clear_project(str(project.id))
    await llm_interaction_instance.end_interaction(
        scene=ManagerScene.PAPER_GENERATE,
        id=project.id
    )

def normal_literature(literatures: List[LiteratureResponse]):
    logger.info(f"参考文献列表：{literatures}")
    reference_list = []
    for index,item in enumerate(literatures):
        url = ""
        if item.doi:
            url = f"https://doi.org/{item.doi}"
        elif item.url:
            url = item.url
        else:
            url = "#"
        logger.info(f"url: {url}")
        reference_list.append(f"[{index+1}] [{item.citation_format}]({url})")
    reference_list = ("\n\n").join(reference_list)
    return f"\n\n## 参考文献\n\n{reference_list}"

async def download_file(
    project_id: str,
    current_user: UserResponse,
    # outline report
    flag: Optional[str] = "outline"
):
    if not project_id:
        raise Exception("项目ID不能为空")
    project = await ProjectConfig.filter(id=project_id,is_deleted=False).prefetch_related("user", "user__organization").first()
    if not project:
        raise Exception(ProjectConfigError.NOT_RECORD.value)
    authed = await is_user_authed(project.user.id, current_user.id)
    if not authed:
        raise Exception(ErrorType.FORBIDDEN.value)
    file_path = ""
    if flag == 'outline':
        file_path = project.manual_modified_outline or project.ai_generated_outline
    elif flag == 'report':
        file_path = project.manual_modified_report or project.ai_generated_report
    elif flag == 'hallucination':
        file_path = project.hallucination_report
    if not file_path:
        raise Exception(ErrorType.FILE_NOT_EXIST.value)
    try:
        upload_file.download_file(file_path)
        return convert_markdown_to_docx(file_path)
    except Exception as e:
        logger.error(f"markdown转word报错:{str(e)}")
        raise e
    finally:
        upload_file.remove_local_file(file_path)
async def generate_images(
    current_user: UserResponse,
    content: str,
    name: str,
    api_url: str,
    api_key: str,
    model: str,
    is_thinking: Optional[bool] = False
):
    try:
        images_list = extract_chart_placeholders(content)
        for item in images_list:
            keywords = await get_search_keywords(
                title=name,
                description=item,
                current_user=current_user,
                api_url=api_url,
                api_key=api_key,
                model=model,
                is_thinking=is_thinking
            )
            figure_type = get_figure_type(item)
            if not figure_type:
                content = replace_text_with_markdown_image_remove(
                    input_text=content,
                    target_text=item,
                    image_url=""
                )
                continue
                # raise Exception("不在支持的图表范围内")
            url = ""
            try:
                data = await get_figure_data(
                    user=current_user,
                    keywords=keywords,
                    description=item,
                    figure_type=figure_type,
                    api_url=api_url,
                    api_key=api_key,
                    model=model,
                    is_thinking=is_thinking
                )
                logger.info(data)
                url = await get_picture_url(
                    type=figure_type,
                    data=data
                )
            except Exception as e:
                logger.error(f"报错：f{str(e)}")
            content = replace_text_with_markdown_image_remove(
                input_text=content,
                target_text=item,
                image_url=url
            )
        return content
    except Exception as e:
        logger.error(str(e))
        raise e
async def get_figure_data(
    user: UserResponse,
    keywords: List[str],
    description: str,
    figure_type: str,
    api_url: str,
    api_key: str,
    model: str,
    is_thinking: Optional[bool] = False
):
    # model_data = await get_user_model(
    #     current_user=user,
    #     use_case=UseCase.PROJECT_CONFIG_NEED
    # )
    # 從系統配置中動態獲取圖片數據搜索引擎配置
    from app.services.system_config_service import system_config_service
    picture_search_engine_config = await system_config_service.get_config("PICTURE_DATA_SEARCH_ENGINE")
    if not picture_search_engine_config:
        picture_search_engine_config = settings.PICTURE_DATA_SEARCH_ENGINE
    
    # 檢查是否使用 Jina 直接內容模式
    jina_direct_content_config = await system_config_service.get_config("JINA_DIRECT_CONTENT")
    jina_direct_content_enabled = jina_direct_content_config and jina_direct_content_config.upper() == "ON"
    
    contexts: List[str] = []
    for keyword in keywords:
        limit = settings.FIGURE_GOOGLE_SEARCH_LIMIT
        logger.info(f"limit 类型: {type(limit)}, 值: {limit}")
        logger.info(f"limit: {limit}")
        
        # 檢查是否使用 Jina 且開啟直接內容模式
        if "jina" in picture_search_engine_config and jina_direct_content_enabled:
            try:
                # 使用 Jina 直接內容模式
                from app.services.search_service import perform_jina_search
                contents = await perform_jina_search(
                    query=keyword,
                    limit=limit,
                    contain_origin=False,
                    direct_content=True
                )
                logger.info(f"Jina 直接內容模式，獲取到 {len(contents)} 條內容")
                # 处理新的返回格式：兼容对象列表和字符串列表
                if contents and isinstance(contents[0], dict):
                    # 新格式：對象列表，从 content 字段提取文本
                    contexts.extend([item.get("content", "") for item in contents if item.get("content")])
                else:
                    # 旧格式：字符串列表
                    contexts.extend(contents)
            except Exception as e:
                logger.warning(f"Jina 直接內容模式失敗，回退到 URL 模式: {str(e)}")
                # 回退到 URL 模式
                urls = await perform_serpapi_search(
                    query=keyword,
                    limit=limit,
                    search_engine=picture_search_engine_config,
                    contain_origin=False
                )
                logger.info(f"URL 模式，urls列表: {urls}")
                for url in urls:
                    page_content = await fetch_webpage_text_async(url)
                    logger.info(f"page_content: \n\n{page_content}")
                    contexts.append(page_content)
        else:
            # 使用 URL 模式
            urls = await perform_serpapi_search(
                query=keyword,
                limit=limit,
                search_engine=picture_search_engine_config,
                contain_origin=False
            )
            logger.info(f"URL 模式，urls列表: {urls}")
            for url in urls:
                page_content = await fetch_webpage_text_async(url)
                logger.info(f"page_content: \n\n{page_content}")
                contexts.append(page_content)
    
    web_data = "\n\n".join(contexts)
    # 调用LLM
    messages = [
        {
            "role": "user",
            "content": generate_figure_data_prompt(
                description=description,
                web_data=web_data,
                demo=get_demo_data(figure_type)
            )
        }
    ]
    response = await call_llm(
        messages=messages,
        flag="提取绘制图需要的JSON格式数据",
        apiKey=api_key,
        apiUrl=api_url,
        model=model,
        is_thinking=is_thinking
    )
    try:
        try:
            return json.loads(response)
        except Exception as e:
            pattern = r"```json\s*(\{.*?\})\s*```"  # 使用非贪婪匹配并捕获 {...} 内容
            match = re.search(pattern, response, re.DOTALL)  # DOTALL 让 "." 匹配换行符

            if match:
                json_str = match.group(1)
                return json.loads(json_str)
            raise Exception("没有解析到数据")
    except Exception as e:
        logger.error(f"JSON解析报错：{e}")
        raise e
async def get_picture_url(
    data: dict[str, any],
    type: str
):
    url = ""
    if type == '柱状图':
        url = create_bar_chart(
            data=data["data"],
            title=data["title"],
            x_label=data["x_label"]
        )
    elif type == '折线图':
        url = create_line_chart(
            data=data["data"],
            title=data["title"],
            x_label=data["x_label"],
            x_data=data["x_data"]
        )
    elif type == '饼图':
        url = create_pie_chart(
            data=data["data"],
            title=data["title"]
        )
    elif type == '散点图':
        url = create_scatter_plot(
            x_label=data["x_label"],
            x_data=data["x_data"],
            y_label=data["y_label"],
            y_data=data["y_data"],
            title=data["title"]
        )
    if url:
        logger.info("url有值")
        url = settings.WEB_BASE_URL + "/api/statics/" + url
        logger.info(f"图片地址: {url}")
        return url
    error_msg = "生成图表报错"
    logger.error(error_msg)
    raise Exception(error_msg)