from datetime import datetime
from typing import List

from app.api.schemas.upload_file import UploadFileResponse
from app.api.schemas.user import UserResponse
from app.core.config import settings
from app.core.logging import get_logger
from app.models.upload_file import UploadFile as UploadFileModel
from app.utils.enum import UploadFileError

logger = get_logger(__name__)

# 动态导入本地或OSS实现
if settings.FILE_MODE == 'oss':
    from app.api.repository.oss_upload_file import (
        save_upload_file,
        write,
        download_file,
        remove_local_file,
    )
else:
    from app.api.repository.local_upload_file import (
        save_upload_file,
        write,
        download_file,
        remove_local_file,
    )


async def get_file_content_by_id(file_id: str) -> UploadFileResponse:
    """
    根据文件id获取文件记录。
    :param file_id: 文件的UUID
    :return: 文件内容字符串
    """
    try:
        upload_file = await UploadFileModel.filter(
          id=file_id,
          is_deleted=False
        ).first()
        if not upload_file:
          raise Exception(f"{UploadFileError.NOT_RECORD.value}")
        return UploadFileResponse.model_validate(upload_file, from_attributes=True)
    except Exception as e:
        error_msg = f"{UploadFileError.EXCEPTION_WHEN_QUERY.value}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)
async def get_file_content_by_ids(file_ids: List[str]) -> List[UploadFileResponse]:
    """
    根据文件ids获取文件列表。
    :param file_id: 文件的UUID
    :return: 文件内容字符串
    """
    try:
        upload_file = await UploadFileModel.filter(
          id__in=file_ids,
          is_deleted=False
        ).all()
        if not upload_file:
          raise Exception(f"{UploadFileError.NOT_RECORD.value}")
        return [UploadFileResponse.model_validate(item, from_attributes=True) for item in upload_file]
    except Exception as e:
        error_msg = f"{UploadFileError.EXCEPTION_WHEN_QUERY.value}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


async def delete_upload_file(
    file_id: str,
    current_user: UserResponse
) -> bool:
    """
    删除指定ID的上传文件记录，同时删除物理文件。
    :param file_id: 要删除的文件UUID
    :param current_user: 当前用户信息（用于权限验证）
    :return: 删除成功返回True，失败抛出异常
    """
    try:
        # 查找文件记录
        upload_file = await UploadFileModel.filter(
            id=file_id,
            is_deleted=False,
            user_id=current_user.id  # 只能删除自己上传的文件
        ).first()
        
        if not upload_file:
            raise Exception(f"{UploadFileError.NOT_RECORD.value}")
        
        # 删除物理文件
        # file_path = upload_file.file_path
        # abs_path = os.path.join(os.getcwd(), file_path)
        
        # try:
        #     if os.path.exists(abs_path):
        #         os.remove(abs_path)
        #         logger.info(f"物理文件已删除: {file_path}")
        #     else:
        #         logger.warning(f"物理文件不存在，跳过删除: {file_path}")
        # except Exception as file_error:
        #     logger.error(f"删除物理文件失败: {file_path}, 错误: {str(file_error)}")
        #     # 物理文件删除失败不影响数据库记录的删除，继续执行
        
        # 软删除数据库记录
        await UploadFileModel.filter(id=file_id).update(
            is_deleted=True,
            deleted_at=datetime.now()
        )
        
        logger.info(f"文件记录已删除: {file_id}, 原文件名: {upload_file.file_name}")
        return True
        
    except Exception as e:
        error_msg = f"删除文件失败: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)