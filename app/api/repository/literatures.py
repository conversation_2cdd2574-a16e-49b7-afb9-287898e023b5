from app.models.literatures import Literature
from app.api.schemas.literatures import LiteratureCreate, LiteratureResponse, LiteratureBase
from datetime import datetime
from typing import List, Optional
from app.models.project_configs import ProjectConfig
from app.core.logging import get_logger
import re

# 获取logger实例
logger = get_logger(__name__)

# 获取第一作者的名字
def first_author_name(data: str):
    first_author = handle_data(data.split(",")[0])
    # 如果是中文字
    if re.match(r"[\u4e00-\u9fff]+", first_author):
        return first_author[1] if len(first_author) > 1 else first_author
    else:
        parts = first_author.split()
        return parts[0] if parts else ""

# 格式化统一处理字符串
def handle_data(data: Optional[str]):
    return (data or "").lower().strip()

async def literature_is_exist(literature: LiteratureCreate) -> Optional[LiteratureResponse]:
    list_data = await Literature.filter(
        research_id=literature.research_id
    ).all()
    authors = handle_data(data=literature.authors)
    title = handle_data(data=literature.title)
    url = handle_data(data=literature.url)
    exist = None
    for item in list_data:
        normal_item = LiteratureBase.model_validate(item, from_attributes=True)
        normal_item.title = handle_data(data=normal_item.title)
        normal_item.authors = handle_data(data=normal_item.authors)
        normal_item.url = handle_data(data=normal_item.url)
        # 如果文献标题不一致则直接跳过本次
        if title != normal_item.title:
            continue
        # 如果文献标题一致
        else:
            # 如果两者的url链接一致，则认为是同一篇文献
            if normal_item.url == url:
                exist = item
                logger.info(f"{str(literature)}因为与数据库中数据的url一致被认为是同一篇文献")
                break
            # 如果作者列表一致，则认为是同一篇文献
            if normal_item.authors == authors:
                exist = item
                logger.info(f"{str(literature)}因为与数据库中数据的作者列表一致被认为是同一篇文献")
                break
            # 如果第一作者的名字（不包括姓氏）相同，则认为是同一篇文献
            if first_author_name(data=normal_item.authors) == first_author_name(data=authors):
                exist = item
                logger.info(f"{str(literature)}因为与数据库中数据的第一作者的名字一致被认为是同一篇文献")
                break
            # 如果作者列表的前6个字符一模一样，则认为是同一篇文献
            if normal_item.authors[0:6] == authors[0:6]:
                exist = item
                logger.info(f"{str(literature)}因为与数据库中数据的作者列表的前6个字符一致被认为是同一篇文献")
                break
    return LiteratureResponse.model_validate(exist, from_attributes=True) if exist else None

async def create_literature(literature: LiteratureCreate) -> LiteratureResponse:
    """创建文献"""
    try:
        exist = await literature_is_exist(literature=literature)
        if exist:
            return exist
        data = literature.model_dump()
        data["updated_at"] = datetime.now()
        literature = Literature(**data)
        await literature.save()
        return LiteratureResponse.model_validate(literature, from_attributes=True)
    except Exception as e:
        logger.error(f"创建文献失败:{e}")


async def get_literature_by_id(research_id: str) -> List[LiteratureResponse]:
    """根据研究ID获取文献"""
    literature = await Literature.filter(research_id=research_id).all()
    return [LiteratureResponse.model_validate(item) for item in literature]


async def get_literature_by_project(config_id: str) -> List[LiteratureResponse]:
    """根据研究ID获取项目配置"""
    project_config = await ProjectConfig.filter(id=config_id, is_deleted=False).first()
    if not project_config:
        raise ValueError("项目配置不存在")
    literatures = await Literature.filter(research_id=project_config.research_id).all()
    literature_list = [LiteratureResponse.model_validate(
        item) for item in literatures]
    return literature_list


async def get_literature_length(research_id: str) -> int:
    literature_count = await Literature.filter(research_id=research_id).count()
    return literature_count
