import json
from typing import List, Optional, Callable, Coroutine, Any

from app.api.repository import upload_file
from app.models.upload_file import UploadFile as UploadFileModel
from app.models.co_ai_traces import CoAiTraces
from app.services.llm_service import call_llm
from app.services.prompts import REMOVE_AI_TRACES_PROMPT
from fastapi import APIRouter
import os
from datetime import datetime, timedelta

from app.core.logging import get_logger
from app.api.schemas.ai_traces import CoAiTracesResponse, ProjectConfigAITraces
from app.services.wallet.interface import AGENT
from app.services.wallet.service import wallet_service
from app.utils.utils import (
    is_markdown_title,
    read_file_content,
    split_markdown_text_by_paragraph,
    sanitize_filename,
    PageQuery,
    PageInfo,
    stream_handle_before_send
)
from app.api.schemas.model_config import ModelConfigBase
from app.api.schemas.user import UserResponse
import asyncio
from app.api.repository.user_default_model import get_user_model
from app.utils.enum import UseCase
from app.services.system_config_service import system_config_service
from app.api.repository.upload_file import get_file_content_by_id
from app.utils.enum import CoAiTracesStatus
from app.api.repository.user import is_user_authed
from app.utils.enum import ErrorType
from app.api.repository.project_config import get_one_project_config
from app.api.schemas.role import InsetRole
from app.utils.redis_content_manager import RedisContentManager
from app.utils.enum import ManagerScene
from app.core.redis_client import get_llm_interaction_instance
from app.core.config import settings
from app.services.content_moderation_service import content_moderation_service

llm_interaction_instance = get_llm_interaction_instance()


content_manager = RedisContentManager(
    scene=ManagerScene.AI_TRACES_REMOVE
)
# 获取logger实例
logger = get_logger(__name__)

router = APIRouter()


@router.post("/traces", summary="AI去痕接口")
async def create_ai_traces(
    current_user: UserResponse,
    file_id: str
) -> CoAiTracesResponse:
    """
    AI去痕处理创建记录

    Args:
        file: 上传的文件
        current_user: 当前用户
    Returns:
        ResponseModel: 响应结果
    """
    order_id = None
    business_id = None

    async def success_callback():
        await wallet_service.charge_confirm(current_user.id, order_id, business_id)

    async def error_callback():
        await wallet_service.charge_cancel(current_user.id, order_id, business_id)

    try:
        model_config = await get_user_model(
            current_user=current_user,
            use_case=UseCase.AI_TRACES_REMOVE.value
        )
        file_record = await get_file_content_by_id(file_id)
        file_ext = os.path.splitext(file_record.file_name)[1].lower()
        file_size = 0
        abs_path = os.path.join(os.getcwd(), file_record.file_path)
        if os.path.exists(abs_path):
            file_size = os.path.getsize(abs_path)
        # 2. 创建AI去痕记录（初始状态）
        co_ai_trace_record = await CoAiTraces.create(
            user_id=current_user.id,
            upload_file_id=file_id,
            model_config_id=model_config.id,  # 现在已经是真正的模型实例
            original_filename=file_record.file_name,
            file_size=file_size,
            file_type=file_ext,
            char_count=0,  # 暂时为0，后面会更新
            processed_sections=0,
            original_content="",  # 暂时为空，后面会更新
            status=CoAiTracesStatus.ONGOING.value,
            processing_start_time=datetime.now()
        )
        logger.info(f"AI去痕记录已创建，ID: {co_ai_trace_record.id}")
        order_id = await wallet_service.pre_charge(current_user.id, AGENT.AI_TRACES.value)

        asyncio.create_task(handle_ai_traces(
            file_record.file_path,
            model_config,
            co_ai_trace_record,
            success_callback,
            error_callback
        ))
        # 添加异步任务
        await llm_interaction_instance.start_interaction(
            scene=ManagerScene.AI_TRACES_REMOVE,
            id=co_ai_trace_record.id
        )
        return await get_one_traces_record(co_ai_trace_record.id, current_user)
    except Exception as e:
        logger.error(str(e))
        raise e


async def get_one_traces_record(
    id: str,
    current_user: UserResponse
) -> CoAiTracesResponse:
    """
    获取某条记录

    Args:
        current_user: 当前用户
        id: 记录ID

    Returns:
        CoAiTracesResponse
    """
    try:
        result = await CoAiTraces.filter(id=id, is_deleted=False).prefetch_related("upload_file", "user").first()
        if not result:
            raise Exception("记录不存在")
        if not await is_user_authed(result.user.id, current_user.id):
            raise Exception(ErrorType.FORBIDDEN.value)
        report_detail: List[ProjectConfigAITraces] = []
        if result.detail_report:
            try:
                upload_file.download_file(result.detail_report)
                content = read_file_content(result.detail_report)
                report_detail = json.loads(content)
            except Exception as e:
                logger.error(f"解析文件内容报错:{e}")
            finally:
                upload_file.remove_local_file(result.detail_report)
        return CoAiTracesResponse.model_validate(
            {
                "id": result.id,
                "ai_report": result.processed_content,
                "status": result.status,
                "upload_file": result.upload_file,
                "detail_list": report_detail,

                # 原始文件信息
                "original_filename": result.original_filename,
                "file_size": result.file_size,
                "file_type": result.file_type,
                "char_count": result.char_count,

                # 处理结果
                "processed_sections": result.processed_sections,
                "original_content": result.original_content,
                "processed_content": result.processed_content,
                "processed_path": result.processed_path,  # 重要：包含这个字段
                "detail_report": result.detail_report,

                # 处理状态和时间
                "error_message": result.error_message,
                "processing_start_time": result.processing_start_time,
                "processing_end_time": result.processing_end_time,
                "tokens_consumed": result.tokens_consumed,

                # 时间戳
                "created_at": result.created_at,
                "updated_at": result.updated_at,
                "is_deleted": result.is_deleted,
                "deleted_at": result.deleted_at,
            },
            from_attributes=True
        )
    except Exception as e:
        raise e


async def handle_ai_traces(
    file_path: str,
    model_config: ModelConfigBase,
    co_ai_trace_record: CoAiTraces,
    success_callback: Optional[Callable[[], Coroutine[Any, Any, None]]] = None,
    error_callback: Optional[Callable[[], Coroutine[Any, Any, None]]] = None
):
    """
    处理单个文件的AI去痕

    Args:
        file_path: 文件路径
        model_config: 模型配置对象 (ModelConfigBase类型)
        co_ai_trace_record: AI去痕记录对象，用于更新处理状态
        success_callback: 成功回调
        error_callback： 失败回调
    Returns:
        List[ProjectConfigAITraces]: 去痕处理结果列表
    """
    starttime = datetime.now()
    logger.info(f"AI去痕迹开始时间：{starttime.strftime('%Y-%m-%d %H:%M:%S')}")
    # 从ModelConfigBase对象获取配置信息
    api_key = model_config.api_key
    api_url = model_config.api_url
    model = model_config.model_name
    token_consume = {
        "input": 0,
        "output": 0
    }

    # 不包含标题的所有段落
    left_list: List[ProjectConfigAITraces] = []
    result: List[ProjectConfigAITraces] = []
    total_tokens = 0
    llm_exception = False
    try:
        # 读取报告文件内容
        upload_file.download_file(file_path)
        report_text = read_file_content(file_path)
        sections = split_markdown_text_by_paragraph(report_text)

        logger.info(f"开始处理文件 {file_path}，共 {len(sections)} 个段落")

        for i, section in enumerate(sections):
            # 纯标题不去AI痕迹
            if is_markdown_title(section):
                result.append(ProjectConfigAITraces(
                    series_num=i,
                    original=stream_handle_before_send(section),
                    modified=section
                ))
            else:
                left_list.append(ProjectConfigAITraces(
                    series_num=i,
                    original=stream_handle_before_send(section),
                    modified=""
                ))

        def chunk_list(lst: List[ProjectConfigAITraces], chunk_size=settings.AI_TRACES_SAME_MOMENT_COUNT):
            return [lst[i:i+chunk_size] for i in range(0, len(lst), chunk_size)]
        # 将还没幻觉处理的文本切成二位数组
        wrapper_list = chunk_list(left_list)

        def complete_callback(token_data: dict):
            token_consume["input"] += token_data.get("input")
            token_consume["output"] += token_data.get("output")
        
        async def call_fn(data: ProjectConfigAITraces):
            nonlocal llm_exception
            messages = [
                # {"role": "system", "content": REMOVE_AI_TRACES_SYSTEM_PROMPT},
                {"role": "user", "content": REMOVE_AI_TRACES_PROMPT.format(
                    content=data.original)}
            ]
            try:
                # modified_content = section
                modified_content = await call_llm(
                    messages=messages,
                    model=model,
                    apiKey=api_key,
                    apiUrl=api_url,
                    stream=False,
                    flag="去AI痕迹",
                    is_thinking=model_config.is_thinking,
                    complete_callback=complete_callback
                )
                data.modified = stream_handle_before_send(modified_content)
                logger.info(
                    f"原始内容和去痕迹后的内容是否一致：{data.modified == data.original}")
            except Exception as e:
                llm_exception = True
                logger.error(f"段落{data.series_num}报错：{str(e)}")
                data.modified = "暂不支持"
                # data.modified = stream_handle_before_send(data.original)
            result.append(data)
        for item in wrapper_list:
            tasks = [call_fn(item1) for item1 in item]
            await asyncio.gather(*tasks)
            # logger.info(f"AI去痕迹的进度：{i}/{len(sections)}")
        # 按序号排序
        final_list = sorted(result, key=lambda x: x.series_num, reverse=False)
        logger.info(f"文件 {file_path} 处理完成，共处理 {len(final_list)} 个段落")

        # 保存详细报告（JSON格式）
        result_content = json.dumps([item.model_dump() for item in final_list])
        project_folder = f"llm_file/{co_ai_trace_record.id}"
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        timestamp = int(datetime.now().timestamp() * 1000)
        detail_file_name = f"remove_ai_trace_{timestamp}.txt"
        detail_relative_path = f"{project_folder}/{detail_file_name}"

        upload_file.write(
            path=detail_relative_path,
            content=result_content
        )

        # 保存处理后的内容为MD文件
        processed_content = "\n\n".join(
            [item.modified for item in final_list]) if final_list else ""
        processed_file_name = f"processed_content_{timestamp}.md"
        processed_relative_path = f"{project_folder}/{processed_file_name}"

        upload_file.write(
            path=processed_relative_path,
            content=processed_content
        )

        logger.info(
            f"AI去痕处理完成，详细报告保存至: {detail_relative_path}, 处理后内容保存至: {processed_relative_path}")

        try:
            await co_ai_trace_record.update_from_dict({
                "tokens_consumed": total_tokens,
                "processed_sections": len(final_list) if final_list else 0,
                "processed_content": processed_content,
                "processed_path": processed_relative_path,  # 新增：设置处理后内容的文件路径
                "original_content": report_text,  # 设置原始内容
                "char_count": len(report_text),  # 设置字符数量
                "status": CoAiTracesStatus.SUCCESS.value,
                "processing_end_time": datetime.now(),
                "detail_report": detail_relative_path
            })
            await co_ai_trace_record.save()
            logger.info(
                f"AI去痕记录更新成功，processed_path: {processed_relative_path}")
            # 判断时候有模型调用错误，属于业务失败、任务成功
            if llm_exception:
                await co_ai_trace_record.update_from_dict({
                    "error_message": "大模型调用异常，业务失败",
                })
                await co_ai_trace_record.save()
                logger.error(f"统计字段-AI去痕-业务失败:{co_ai_trace_record.id}")
            # 成功回调
            success_callback and await success_callback()
        except Exception as token_update_error:
            logger.warning(f"更新token消耗失败: {str(token_update_error)}")
            # 失败回调
            error_callback and await error_callback()
    except Exception as e:
        error_msg = f"AI去痕迹失败：{str(e)}"
        logger.error(error_msg)

        # 更新AI去痕记录状态为失败
        if co_ai_trace_record:
            try:
                await co_ai_trace_record.update_from_dict({
                    "status": CoAiTracesStatus.FAILED.value,
                    "error_message": str(e),
                    "processing_end_time": datetime.now()
                })
                await co_ai_trace_record.save()
            except Exception as update_error:
                logger.warning(f"更新AI去痕记录状态失败: {str(update_error)}")
        # 失败回调
        error_callback and await error_callback()
    finally:
        upload_file.remove_local_file(file_path)
    await content_manager.clear_project(project_id=co_ai_trace_record.id)
    await llm_interaction_instance.end_interaction(scene=ManagerScene.AI_TRACES_REMOVE, id=co_ai_trace_record.id)
    endtime = datetime.now()
    logger.info(
        f"AI去痕迹结束时间: {endtime.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(endtime - starttime).total_seconds()}秒")
    logger.info(f"AI去痕迹消耗token：{token_consume}")

async def create_ai_traces_from_project(
    project_id: str,
    current_user: UserResponse
) -> CoAiTracesResponse:
    """
    根据项目ID创建AI去痕

    Args:
        project_id: 项目配置ID
        current_user: 当前用户

    Returns:
        CoAiTracesResponse: 创建的去痕记录
    """
    try:
        logger.info(f"开始为项目 {project_id} 创建幻觉审查记录")

        # 1. 获取项目配置详情
        project_config = await get_one_project_config(project_id)
        if not project_config:
            raise Exception("项目配置不存在")
        # 2. 检查项目是否属于当前用户或用户有权限访问
        if not await is_user_authed(
            resource_belong_user=project_config.user.id,
            operator=current_user.id
        ):
            raise Exception("无权访问此项目")
        # 3. 确定要审查的正文内容路径（优先级：手动修改的报告 > AI生成的报告 > 手动修改的大纲 > AI生成的大纲）
        content_path = project_config.manual_modified_report or project_config.ai_generated_report
        if not content_path:
            raise Exception("论文还没有生成")

        # 4. 文本内容审核 - 检查是否包含敏感词或违规内容
        is_safe, error_msg = await content_moderation_service.verify_file_content(
            file_path=content_path,
            description=f"项目 {project_id} 的文档"
        )
        
        if not is_safe:
            raise Exception(error_msg)

        # 5. 创建文件记录（使用项目内容的路径）
        file_record = await UploadFileModel.create(
            file_path=content_path,
            file_name=sanitize_filename(project_config.name) + '.txt',
            user_id=current_user.id
        )
        result = await create_ai_traces(
            current_user=current_user,
            file_id=file_record.id
        )

        logger.info(f"项目 {project_id} 的幻觉审查记录已创建: {result.id}")

        # 8. 返回创建的记录
        return await get_one_traces_record(result.id, current_user)

    except Exception as e:
        error_msg = f"创建幻觉审查失败: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


async def delete_ai_traces(
    traces_id: str,
    current_user: UserResponse
) -> bool:
    """
    删除AI去痕记录（软删除）

    Args:
        traces_id: AI去痕记录ID
        current_user: 当前用户

    Returns:
        bool: 删除成功返回True
    """
    try:
        # 查找记录
        traces_record = await CoAiTraces.filter(
            id=traces_id,
            is_deleted=False
        ).prefetch_related("user").first()

        if not traces_record:
            raise Exception("AI去痕记录不存在")

        # 检查用户权限
        if not await is_user_authed(traces_record.user.id, current_user.id):
            raise Exception(ErrorType.FORBIDDEN.value)

        # 软删除
        traces_record.is_deleted = True
        traces_record.deleted_at = datetime.now()
        await traces_record.save()

        logger.info(f"AI去痕记录 {traces_id} 已被用户 {current_user.id} 删除")
        return True

    except Exception as e:
        logger.error(f"删除AI去痕记录失败: {str(e)}")
        raise Exception(f"删除AI去痕记录失败: {str(e)}")


async def get_ai_traces_list(
    query: PageQuery,
    current_user: UserResponse,
    keyword: Optional[str] = None,
    status: Optional[str] = None
) -> PageInfo[CoAiTracesResponse]:
    """
    分页获取AI去痕记录列表（只查询近配置天数的记录）

    Args:
        query: 分页查询参数
        current_user: 当前用户
        keyword: 搜索关键词（可选，根据原始文件名模糊搜索）
        status: 状态筛选（可选）

    Returns:
        PageInfo[CoAiTracesResponse]: 分页后的AI去痕记录列表（近配置天数）
    """
    try:
        # 构建查询条件
        queryset = CoAiTraces.filter(is_deleted=False)
        
        # 根据用户权限过滤数据
        # 如果是超级管理员，可以查看所有数据；否则只能查看自己的数据
        if current_user.role and current_user.role.identifier not in [InsetRole.SUPER_ADMIN.value, InsetRole.ADMIN.value]:
            queryset = queryset.filter(user_id=current_user.id)
        elif current_user.role and current_user.role.identifier == InsetRole.ADMIN.value:
            queryset = queryset.filter(
                user__organization_id=current_user.organization.id)
        
        # 只查询近配置天数的记录
        filter_days_str = await system_config_service.get_config("history_record_filter_days")
        filter_days = int(filter_days_str) if filter_days_str and filter_days_str.isdigit() else 30
        thirty_days_ago = datetime.now() - timedelta(days=filter_days)
        queryset = queryset.filter(updated_at__gte=thirty_days_ago)
        # 关键词搜索（根据原始文件名模糊搜索）

        if keyword:
            queryset = queryset.filter(original_filename__icontains=keyword)

        # 状态筛选
        if status:
            queryset = queryset.filter(status=status)

        # 获取总数
        total = await queryset.count()

        # 分页查询
        items = await queryset.prefetch_related(
            "upload_file",
            "user",
            "model_config"
        ).offset(
            (query.page - 1) * query.size
        ).limit(query.size).order_by("-updated_at")

        # 转换为响应格式
        result_items = []
        for item in items:
            result_items.append(CoAiTracesResponse.model_validate(
                {
                    "id": item.id,
                    "ai_report": item.processed_content,
                    "status": item.status,
                    "upload_file": item.upload_file,
                    "detail_list": [],  # 列表中不需要详细数据

                    # 原始文件信息
                    "original_filename": item.original_filename,
                    "file_size": item.file_size,
                    "file_type": item.file_type,
                    "char_count": item.char_count,

                    # 处理结果
                    "processed_sections": item.processed_sections,
                    "original_content": item.original_content,
                    "processed_content": item.processed_content,
                    "processed_path": item.processed_path,  # 重要：包含这个字段
                    "detail_report": item.detail_report,

                    # 处理状态和时间
                    "error_message": item.error_message,
                    "processing_start_time": item.processing_start_time,
                    "processing_end_time": item.processing_end_time,
                    "tokens_consumed": item.tokens_consumed,

                    # 时间戳
                    "created_at": item.created_at,
                    "updated_at": item.updated_at,
                    "is_deleted": item.is_deleted,
                    "deleted_at": item.deleted_at,
                },
                from_attributes=True
            ))

        return PageInfo(
            total=total,
            page=query.page,
            size=query.size,
            items=result_items
        )

    except Exception as e:
        logger.error(f"获取AI去痕记录列表失败: {str(e)}")
        raise Exception(f"获取AI去痕记录列表失败: {str(e)}")
