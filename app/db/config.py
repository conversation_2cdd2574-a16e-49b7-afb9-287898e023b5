from tortoise import Tortoise
from app.core.config import settings
import os
from app.core.logging import get_logger

logger = get_logger(__name__)

# 获取数据库连接URL
DATABASE_URL = os.environ.get("DATABASE_URL")

logger.info("DatabaseConfig - 初始化数据库连接配置")
if not DATABASE_URL:
    from dotenv import load_dotenv
    from pathlib import Path

    # 找到当前文件夹的 .env 文件（可以根据你的目录结构调整）
    env_path = Path(__file__).parent.parent / ".env"
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
        DATABASE_URL = os.environ.get("DATABASE_URL")
    else:
        raise FileNotFoundError(f".env 文件不存在，且没有检测到 DATABASE_URL 环境变量，请检查！")

# 最终确认 DATABASE_URL 必须有
if not DATABASE_URL:
    raise ValueError("DATABASE_URL 环境变量未设置，无法启动 ORM！")

# 在日志中隐藏敏感信息
def mask_db_url(db_url):
    """隐藏数据库URL中的敏感信息"""
    import re
    if not db_url:
        return db_url
    # 使用正则表达式替换密码部分
    return re.sub(r'(://\w+:)([^@]+)(@)', r'\1******\3', db_url)

logger.info(f"DatabaseConfig - 数据库连接URL: {mask_db_url(DATABASE_URL)}")

# 解密数据库连接URL (如果需要)
def get_decrypted_db_url(db_url):
    """获取解密后的数据库连接URL"""
    # 在函数内部导入，避免循环导入
    from app.utils.db_crypto import decrypt_db_url, is_encrypted_db_url
    if is_encrypted_db_url(db_url):
        logger.info("DatabaseConfig - 数据库连接URL已加密，正在解密...")
        return decrypt_db_url(db_url)
    logger.info("DatabaseConfig - 数据库连接URL未加密，使用原始连接字符串")
    return db_url

# 获取解密后的数据库连接URL
DECRYPTED_DATABASE_URL = get_decrypted_db_url(DATABASE_URL)

TORTOISE_ORM = {
    "connections": {"default": DECRYPTED_DATABASE_URL},
    "apps": {
        "models": {
            "models": ["aerich.models", "app.models"],
            "default_connection": "default",
        },
    },
}


async def init_db():
    """初始化数据库连接"""
    # 使用解密后的数据库URL
    db_url = get_decrypted_db_url(settings.DATABASE_URL)
    await Tortoise.init(
        db_url=db_url,
        modules={"models": ["app.models"]},
    )
    # 生成表结构（仅在开发环境使用）
    await Tortoise.generate_schemas()


async def close_db():
    """关闭数据库连接"""
    await Tortoise.close_connections() 